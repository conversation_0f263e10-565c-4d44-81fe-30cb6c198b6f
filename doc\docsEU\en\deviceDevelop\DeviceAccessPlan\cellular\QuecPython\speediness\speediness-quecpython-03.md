# Presentation

## **1. Device Online**

At this time, the module equipped with the program has started to run, and you can view the program on __REPL__ page. REPL is used to communicate with the device at the PC end and issue commands to make the device work according to the developer's idea. At the same time, the device can return information to the PC, and the user can see the internal operation of the device in the interaction interface. When __event: 2 0__ is displayed on the interaction interface, it indicates the module has connected with Developer Center. The interaction interface of QPYcom is shown as follows.


<a data-fancybox title="img" href="/en/deviceDevelop/develop/speediness/resource/QuecPython/Speediness-QuecPython-09.png">![img](/en/deviceDevelop/develop/speediness/resource/QuecPython/Speediness-QuecPython-09.png)</a>


At this time, you can see the device is online from Developer Center. The presentation of Developer Center interface is shown below:


<a data-fancybox title="img" href="/en/deviceDevelop/develop/speediness/resource/QuecPython/Speediness-QuecPython-15.png">![img](/en/deviceDevelop/develop/speediness/resource/QuecPython/Speediness-QuecPython-15.png)</a>

## __2. Debug Device Online__

 <span style="color:#999AAA">Note: Online debugging can be used to debug device features, properties and services. To ensure successful debugging, it is important to verify that the device is online and properly connected.</span>

 Draw up TSL features according to the actual project requirements of the smart aromatherapy diffuser. The default TSL model is presented in the table below.

|Feature ID| Feature Type |              Feature Name              | Data Format | Read/Write Type |                                  Unit                                  |
|:----------:| :----------: | :------------------------------------: | :---------: | :-------------: | :--------------------------------------------------------------------: |
|1|   Property   | The remaining content of essential oil |    FLOAT    |    Read-Only    |                                   ML                                   |
|2|   Property   |     Frequency of regular reporting     |     INT     |   Read-Write    |                                 Second                                 |
|3|   Property   |             On/Off status              |    BOOL     |    Read-Only    |                       True: On；<br>False: Off；                       |
|4|    Event     |     Alert to lack of essential oil     |    BOOL     |       --        |               True: Alert； <br> False: Alert canceled；               |
|5|   Property   |     mode     |     ENUM     |   Read-Write    |    1: Working Mode 1;<br> 2: Working Mode 2;<br>3: Working Mode 3;                          |
|6|   Service    |            Set working mode            |         |       --        | Feature ID: 5|

__1) View Data Logs__

The communication logs indicate that the device reports the remaining content of essential oil to Developer Center every 30 seconds.

<a data-fancybox title="img" href="/en/deviceDevelop/develop/speediness/resource/QuecPython/Speediness-QuecPython-10.png">![img](/en/deviceDevelop/develop/speediness/resource/QuecPython/Speediness-QuecPython-10.png)</a>

__2) Read the Current Property Value__

Click a single property to __Read__, and then Developer Center will send the querying command to the device. You can also read Communication Logs in real time to check the situation of data sent and reported by the device.

<a data-fancybox title="img" href="/en/deviceDevelop/develop/speediness/resource/QuecPython/Speediness-QuecPython-11.png">![img](/en/deviceDevelop/develop/speediness/resource/QuecPython/Speediness-QuecPython-11.png)</a>

__3) Call Service__

According to __Device Debug__ > __Service Calls__ defined by TSL features, you can simulate the condition of remotely issuing commands. On the "Device Debug" page, select "__Set Working Mode__", set the parameter to __Working Mode 2__ and click __"Send Command"__, then you can see that the device has received the service issued by Developer Center.

<a data-fancybox title="img" href="/en/deviceDevelop/develop/speediness/resource/QuecPython/Speediness-QuecPython-12.png">
  <img src="/en/deviceDevelop/develop/speediness/resource/QuecPython/Speediness-QuecPython-12.png" width="600" alt="img">
</a>

At this time, the device has received the callback event and service data. The interaction interface of QPYcom is shown as follows.


<a data-fancybox title="img" href="/en/deviceDevelop/develop/speediness/resource/QuecPython/Speediness-QuecPython-13.png">![img](/en/deviceDevelop/develop/speediness/resource/QuecPython/Speediness-QuecPython-13.png)</a>

__4) Alert Event Logs__

You can use the **Event Logs** feature to monitor alert events generated by the device. The feature allows you to view how the device automatically reports alert events to Developer Center in real time.

<a data-fancybox title="img" href="/en/deviceDevelop/develop/speediness/resource/QuecPython/Speediness-QuecPython-14.png">![img](/en/deviceDevelop/develop/speediness/resource/QuecPython/Speediness-QuecPython-14.png)</a>


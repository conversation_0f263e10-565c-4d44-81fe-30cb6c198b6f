---
title: "消息管理"
description: "消息管理相关文档"
---

# 消息管理

本文介绍了如何对消息进行管理, 包括设备告警和事件消息, 自动化和场景执行结果消息

## 功能概述

```objc
#import <QuecUserKit/QuecUserKit.h>
/// 初始化
[QuecUserMessageService sharedInstance]
```

### 删除消息

**接口说明**

删除用户消息

```objc
- (void)deleteMessageByMsgId:(NSString *)msgId
                    language:(NSString *)language
                     success:(QuecVoidBlock)success
                     failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|
| --- | --- | --- |
| msgId |	是|阅读的消息ID列表 多个ID使用英文逗号分隔	|
| language |	否|语音	|
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	|


**示例代码**

```objc
[[QuecUserMessageService sharedInstance] deleteMessageByMsgId:@"messageId" language:@"cn" success:^{
        /// Next Action
    } failure:^(NSError *error) {
        NSLog(@"check error: %@", error);
    }];
```

### 阅读消息

**接口说明**

单个及批量阅读消息(根据标签阅读调用下面新Api)

```objc
- (void)readMessageByMsgIdList:(NSString *)msgIdList
                        msgType:(int)msgType
                        success:(void(^)(QuecReadMsgModel *model))success
                        failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|
| --- | --- | --- |
| msgIdList |	否|阅读的消息ID列表 多个ID使用英文逗号分隔,如果不传，会阅读所有消息| 
| msgType |	否|1：设备通知，2：设备告警，3：设备故障，4：系统消息；接收的消息类型任意组合，多个类型使用英文逗号分隔	| 
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	|

**QuecReadMsgModel属性定义**

|参数|	类型|说明|
| --- | --- | --- |
| successList | NSArray<QuecReadMsgListModel *> |成功列表|
| failureList | NSArray<QuecReadMsgListModel *>   |失败列表|

**QuecReadMsgListModel属性定义**
|参数|	类型|说明|
| --- | --- | --- |
| msgID | NSString |消息ID|
| failureMsg | NSString   |阅读失败原因(仅失败列表有此字段)|

**示例代码**

```objc
[QuecUserMessageService.sharedInstance readMessageByMsgIdList:@"MsgIdList" msgType:1 success:^(QuecReadMsgModel * _Nonnull model) {
        /// Next Action
    } failure:^(NSError *error) {
        NSLog(@"check error: %@", error);
    }];
```

### 查询消息列表

**接口说明**

查询用户的消息列表(根据消息类型查询，标签查询调用下方新Api)

```objc
- (void)getUserMessageListByPageNumber:(NSInteger)pageNumber
                              pageSize:(NSInteger)pageSize
                               msgType:(NSInteger)msgType
                                isRead:(BOOL)isRead
                             deviceKey:(NSString *)deviceKey
                            productKey:(NSString *)productKey
                               success:(void(^)(NSArray<QuecUserMessageListModel *> *list, NSInteger total))success
                               failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|
| --- | --- | --- |
| pageNumber |	否|查询的列表页，默认为 1	| 
| pageSize |	否|查询的页大小，默认 10	| 
| msgType |	否|1：设备通知，2：设备告警，3：设备故障，4：系统消息；接收的消息类型任意组合，多个类型使用英文逗号分隔	| 
| isRead |	否|是否已读	| 
| deviceKey |	否|device key	| 
| productKey |	否|product key	| 
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	|

**QuecUserMessageListModel属性定义**

|参数|	类型|说明|
| --- | --- | --- |
| msgId | NSString |消息ID|
| msgType | NSInteger   |/// 消息类型  1-设备通知  2-设备告警  3-设备故障  4-系统消息|
| isRead | BOOL |是否已读|
| title | NSString |标题|
| content | NSString |内容|
| pk | NSString |Product Key|
| dk | NSString |Device Key|
| addTime | NSUInteger |添加时间|
| readTime | NSUInteger |阅读时间|
| shareCode | NSString |分享code|
| sceneIcon | NSString |场景图标|
| sceneName | NSString |场景名称|
| sceneExecuteTime | NSString |场景执行时间|
| executeId | NSString |场景执行Id|
| logId | NSString |自动化日志id|
| automationId | NSString |自动化Id|
| firstLabel | NSString |消息一级分类|
| secondaryLabel | NSString |消息二级分类|

**示例代码**

```objc
[QuecUserMessageService.sharedInstance getUserMessageListByPageNumber:1 pageSize:10 msgType:1 isRead:NO deviceKey:@"deviceKey" productKey:@"productKey" success:^(NSArray<QuecUserMessageListModel *> * _Nonnull list, NSInteger total) {
        /// Next Action
    } failure:^(NSError *error) {
        NSLog(@"check error: %@", error);
    }];
```

### 上传pushToken

**接口说明**

上传pushToken，需要先获取 阿里云 deviceid，海外需要google fcmtoken

```objc
- (void)addPushToken:(NSString *)pushToken
              success:(QuecVoidBlock)success
              failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|
| --- | --- | --- |
| pushToken | 是       | pushToken aliyun‘s deviceid , google's fcmtoken |
| success   | 否       | 接口请求成功回调                                |
| failure   | 否       | 接口请求失败回调                                |

**示例代码**

```objc
[QuecUserMessageService.sharedInstance addPushToken:@"pushToken" success:^{
            /// Next Action
        } failure:^(NSError *error) {
            NSLog(@"check error: %@", error);
        }];
```

### 删除pushToken

**接口说明**

删除已绑定pushToken，需要获取缓存的 阿里云 deviceid，海外需要google fcmtoken

```objc
- (void)deletePushToken:(NSString *)pushToken
                 success:(QuecVoidBlock)success
                 failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|
| --- | --- | --- |
| pushToken | 是       | pushToken aliyun‘s deviceid , google's fcmtoken |
| success   | 否       | 接口请求成功回调                                |
| failure   | 否       | 接口请求失败回调                                |


**示例代码**

```objc
[QuecUserMessageService.sharedInstance deletePushToken:@"pushToken" success:^{
            /// Next Action
        } failure:^(NSError *error) {
            NSLog(@"check error: %@", error);
        }];
```

### 查询消息列表

**接口说明**

通过标签查询消息列表

```objc
- (void)getUserMessageListByPageNumber:(NSInteger)pageNumber
                              pageSize:(NSInteger)pageSize
                        firstLabelList:(NSString *)firstLabelList
                    secondaryLabelList:(NSString *)secondaryLabelList
                                isRead:(BOOL)isRead
                             deviceKey:(NSString * _Nullable)deviceKey
                            productKey:(NSString * _Nullable)productKey
                               success:(void (^)(NSArray<QuecUserMessageListModel *> *, NSInteger))success
                               failure:(void (^)(NSError *))failure;
```

**参数说明**

|参数|	是否必传|说明|
| --- | --- | --- |
| pageNumber         | 否       | 查询的列表页，默认为 1                                       |
| pageSize           | 否       | 查询的页大小，默认 10                                        |
| firstLabelList     | 是       | 1:系统 2:智能 3:设备，多个使用","拼接                        |
| secondaryLabelList | 是       | 1:隐私 2:服务 3:自动化 4:场景 5:设备通知 6:设备告警 7:设备故障 8:ota，多个使用","拼接 |
| isRead             | 是       | 是否已读                                                     |
| deviceKey          | 是       | 设备dk                                                       |
| productKey         | 是       | 产品pk                                                       |
| success            | 否       | 接口请求成功回调                                             |
| failure            | 否       | 接口请求失败回调                                             |

**QuecUserMessageListModel属性定义**

|参数|	类型|说明|
| --- | --- | --- |
| msgId | NSString |消息ID|
| msgType | NSInteger   |/// 消息类型  1-设备通知  2-设备告警  3-设备故障  4-系统消息|
| isRead | BOOL |是否已读|
| title | NSString |标题|
| content | NSString |内容|
| pk | NSString |Product Key|
| dk | NSString |Device Key|
| addTime | NSUInteger |添加时间|
| readTime | NSUInteger |阅读时间|
| shareCode | NSString |分享code|
| sceneIcon | NSString |场景图标|
| sceneName | NSString |场景名称|
| sceneExecuteTime | NSString |场景执行时间|
| executeId | NSString |场景执行Id|
| logId | NSString |自动化日志id|
| automationId | NSString |自动化Id|
| firstLabel | NSString |消息一级分类|
| secondaryLabel | NSString |消息二级分类|

**示例代码**

```objc
[QuecUserMessageService.sharedInstance getUserMessageListByPageNumber:1 pageSize:10 firstLabelList:@"firstLabelList" secondaryLabelList:@"secondaryLabelList" isRead:NO deviceKey:@"dk" productKey:@"pk" success:^(NSArray<QuecUserMessageListModel *> * _Nonnull, NSInteger) {
        /// Next Action
    } failure:^(NSError * _Nonnull) {
        NSLog(@"check error: %@", error);
    }]
```

### 阅读消息

**接口说明**

通过标签标记阅读消息

```objc
- (void)readMessageByMsgIdList:(NSString * _Nullable)msgIdList
                firstLabelList:(NSString * _Nullable)firstLabelList
            secondaryLabelList:(NSString * _Nullable)secondaryLabelList
                       success:(void (^)(QuecReadMsgModel *model))success
                       failure:(void (^)(NSError *))failure;
```

**参数说明**

|参数|	是否必传|说明|
| --- | --- | --- |
| msgIdList          | 是       | 阅读的消息ID列表 多个ID使用英文逗号分隔,如果不传，会阅读所有消息 |
| firstLabelList     | 是       | 1:系统 2:智能 3:设备，多个使用","拼接                        |
| secondaryLabelList | 是       | 1:隐私 2:服务 3:自动化 4:场景 5:设备通知 6:设备告警 7:设备故障 8:ota，多个使用","拼接 |
| success            | 否       | 接口请求成功回调                                             |
| failure            | 否       | 接口请求失败回调                                             |

**QuecReadMsgModel属性定义**

|参数|	类型|说明|
| --- | --- | --- |
| successList | NSArray<QuecReadMsgListModel *> |成功列表|
| failureList | NSArray<QuecReadMsgListModel *>   |失败列表|

**QuecReadMsgListModel属性定义**
|参数|	类型|说明|
| --- | --- | --- |
| msgID | NSString |消息ID|
| failureMsg | NSString   |阅读失败原因(仅失败列表有此字段)|


**示例代码**

```objc
[QuecUserMessageService.sharedInstance readMessageByMsgIdList:@"messageId" firstLabelList:@"firstLabelList" secondaryLabelList:@"" success:^(QuecReadMsgModel * _Nonnull msgModel) {
        /// Next Action
    } failure:^(NSError * _Nonnull error) {
        NSLog(@"check error: %@", error);
    }];
```

### 获取未读消息数

**接口说明**

获取未读消息数，可用于标记用户消息阅读状态

```objc
- (void)getMsgStatsWithSuccess:(void(^)(QuecUserMessageStatsModel *statsModel))success failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|
| --- | --- | --- |
| success | 否       | 接口请求成功回调 |
| failure | 否       | 接口请求失败回调 |

**QuecUserMessageStatsModel属性定义**

|参数|	类型|说明|
| --- | --- | --- |
| smart | QuecUserMessageUnReadModel |智能相关消息|
| system | QuecUserMessageUnReadModel   |系统相关消息|
| device | QuecUserMessageUnReadModel   |设备相关消息|

**QuecUserMessageUnReadModel属性定义**

|参数|	类型|说明|
| --- | --- | --- |
| read | NSInteger |已读消息数|
| unRead | NSInteger   |未读消息数|

**示例代码**

```objc
[QuecUserMessageService.sharedInstance getMsgStatsWithSuccess:^(QuecUserMessageStatsModel * _Nonnull statsModel) {
        /// Next Action
    } failure:^(NSError *error) {
        NSLog(@"check error: %@", error);
    }];
```

### 用户接收推送的消息类型

**接口说明**

获取用户接收推送的消息类型

```objc
- (void)getMsgPushSettingWithSuccess:(void(^)(QuecPushSettingModel *data))success failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|
| --- | --- | --- |
| success | 否       | 接口请求成功回调 |
| failure | 否       | 接口请求失败回调 |

**QuecPushSettingModel属性定义**

|参数|	类型|说明|
| --- | --- | --- |
| deviceNotify | BOOL |设备推送|
| smartNotify | BOOL   |智能推送|
| systemNotify | BOOL |系统推送|
| device | QuecPushSettingItemModel   |设备推送子属性设置情况|
| smart | QuecPushSettingItemModel |智能推送子属性设置情况|
| system | QuecPushSettingItemModel   |系统推送子属性设置情况|

**QuecPushSettingItemModel属性定义**

|参数|	类型|说明|
| --- | --- | --- |
| app | BOOL |系统推送是否开启|
| sms | BOOL   |短信推送是否开启|
| voice | BOOL |语音推送是否开启|


**示例代码**

```objc
[[QuecUserMessageService sharedInstance] getMsgPushSettingWithSuccess:^(QuecPushSettingModel *pushSettingModel) {
        /// Next Action
    } failure:^(NSError *error) {
        NSLog(@"check error: %@", error);
    }];
```

### 设置用户接收推送的消息类型

**接口说明**

用户自定义设置接收推送的消息类型

```objc
- (void)setMsgPushSettingWithConfig:(QuecPushSettingModel *)config success:(QuecVoidBlock)success failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|
| --- | --- | --- |
| config | 是       | QuecPushSettingModel属性定义同上【用户接收推送的消息类型】 |
| success | 否       | 接口请求成功回调 |
| failure | 否       | 接口请求失败回调 |

**示例代码**

```objc
[[QuecUserMessageService sharedInstance] setMsgPushSettingWithConfig:model success:^{
        /// Next Action
    } failure:^(NSError *error) {
        NSLog(@"check error: %@", error);
    }];
```


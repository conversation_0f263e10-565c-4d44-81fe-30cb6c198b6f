# Wi-Fi Device Firmware Download

## __1) Download Burning Tool__

Enter <a href="https://core.acceleronix.io/download?menuCode=MODULE_DEVL" target="_blank">**Download Center**</a>, select the specified module model and enter the corresponding download page, find the burning tool and download it.

## __2) Connect PC to Device__

Connect your PC to the M_UART_USB  serial port of the module with a MicroUSB cable.

## __3) Download Firmware__

Open __Beken Writer__ and select the firmware to be downloaded. See the figure below for the download procedure.
<a data-fancybox title="img" href="/en/deviceDevelop/KnowledgeBase/step31.png">![img](/en/deviceDevelop/KnowledgeBase/step31.png)</a>

① Select the "__Port COM__" used to connect the module.<br>
② Click "__Browse...__" to select the target .bin file.<br>
③ Click "__Program__" and reset the module by __Reset PIN__<br>

Note: Please ensure the port is not occupied during the firmware download.
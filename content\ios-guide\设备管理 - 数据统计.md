---
title: "设备数据统计"
description: "设备数据统计相关文档"
---

# 设备数据统计

## 功能概述

设备数据统计管理, 主要获取设备相关的统计信息

```objc
#import <QuecDeviceKit/QuecDeviceKit.h>
/// 初始化
[QuecDeviceStatisticsService sharedInstance]
```

## 数据统计

### 设备历史轨迹

**接口说明**

获取设备历史轨迹

```objc
- (void)getLocationHistoryWithProductKey:(NSString *)productKey
                                 deviceKey:(NSString *)deviceKey
                           startTimestamp:(NSInteger)startTimestamp
                             endTimestamp:(NSInteger)endTimestamp
                                gatewayDk:(NSString *)gatewayDk
                                gatewayPk:(NSString *)gatewayPk
                              locateTypes:(NSString *)locateTypes
                                  success:(void(^)(NSArray<QuecLocationHistoryModel *> *list))success
                                  failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- |  
| productKey | 是 | 产品key |
| deviceKey | 是 | 设备key |
| startTimestamp | 是 | 开始时间（毫秒时间戳） |
| endTimestamp | 是 | 结束时间（毫秒时间戳） |
| gatewayDk | 否 | 网关设备的 Device Key |
| gatewayPk | 否 | 网关设备的 Product Key |
| locateTypes | 否 | 定位类型（默认查询所有类型的定位），查询多种定位时使用英文逗号分隔 GNSS-全球导航卫星系统 GPS-美国导航定位系统 GL-俄罗斯格洛纳导航定位系统 GA-欧盟伽利略卫星导航系统 BD/PQ-中国导航定位系统 LBS-基于通信运营商的基站定位系统 |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**QuecLocationHistoryModel属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| bdLat  | NSNumber | 设备纬度。BD09 |
| bdLng  | NSNumber | 设备经度。BD09 |
| deviceKey  | NSString | 设备dk |
| gcjLat  | NSNumber | 设备纬度。GCJ02 |
| gcjLng  | NSNumber | 设备经度。GCJ02 |
| hdop  | NSNumber | 水平精度因子。0.5-99.99 |
| locateId  | NSString | 轨迹ID |
| locateType  | NSInteger | 定位方式（0：GNSS 1：LBS 2：手动标点） |
| locationTime  | NSString | 定位时间 |
| productKey  | NSString | 设备pk |
| satellites  | NSInteger | 当前卫星数 |
| tsLocateTime  | NSInteger | 最新定位时间（时间戳） |
| wgsLat  | NSNumber | 设备纬度。WGS84 |
| wgsLng  | NSNumber | 设备经度。WGS84 |

**示例代码**

```objc
[QuecDeviceStatisticsService.sharedInstance getLocationHistoryWithProductKey:@"productKey"
                                                                       deviceKey:@"deviceKey"
                                                                  startTimestamp:startTimestamp
                                                                    endTimestamp:endTimestamp
                                                                       gatewayDk:@"gatewayDk"
                                                                       gatewayPk:@"gatewayPk"
                                                                     locateTypes:locateTypes
                                                                         success:^(NSArray<QuecLocationHistoryModel *> * _Nonnull list) {
/// Next Action
} failure:^(NSError *error) {
NSLog(@"check error: %@", error);
}];
```

### 设备属性图表列表

**接口说明**

获取设备属性图表列表

```objc
- (void)getPropertyChartListWithProductKey:(NSString *)productKey
                                   deviceKey:(NSString *)deviceKey
                             startTimestamp:(NSInteger)startTimestamp
                               endTimestamp:(NSInteger)endTimestamp
                              attributeCode:(NSString *)attributeCode
                                  gatewayDk:(NSString *)gatewayDk
                                  gatewayPk:(NSString *)gatewayPk
                                  countType:(NSInteger)countType
                            timeGranularity:(NSInteger)timeGranularity
                                    success:(void(^)(NSArray<QuecPropertyChartListModel *> *dataArray))success
                                    failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- |  
| productKey | 是 | 产品key |
| deviceKey | 是 | 设备key |
| startTimestamp | 是 | 开始时间（毫秒时间戳） |
| endTimestamp | 是 | 结束时间（毫秒时间戳） |
| attributeCode | 否 | 物模型属性标识符，查询多个属性时使用英文逗号分隔 |
| gatewayDk | 否 | 网关设备的 Device Key |
| gatewayPk | 否 | 网关设备的 Product Key |
| countType | 否 | 聚合类型（默认3）：1-最大值 2-最小值 3-平均值 4-差值 |
| timeGranularity | 否 | 统计时间粒度（默认2）：1-月 2-日 3-小时 4-分钟 5-秒 |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**QuecPropertyChartListModel属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| propertyCode  | NSString | 物模型code |
| xaxisData  | NSArray<NSString  *> | 记录时间 |
| xaxisDataTs  | NSArray<NSNumber *> | 时间戳 |
| yaxisData  | NSArray | 属性内容 |

**示例代码**

```objc
[QuecDeviceStatisticsService.sharedInstance getPropertyChartListWithProductKey:@"productKey"
                                                                         deviceKey:@"deviceKey"
                                                                    startTimestamp:startTimestamp
                                                                      endTimestamp:endTimestamp
                                                                     attributeCode:@"code1,code2,code3"
                                                                         gatewayDk:@"gatewayDk"
                                                                         gatewayPk:@"gatewayPk"
                                                                         countType:countType
                                                                   timeGranularity:timeGranularity
                                                                           success:^(NSArray<QuecPropertyChartListModel *> * _Nonnull dataArray) {
    /// Next Action
} failure:^(NSError *error) {
NSLog(@"check error: %@", error);
}];
```

### 设备属性环比统计数据

**接口说明**

获取设备属性环比统计数据

```objc
- (void)getPropertyStatisticsPathWithProductKey:(NSString *)productKey
                                        deviceKey:(NSString *)deviceKey
                                 currentTimestamp:(NSInteger)currentTimestamp
                                    attributeCode:(NSString *)attributeCode
                                        gatewayDk:(NSString *)gatewayDk
                                        gatewayPk:(NSString *)gatewayPk
                                        countType:(NSInteger)countType
                                timeGranularities:(NSString *)timeGranularities
                                          success:(void(^)(NSArray<QuecPropertyStatisticsModel *> *dataArray))success
                                          failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- |  
| productKey | 是 | 产品key |
| deviceKey | 是 | 设备key |
| currentTimestamp | 是 | 当前时间（毫秒时间戳） |
| attributeCode | 否 | 物模型属性标识符，查询多个属性时使用英文逗号分隔 |
| gatewayDk | 否 | 网关设备的 Device Key |
| gatewayPk | 否 | 网关设备的 Product Key |
| countType | 否 | 聚合类型（默认3）：1-最大值 2-最小值 3-平均值 4-差值 |
| timeGranularities | 否 | 统计时间粒度，查询多个粒度时使用英文逗号分隔（默认1）：1-日 2-周 3-月 4-年 |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**QuecPropertyStatisticsModel属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| statValue  | NSArray | 当期环比值 |
| timeGranularity  | NSInteger | 时间单位 【1-日，2-周，3-月，4-年】 |

**示例代码**

```objc
[QuecDeviceStatisticsService.sharedInstance getPropertyStatisticsPathWithProductKey:@"productKey"
                                                                              deviceKey:@"deviceKey"
                                                                       currentTimestamp:currentTimestamp
                                                                          attributeCode:@"code1,code2,code3"
                                                                              gatewayDk:@"gatewayDk"
                                                                              gatewayPk:@"gatewayPk"
                                                                              countType:countType
                                                                      timeGranularities:timeGranularities
                                                                                success:^(NSArray<QuecPropertyStatisticsModel *> * _Nonnull dataArray) {
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 设备属性数据列表

**接口说明**

获取设备属性数据列表

```objc
- (void)getPropertyDataListWithProductKey:(NSString *)productKey
                                  deviceKey:(NSString *)deviceKey
                             startTimestamp:(NSInteger)startTimestamp
                               endTimestamp:(NSInteger)endTimestamp
                              attributeCode:(NSString *)attributeCode
                                  gatewayDk:(NSString *)gatewayDk
                                  gatewayPk:(NSString *)gatewayPk
                                 pageNumber:(NSInteger)pageNumber
                                   pageSize:(NSInteger)pageSize
                                    success:(void(^)(NSArray<QuecPropertyDataListModel *> *list, NSInteger total))success
                                    failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- |  
| productKey | 是 | 产品key |
| deviceKey | 是 | 设备key |
| startTimestamp | 是 | 开始时间（毫秒时间戳） |
| endTimestamp | 是 | 结束时间（毫秒时间戳） |
| attributeCode | 否 | 物模型属性标识符，查询多个属性时使用英文逗号分隔 |
| gatewayDk | 否 | 网关设备的 Device Key |
| gatewayPk | 否 | 网关设备的 Product Key |
| pageNumber | 否 | 当前页，默认为第 1 页 |
| pageSize | 否 | 页大小，默认为 10 条 |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	|  

**示例代码**

```objc
[QuecDeviceStatisticsService.sharedInstance getPropertyDataListWithProductKey:@"productKey"
                                                                        deviceKey:@"deviceKey
                                                                   startTimestamp:startTimestamp 
                                                                     endTimestamp:endTimestamp
                                                                    attributeCode:@"code1,code2,code3"
                                                                        gatewayDk:@"gatewayDk"
                                                                        gatewayPk:@"gatewayPk"
                                                                       pageNumber:1
                                                                         pageSize:10 success:^(NSArray<QuecPropertyDataListModel *> * _Nonnull list, NSInteger total) {
        /// Next Action
    } failure:^(NSError *error) {
        NSLog(@"check error: %@", error);
    }];
```

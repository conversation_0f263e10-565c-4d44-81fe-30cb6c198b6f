# 设备与平台交互 相关事件

## __事件回调格式：__

> __+QIOTEVT: \<event_type\>,\<event_code\> [,\<data\>]__


## __事件类别解析：__

* __`<event_type>`__ ：整型。事件标识符。
  * __`1`__： 引导认证操作
  * __`2`__： 接入操作
  * __`3`__： 订阅操作
  * __`4`__： 发送数据操作
  * __`5`__： 接收数据操作
  * __`6`__： 注销操作
  * __`7`__： OTA 操作
  * __`8`__： 平台事件
  * __`9`__:  唤醒事件
* __`<event_code>`__： 整型。事件返回码。


## __事件返回码<event_type><event_code>：__

## __1：引导认证操作__

| 事件标识,事件码 |                             描述                             |                                                             处理方式                                                             |
| :-------------: | :----------------------------------------------------------: | :------------------------------------------------------------------------------------------------------------------------------: |
|     1,10200     |           设备认证成功                |                                                                                                                                  |
|     1,10300     |                           其他错误                           |                                          设备内部错误，请联系移远技术支持进行抓包分析                                          |
|     1,10404     |                     平台内部接口调用错误                     |                                          设备内部错误，请联系移远技术支持进行抓包分析                                          |
|     1,10421     |                      静态设备不允许认证                      |                                         使用静态设备秘钥配置指令，配置静态设备的设备秘钥                                         |
|     1,10422     |                    设备已认证（连接失败）                    |                                       设备DeviceSecret错误，需在开发者中心重置DeviceSecret                                       |
|     1,10423     |                 查询产品信息失败（连接失败）                 |                                            设备ProductKey配置错误，检查ProductKey配置                                            |
|     1,10424     |                 PAYLOAD 解密失败（连接失败）                 | 可能性一：设备ProductKey配置错误，检查ProductSecret配置 <br>可能性二：设备DeviceSecret解密失败，检查模组与平台的DeviceSecret |
|     1,10425     |                  签名验证未通过（连接失败）                  |                               设备DeviceKey非法，DeviceKey长度不符合(4-32位，数字、字母大小写均可)                               |
|     1,10427     |                  散列信息不合法（连接失败）                  |                                          设备内部错误，请联系移远技术支持进行抓包分析                                          |
|     1,10431     |                    DK 不合法（连接失败）                    |                               设备DeviceKey非法，DeviceKey长度不符合(4-32位，数字、字母大小写均可)                               |
|     1,10433     |                         Flag 不合法                         |                                          设备内部错误，请联系移远技术支持进行抓包分析                                          |
|     1,10434     | ClientID 与password不匹配（password中包涵ClientID 相关信息） |                                          设备内部错误，请联系移远技术支持进行抓包分析                                          |
|     1,10450     |                   设备内部错误（连接失败）                   |    可能性一：设备暂无网络，请检查设备天线是否有接与注网状态 <br>可能性二：设备内部错误，请联系移远技术支持进行抓包分析    |
|     1,10500     |               设备认证失败（系统发生未知异常）               |                                          设备内部错误，请联系移远技术支持进行抓包分析                                          |

## __2：接入操作__

| 事件标识,事件码 |                             描述                             |                                                         处理方式                                                         |
| :-------------: | :----------------------------------------------------------: | :----------------------------------------------------------------------------------------------------------------------: |
|     2,10200     |                           注册成功                           |                                                                                                                          |
|     2,10404     |                     平台内部接口调用错误                     |                                      设备内部错误，请联系移远技术支持进行抓包分析                                      |
|     2,10430     |                  设备密钥不正确（连接失败）                  |                                 设备DeviceSecret设置错误，需重置模组和平台的DeviceSecret                                 |
|     2,10431     |                  设备被禁用（连接失败）                  |                                 设备内部错误，请联系移远技术支持进行抓包分析                                 |
|     2,10433     |                         Flag 不合法                         |                                      设备内部错误，请联系移远技术支持进行抓包分析                                      |
|     2,10434     | ClientID 与password不匹配（password中包涵ClientID 相关信息） |                                      设备内部错误，请联系移远技术支持进行抓包分析                                      |
|     2,10437     |                         设备秘钥错误                         |                                            重新发起连接平台或等待设备自动重连                                            |
|     2,10438     |                     平台未查询到设备信息                     |                                            重新发起连接平台或等待设备自动重连                                            |
|     2,10450     |                   设备内部错误（连接失败）                   | 可能性一：设备暂无网络，请检查设备天线是否有接与注网状态<br>可能性二：设备内部错误，请联系移远技术支持进行抓包分析 |
|     2,10471     |                 实现方案版本不支持(连接失败)                 |                                      设备内部错误，请联系移远技术支持进行抓包分析                                      |
|     2,10473     |                   接入心跳异常（连接超时）                   |                                      设备暂无网络，请检查设备天线是否有接与注网状态                                      |
|     2,10474     |                     网络异常（连接超时）                     |                                      设备暂无网络，请检查设备天线是否有接与注网状态                                      |
|     2,10475     |                        服务器发生改变                        |                                 服务器或产品信息发生改变，需修改设备产品信息或服务器信息                                 |
|     2,10476     |                         连接 AP 异常                         |                                      设备暂无网络，请检查设备天线是否有接与注网状态                                      |
|     2,10477     |                    设备被禁用（连接失败）                    |                                         设备DeviceKey非法，检查DeviceKey是否合法                                         |
|     2,10478     |                         设备重置失败                         |                         可能为动态设备秘钥使用静态设备秘钥配置接口导致，请检查设备是否为动态设备                         |
|     2,10500     |                 注册失败（系统发生未知异常）                 |                                      设备内部错误，请联系移远技术支持进行抓包分析                                      |

## __3：订阅操作__

| 事件标识,事件码 |   描述   |                    处理方式                    |
| :-------------: | :------: | :--------------------------------------------: |
|     3,10200     | 订阅成功 |                                                |
|     3,10300     | 订阅失败 | 设备内部错误，请联系移远技术支持进行抓包分析 |

## __4：发送数据操作__

| 事件标识,事件码 |                           描述                           |             备注             |                                                                                                  处理方式                                                                                                  |
| :-------------: | :-------------------------------------------------------: | :---------------------------: | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------: |
|     4,10200     |                [,\<txid\>]透传数据发送成功                | txid_mode配置项开启后返回txid |                                                                                                                                                                                                            |
|     4,10210     |               [,\<txid\>]物模型数据发送成功               | txid_mode配置项开启后返回txid |                                                                                                                                                                                                            |
|     4,10220     |                [,\<txid\>]定位数据发送成功                | txid_mode配置项开启后返回txid |                                                                                                                                                                                                            |
|     4,10230     |                [,\<txid\>]设备状态发送成功                | txid_mode配置项开启后返回txid |                                                                                                                                                                                                            |
|     4,10240     |                [,\<txid\>]设备信息发送成功                | txid_mode配置项开启后返回txid |                                                                                                                                                                                                            |
|     4,10250     | [,\<time\>,\<time_zone\>,\<timestamp\>]NTP 信息发送成功 | txid_mode配置项开启后返回txid |                                                                                                                                                                                                            |
|     4,10300     |                [,\<txid\>]透传数据发送失败                | txid_mode配置项开启后返回txid | 可能性一：设备发送数据长度与命令指定数据长度相异，检查发送数据的长度是否正确<br>可能性二：设备发送数据长度大于模组所支持的最大发送长度 <br>可能性三：设备没有连接平台，检查设备是否处于已连接上平台状态 |
|     4,10310     |               [,\<txid\>]物模型数据发送失败               | txid_mode配置项开启后返回txid | 可能性一：设备发送数据长度与命令指定数据长度相异，检查发送数据的长度是否正确<br>可能性二：设备发送数据长度大于模组所支持的最大发送长度 <br>可能性三：设备没有连接平台，检查设备是否处于已连接上平台状态 |
|     4,10320     |                [,\<txid\>]定位数据发送失败                | txid_mode配置项开启后返回txid |                                                                                                                                                                                                            |
|     4,10330     |                [,\<txid\>]状态数据发送失败                | txid_mode配置项开启后返回txid |                                                                                                                                                                                                            |
|     4,10340     |                [,\<txid\>]设备信息发送失败                | txid_mode配置项开启后返回txid |                                                                                                                                                                                                            |
|     4,10350     |                [,\<txid\>]NTP 信息发送失败                | txid_mode配置项开启后返回txid |                                                                                                                                                                                                            |
|     4,10400     | 表示透传数据发送被拒绝 |
|     4,10410     | 表示物模型数据发送被拒绝|可能性一：设备发送数据长度与命令指定数据长度相异，检查发送数据的长度是否正确<br>可能性二：设备发送数据长度大于模组所支持的最大发送长度<br>可能性三：设备没有连接平台，检查设备是否处于已连接上平台状态 |
|     4,10420     | 表示定位数据发送被拒绝|

## __5：接收数据操作__

| 事件标识,事件码 |                                                      描述                                                      |                    备注                    |                  处理方式                  |
| :-------------: | :-------------------------------------------------------------------------------------------------------------: | :----------------------------------------: | :----------------------------------------: |
|     5,10200     |     URC中紧跟[,\<length>\<\r\n>\<data>]表示收到下发的透传数据。	下行数据为非缓存模式时，收到数据将直接下发     | 下行数据为非缓存模式时，收到数据将直接下发 |                                            |
|     5,10210     |                          URC中紧跟[,\<length>\<\r\n>\<data>]表示收到下发的物模型数据。                          | 下行数据为非缓存模式时，收到数据将直接下发 |                                            |
|     5,10211     | URC中紧跟,\<PkgID>,\<ID1>[,\<ID2>...]表示收到物模型查询命令。\<PkgID\>	平台下发的请求包ID；\<ID\>	物模型ID	； |                                            |                                            |
|     5,10212     |                    URC 中紧跟,\<PkgID\>,\<length\>,[\<data\>]表示收到下发的物模型服务数据。                    |  下行数据为非缓存模式时，收到数据将直接下发 |                                      |
|     5,10428     |                                            设备接收缓存过多导致限流                                            |                                            |      设备缓存过多，需及时读取缓存数据      |
|     5,10473     |                                    收到数据但长度超过设备缓存限制，接收失败                                    |                                            |   平台下发数据过长，检查平台下发数据长度   |

## __6：注销操作__

| 事件标识,事件码 |              描述              |
| :-------------: | :----------------------------: |
|     6,10200     |    注销成功（断开连接成功）    |

## __7：OTA 操作__

| 事件标识,事件码 |                                                            描述                                                            |                                                                                                       参数说明                                                                                                       |                                                                                                              处理方式                                                                                                              |
| :-------------: | :------------------------------------------------------------------------------------------------------------------------: | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------: | :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------: |
|     7,10700     | URC中紧跟,\<componentNo>,\<sourceVersion>,\<targetVersion>,\<batteryLimit>,\<minSignalIntensity>,\<useSpace>表示有升级任务 | \<componentNo>				组件标识；<br/>\<sourceVersion>			源版本；<br/>\<targetVersion>				目标版本；<br/>\<batteryLimit>				OTA升级最小电量；<br/>\<minSignalIntensity>		OTA升级最小信号强度；<br/>\<useSpace>					OTA升级需要磁盘空间； |                                                                                                                                                                                                                                    |
|     7,10701     |                        URC中紧跟[,"\<componentNo>",\<length>,"\<MD5>",\<CRC>]表示设备开始下载固件包                        |                                                             \<componentNo>		组件标识；<br/>\<length>			OTA升级固件包大小；<br/>\<MD5>				OTA升级固件包md5值；                                                             |                                                                                                                                                                                                                                    |
|     7,10702     |                                                        固件包下载中                                                        |                                                                                                                                                                                                                      |                                                                                                                                                                                                                                    |
|     7,10703     |                   URC中紧跟[,"\<componentNo>",\<length>,\<startaddr>,\<piece_length>]表示固件包下载完成                   |                                           \<componentNo>		组件标识；<br/>\<length>			OTA升级固件包大小；<br/>\<startaddr>			开始下载位置；<br/>\<piece_length>		当前文件块大小；                                           |                                                                                                                                                                                                                                    |
|     7,10704     |                                                        固件包更新中                                                        |                                                                                                                                                                                                                      |                                                                                                                                                                                                                                    |
|     7,10705     |                                                        更新固件成功                                                        |                                                                                                                                                                                                                      |                                                                                                                                                                                                                                    |
|     7,10706     |                                                        更新固件失败                                                        |                                                                                                                                                                                                                      | 可能性一：平台升级计划目标版本配置错误，检查升级计划中的目标版本填写是否正确<br>可能性二：升级过程中网络中断，检查OTA升级途中是否有网络或者信号中断现象 <br>可能性三：平台上传的升级包不正确，检查平台上传的升级包是否有效 |
|     7,10707     |                                                    首个设备操作结果广播                                                    |                                                                                                                                                                                                                      |                                                                                                                                                                                                                                    |


## __8：平台事件__

| 事件标识,事件码 |                   描述                   |                                       处理方式                                       |
| :-------------: | :--------------------------------------: | :----------------------------------------------------------------------------------: |
|     8,10428     |           设备高频消息导致限流           |                       设备发送消息过于频繁，请降低发送数据频率                       |
|     8,10429     | 超过单设备激活数量或者每日请求数导致限流 | 设备激活码不足，您可以联系当地销售进行价格咨询，由销售为您推进整个激活码的购买流程。 |


 ## __9：唤醒事件__

| 事件标识,事件码 | 描述    |
|:--------:| :-------------:|
| 9,10200| 设备从深度睡眠模式被唤醒 |
| 9,10300| 唤醒设备从深度睡眠模式错误 |



# Delta Package Making (IoT Modules Embedded with ASR Chips)

This chapter introduces you to make a delta package of a IoT Module embedded with an ASR chip with the delta package making tool. Delta packages can be used for DFOTA upgrades.

## Ⅰ. Tool Download & Usage
The delta package making tool can be provided by contacting Acceleronix Technical Support <<EMAIL>>.

## Ⅱ. Configuration of New and Old Firmware Package
Put __system image__ files generated by decompressing the __new firmware__ and __old firmware__ into __adiff_3.2.exe__ peer directory and name the two files __system_new.img__ and __system_old.img__ respectively .


## Ⅲ. Run the Script Command
1.  Run CMD under the __adiff_3.2.exe__ directory and enter the command to run the script to generate a delta package:

* For 8M modules
```c
adiff_3.2.exe  system_old.img system_new.img  system_patch.mini
```
* For 16M modules
```c
adiff.exe -p system_old.img  system_new.img  system_patch.bin -l fs
```

<a data-fancybox title="img" href="/en/deviceDevelop/KnowledgeBase/step27.png">![img](/en/deviceDevelop/KnowledgeBase/step27.png)</a>

2.  The generated delta package or 8M (system_patch.mini_1 system_patch.mini_2) or 16M (system_patch.bin) can be seen in the current directory after successful execution. You can upload this delta package to Acceleronix Developer Center for OTA upgrade .


Note: The name, system_patch, of the generated delta package can be changed, but the suffix cannot be modified. 

# Delta Package Making (Quectel Modules Embedded with UNISCO Chips)

This chapter introduces you to make a delta package of a Quectel Module embedded with an UNISOC chip with the delta package making tools. Delta packages can be used for DFOTA upgrades.

## Ⅰ. Tool Download & Usage
The delta package making tool can be provided by contacting Acceleronix Technical Support.
## Ⅱ. Configuration of New and Old Firmware Package
Put .pac files generated by decompressing the new firmware and old firmware into dtools.exe peer directory and name the two files new.pac and old.pac respectively.


## Ⅲ. Run the Script Command
1.  Run CMD under the __dtools.exe__ directory and enter the command to run the script:

```c
dtools.exe fotacreate2 --pac new.pac,old.pac,setting\fota8910.xml output.pack -d v  
```

As shown in the following figure.

<a data-fancybox title="img" href="/en/deviceDevelop/KnowledgeBase/step30.png">![img](/en/deviceDevelop/KnowledgeBase/step30.png)</a>

2. If it is generated successfully, you can see the generated delta package __output.pack__ in this directory. You can upload this delta package to Acceleronix Developer Center for OTA upgrade .

Note: The name __output__ of the generated delta package can be changed, but the suffix cannot be modified. 


# 删除邮箱关联


**接口地址**:`/v2/enduser/enduserapi/deleteEmailRelation`


**请求方式**:`DELETE`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:<p>删除邮箱关联</p>


**请求参数**:


| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型 | schema |
| -------- | -------- | -------- | -------- | -------- | ------ |
| code     | 验证码   | query    | true     | string   |        |


**响应状态**:


| 状态码 | 说明                       | schema             |
| ------ | -------------------------- | ------------------ |
| 200    | 删除邮箱关联成功           | 返回注册码响应数据 |
| 5046   | 邮箱验证码验证失败         |                    |
| 5064   | 请输入验证码               |                    |
| 5385   | 未绑定邮箱                 |                    |
| 5396   | 此为唯一登录方式，不能删除 |                    |


**响应参数**:


| 参数名称 | 参数说明   | 类型           | schema         |
| -------- | ---------- | -------------- | -------------- |
| code     | 响应状态码 | integer(int32) | integer(int32) |
| data     | 响应数据   | object         |                |
| extMsg   | 扩展消息   | string         |                |
| msg      | 响应消息   | string         |                |


**响应示例**:
```javascript
{
	"code": 0,
	"data": {},
	"extMsg": "",
	"msg": ""
}
```

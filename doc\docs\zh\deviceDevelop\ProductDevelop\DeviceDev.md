# 硬件开发

硬件开发页面会根据当前产品的连网方式，提供不同的开发资料指引。

## 开发资源

产品各连网方式对应可选择的开发方式如下：

| 连网方式    | 开发方式                                                     |
| ----------- | ------------------------------------------------------------ |
| 2G/3G/4G/5G | QuecOpen方案 <br />QuecPython方案 <br />AT指令方案 <br />Modbus方案 |
| WiFi        | QuecOpen方案 <br />0代码开发方案(部分方案支持)               |
| NB-IoT      | QuecOpen方案 <br />QuecPython方案 <br />AT指令方案           |
| 蓝牙        | QuecOpen方案                                                 |

<a data-fancybox title="img" href="/zh/deviceDevelop/creatproduct/devicedevelop02.png">![img](/zh/deviceDevelop/creatproduct/devicedevelop02.png)</a>

选择开发方式后将展示对应的开发指引与资源下载地址。

开发指引：主要包含开发指导文档、API文档、示例教程、帮助中心，您可根据指导文档快速完成设备接入开发者中心工作。

资源下载：主要包含所选模组开发所需的资料，点击后请选择您所使用的模组型号下载所需内容。

## MCU SDK

当选择AT指令方案时，开发者中心支持根据当前产品已发布的物模型生成MCU SDK。

<a data-fancybox title="img" href="/zh/deviceDevelop/creatproduct/devicedevelop01.png">![img](/zh/deviceDevelop/creatproduct/devicedevelop01.png)</a>

选择硬件平台后点击下一步，等待代码包生成。

生成成功后可下载到本地，并把SDK加入到工程文件中即可使用。

SDK的具体使用说明请查看代码包中doc目录下的MCU SDK用户应用指导说明文件。

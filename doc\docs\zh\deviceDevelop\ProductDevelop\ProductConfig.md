# 产品配置

开发者中心针对上层应用系统提供了不同的产品级功能配置能力，主要包含基础配置、App相关配置。

<a data-fancybox title="img" href="/zh/deviceDevelop/creatproduct/productconfig01.png">![img](/zh/deviceDevelop/creatproduct/productconfig01.png)</a>

# 基础配置

## 动态认证接入

开启：每台设备只需烧录ProductKey 和 ProductSecret ，IoT平台认证通过后会下发DeviceSecret，您可以根据安全需要开启或关闭该功能。

关闭：未导入或未连接过IoT平台的设备将无法通过设备认证。

## 自动续费

**重要说明：为了保障业务正常进行，系统默认启用产品级别的激活码自动续费功能。即当该产品下的设备激活码到期后，会自动消耗该产品下的激活码。若您的产品无需激活码自动续费，请手动关闭该功能。**

当自动续费功能开启时，可将指定设备加入自动续费黑名单，激活码到期后，不自动消耗激活码，设备将下线并置为未激活状态。

<a data-fancybox title="img" href="/zh/deviceDevelop/creatproduct/productconfig02.png">![img](/zh/deviceDevelop/creatproduct/productconfig02.png)</a>

1、点击“自动续费”卡片中“设置”按钮。弹窗页面展示自动续费黑名单中的设备，此列表中的设备自动续费设置为关闭状态。

2、通过“添加设备”将指定设备加入自动续费黑名单中，则当设备激活码到期后不会再自动消耗新的激活码。

3、通过“移除”将设备从自动续费黑名单中移除，移除后设备到期后可自动再消耗一个新的激活码。

# App相关配置

## 用户绑定权限

当使用移远官方App、OEM App或者基于移远SDK开发App时，可对用户与设备绑定的权限方式进行配置。

**多用户绑定：** 用户A绑定成功后，用户B再绑定设备时，操作成功。

**拥有者绑定：** 用户A绑定成功后，用户B再绑定设备时，操作失败。

**轮流绑定：** 用户A绑定成功后，用户B再绑定设备时，操作成功。用户A自动与设备解除绑定，无法继续使用。

备注：若当前产品有设备被用户绑定时，不允许修改绑定模式。

请注意，绑定关系的判断仅在同一个App下生效，不同App用户间的绑定互不影响。

## 多语言配置

通过多语言配置功能，您可针对产品目标销售区域的人群，自行配置App中展示的产品名称、物模型属性、配网引导、面板内容、消息推送的多语言文案。

**语种管理：**

开发者中心默认提供中英文的配置入口。当需要配置其他语种时，可通过语种管理进行添加，添加后在内容编辑弹框中将出现该语种的输入框。

**产品名称：**

App用户配网后显示的默认名称，通常展示在设备列表中。

**物模型：**

可对当前产品已发布的物模型配置不同语言的文案内容，通常显示在App设备面板中。

当进入设备面板时，会根据App用户当前使用的语言显示已配置的文案，若未设置多语言内容，则会使用创建物模型时填写的文案进行展示。

备注：若物模型已发布，但多语言配置中没有可配置的物模型多语言标签，请重新进行一次物模型发布操作。

**消息通知：**

可对当前产品已配置的消息通知规则配置不同语言的文案内容，您可在“产品配置->规则引擎”中新建消息通知规则。

当通知规则被触发时，App端将根据用户当前使用的语言进行对应文案内容的推送。

**配网引导：**

可对当前产品已配置的配网引导步骤配置不同语言的文案内容，您可在“产品配置->配网引导”中新建引导步骤。

**产品面板：**

可对当前产品已关联的面板内容配置不同语言的文案内容，您可在“产品交互->面板配置”中修改关联的产品面板。

**导出/导入：**

可通过导出功能，把当前可配置的多语言标签进行批量导出，统一编辑后再重新进行导入。导入仅可对产品下已有的多语言标签进行配置

## 配网引导

当使用OEM App时，可对App上显示的配网流程添加各步骤的介绍文字与配图。

图片可上传JPG、JPEG、PNG格式，步骤文字可输入不超过100字符。

配网引导步骤最多仅可以添加五步。

## 规则引擎

通过配置规则引擎，可基于设备上报数据或设备上下线事件，向移动端用户推送告警消息，又或者向SaaS客户端推送告警事件。

创建规则时需把左侧组件拖入到画布中，并通过组件间的连线完成数据流配置。可通过点击已拖入画布中的组件进行详细配置。

<a data-fancybox title="img" href="/zh/deviceDevelop/creatproduct/productconfig03.png">![img](/zh/deviceDevelop/creatproduct/productconfig03.png)</a>

| 组件名称            | 描述                                                         |
| ------------------- | ------------------------------------------------------------ |
| 输入-设备数据       | 支持以下设备数据触发规则： <br>●设备上报物模型数据：支持物模型产品通过设备上报的属性、事件作为触发条件。 <br/>●设备上报定位数据：支持通过设备上报的定位数据作为触发条件。 <br/>●设备上线：支持通过设备的上线事件作为触发条件。 <br/>●设备离线：支持通过设备的离线事件作为触发条件，可配置持续离线后一段时间再触发。 <br/>●设备重置：支持通过设备的重置事件作为触发条件。 <br/>●设备持续无上报数据：支持监控设备持续一段时间内未上报数据作为触发条件。 |
| 处理方法-逻辑规则   | 仅当选择设备上报物模型数据作为触发条件时，可使用该组件。 <br/>**触发方式：** <br/>**连续触发：**指每次达到触发条件即会触发，例如设置当光照值大于400 lux时触发，只要设备上报光照值为401 lux、410 lux或以上光照值均会触发消息推送。 <br>**边缘触发：**指达到触发条件临界值仅会触发一次，需要再次达到临界值才会二次触发。例如设置当光照值大于400 lux时触发，设备上报光照值第一次到达400 lux以上时会触发消息推送，但持续上报410 lux、420 lux则不会触发消息推送。需要设备上报光照值小于400 lux后，再次上报400 lux以上光照值时才会二次触发消息推送。 <br/>**比较参数1：** <br/>配置触发规则的物模型属性，每个类型的属性支持不同的判断规则。 <br>BOOL：支持数据上报、True/False值判断触发； <br/>INT/FLOAT/DOUBLE：支持数据上报、数值大于、小于或等于特定数值触发； <br/>ENUM：支持数据上报、枚举值判断触发； <br/>TEXT：支持数据上报、文本值等于特定文本触发； <br/>RAW：支持数据上报触发； <br/>DATE：支持数据上报、时间值大于、小于、或等于特定时间触发； <br/>ARRAY：支持数据上报触发； <br/>STRUCT：支持数据上报触发与参数具体值判断触发，规则与普通属性一致。 <br/>**比较参数2：** <br/>●固定值：当比较参数1选择数值类型时，可使用固定值进行比较。 <br/>●设备属性：支持配置其他与比较参数1同类型的属性进行比较。 <br/>●四则运算：当比较参数1选择数值类型时，可选择对指定的数值型属性进行四则运算后的结果进行比较。 |
| 处理方法-地理围栏   | 仅当选择设备上报定位数据作为触发条件时，可使用该组件。 <br/>支持通过圆形或多边形的方式选择围栏边界，并指定是进入围栏范围时触发或离开围栏范围时触发。 |
| 规则过滤-设备过滤   | 可选组件。 <br/>设备白名单：当前产品下仅白名单中的设备可触发本条规则。 <br/>设备黑名单：当前产品下除黑名单外的设备可触发本条规则 。 <br/>输入设备黑白名单时，需填入设备DeviceKey，每行为一台设备。 |
| 规则过滤-推送间隔   | 可选组件。 <br/>推送间隔时间内不会重复推送。最大间隔不超过24小时。 |
| 执行动作-控制设备   | 仅物模型产品可使用该组件。 <br/>规则触发时，可对触发该规则的设备下发已配置好的控制指令。 |
| 执行动作-AMQP推送   | 规则触发时，将向已订阅规则引擎消息类型的AMQP客户端推送该事件。 <br/>可通过配置自定义参数区分不同规则事件。 |
| 执行动作-消息通知   | 当触发规则时，将向已绑定该设备的App用户推送消息，需设置推送的标题与正文内容。 |
| 执行动作-公众号推送 | 当触发规则时，将通过公众号向已绑定该设备的小程序用户推送消息，需关联公众号已审核的模板以及关联对应参数。 <br/>备注：需在个人中心-第三方配置-公众号配置中先添加公众号配置。 |

## 联动配置

当使用移远官方App、OEM App或者基于移远SDK开发App时，可使用自动化联动功能，在使用前需要对当前产品可支持自动化联动，且由设备触发或设备执行的触发条件与触发动作进行配置。

针对触发条件与触发动作，仅可选择BOOL、INT、FLOAT、DOUBLE、ENUM类型的物模型属性，保存后在App添加自动化任务时，可选择已配置的属性。

## 产品说明书

添加后移动端用户可查看该产品的电子说明书，说明书可上传小于20M的PDF格式文件。 企业可根据使用人群添加不同语种的产品说明书，且支持把某一语种文件设为默认说明书。

## 数据聚合配置

若移动端（App或者小程序等）需要使用物模型的统计接口，可配置需要进行聚合的物模型属性。

点击添加配置时，可添加INT、FLOAT、DOUBLE类型的物模型属性，保存后系统将对设备上报的属性进行聚合，可查询最大值、最小值、平均值等数据。

当不再需要进行聚合分析时，可删除对应的属性。

## 用户订阅通知

当使用移远官方App、OEM App或者基于移远SDK开发App时，可开启用户订阅通知功能，开启后若有App用户通Websocket功能订阅设备状态（例如进入设备详情页）时，设备端将收到用户订阅通知，可按需提高或降低数据的上报频率，让用户可更实时的查看设备状态。

## 设备二维码

当App用户需要通过扫描二维码的方式绑定设备时，可上传设备DK与二维码的对应关系。

当产品为蜂窝设备时，支持配置使用DeviceKey作为二维码内容进行设备绑定。

## AI能力

当使用移远官方App时，开启AI能力后App端将展示AI智能推荐提示。

## 低功耗配置

若您所选择的方案支持低功耗，则可查看该配置卡片。

当使用移远官方App、OEM App或者基于移远SDK开发App时，可配置低功耗心跳周期与下行消息的缓存时长。

开启后若设备已离线，但还处于低功耗心跳周期内，则App上将显示设备的状态为低功耗，若超出低功耗心跳周期设备也没有与平台端进行数据交互，才会变为离线。

开启该功能后，包括离线消息推送均会按照低功耗心跳周期超时进行触发。

## 优先激活蓝牙

针对WiFi产品可开启该功能，当无法连接路由器时，可通过蓝牙通道激活设备，不阻碍App用户使用。

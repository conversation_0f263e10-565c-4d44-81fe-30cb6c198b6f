# Message Center

Message Center provides users with a channel to view historical system messages and notifications. The bell icon in the upper right corner displays the number of unread messages.

| Message Type   | Description                                                  |
| -------------- | ------------------------------------------------------------ |
| System Message | Notifications related to the enterprise user registration review result, result of individual developer account upgrade to enterprise account, review result of industry application activation application and message queue expiration. |
| Notice         | Officially released announcement messages, such as system update notices and feature adjustment information. |


# 物模型格式数据业务交互示例
## **场景描述**
本章节指导用户通过 AT命令 接入方案进行物模型数据格式的业务交互。

## **涉及指令**

|    指令名称    |         功能描述         |
| :------------: | :----------------------: |
|   AT+QIOTCFG   |       配置可选参数       |
| AT+QIOTMODELTD |   发送物模型数据至平台   |
| AT+QIOTMODELRD | 读取平台下发的物模型数据 |

<span style='color:#999AAA'>提示：详情请参考[数据业务交互 相关命令](/deviceDevelop/DeviceAccessPlan/cellular/AT/API/cellular-at-04)。</span>


	
## **操作步骤**
### **平台侧**
#### **一、登录开发者中心**

登录<a href="https://iot.quectelcn.com" target="_blank">开发者中心</a>，未注册可单击<a href="https://iot.quectelcn.com/registerType" target="_blank">立即注册</a>完成注册。


#### **二、创建产品**


创建产品详情请参考 [创建产品](/deviceDevelop/DeviceAccessPlan/speediness-01)。

### **设备侧**
#### **物模型数据交互**

物模型是开发者中心 针对物理实体设备在开发者中心建立的数据模型，主要用于描述产品的功能，从而方便数据的解析。设备成功接入平台后，通过 __AT+QIOTMODELTD__ 与 __AT+QIOTMODELRD__ 指令来实现与开发者中心的数据交互。





|    功能ID    | 功能类型 | 功能名称                        | 数据类型 | 数值范围                          | 引用关系       | 示例                                                                   |
| :----------: | :------- | :------------------------------ | :------- | :-------------------------------- | :------------- | :--------------------------------------------------------------------- |
|      1       | 属性     | 开关                            | BOOL     | true - 开启；<br />false - 关闭； |                | <span v-pre>{"1":true}</span>                                          |
|      2       | 属性     | 电池电量                        | INT      | 0~100                             |                | <span v-pre>{"2":100}</span>                                           |
|      3       | 属性     | 精油剩余容量                    | FLOAT    | 0~300                             |                | <span v-pre>{"3":25.6}</span>                                          |
|      4       | 属性     | 工作模式                        | ENUM     | 1~HPC<br>2~CPL                    |                | <span v-pre>{"4":1} </span>                                            |
|      5       | 属性     | 设备编号                        | TEXT     | 0~20                              |                | <span v-pre>{"5":"ZWS"} </span>                                        |
|      6       | 属性     | 时间                            | DATE     | --                                |                | <span v-pre>{"6":1659664758817} </span>                                |
|      7       | 属性     | 结构体                          | STRUCT   | --                                | 功能ID：8，9   | <span v-pre>{"7":{"8":true,"9":100}} </span>                           |
|  8(父ID：7)  | 参数     | <span v-pre> {$参数名称}</span> | BOOL     | true - 开启；<br>false - 关闭；   |                | --                                                                     |
|  9(父ID：7)  | 参数     | <span v-pre> {$参数名称}</span> | INT      | 0~100                             |                | --                                                                     |
|      10      | 属性     | 整型数组                        | ARRAY    | 最大数量：10，元素类型：INT       |                | <span v-pre>{"10":[10,20,30]} </span>                                  |
|      11      | 属性     | 结构体数组                      | ARRAY    |                                   | 功能ID：12，13 | <span v-pre>{"11":[{"12":true,"13":100},{"12":false,"13":98}]} </span> |
| 12(父ID：11) | 参数     | <span v-pre> {$参数名称}</span> | BOOL     | true - 开启；<br>false - 关闭；   |                | --                                                                     |
| 13(父ID：11) | 参数     | <span v-pre> {$参数名称}</span> | INT      | 0~100                             |                | --                                                                     |


<span style="color:#FF0000">开发建议：上报频率不宜过快，建议采用灵活上报的形式： <br>
若是控制类的数据（如开关、工作模式），则可采用变化上报的方式。例如平台或者APP下发控制或者通过其他方式改状态值，设备再进行上报到平台端。 <br>
若是实时变化类的数据（如电流、温度值），则可采用定时上报。例如每隔5分钟设备定时上报一次到平台端。</span>

#### **示例一(主动发送物模型JSON格式数据)**
__1、将数据格式设置为JSON格式__

MCU可通过 __AT+QIOTCFG="tsl"[,\<tsl_mode>]__ 指令配置物模型数据格式，若不配置则出厂默认为 __`0`__：TTLV格式。

```c
[TX]AT+QIOTCFG="tsl",1

[RX]AT+QIOTCFG="tsl",1

OK
```
__2、发送JSON格式物模型数据__

发送物模型数据149字节 __{"1":true,"2":100,"3":25.6,"4":1,"5":"ZWS","6":1659664758817,"7":{"8":true,"9":100},"10":[10,20,30],"11":[{"12":true,"13":100},{"12":false,"13":98}]}__，QoS级别为1，则执行 __AT+QIOTMODELTD=1,149__，待响应 __>__ 后，MCU发送物模型数据。

```c
[TX]AT+QIOTMODELTD=1,149
[RX]>
[TX]{"1":true,"2":100,"3":25.6,"4":1,"5":"ZWS","6":1659664758817,"7":{"8":true,"9":100},"10":[10,20,30],"11":[{"12":true,"13":100},{"12":false,"13":98}]}
[RX]OK

[RX]+QIOTEVT: 4,10210
```

<span style="color:#999AAA">提示：若QoS级别为1或以上，模块成功发送数据后将响应OK并上报+QIOTEVT: 4,10210；若QoS级别为0，模块成功发送数据后将响应OK，不上报事件。</span>



#### **示例二(根据PkgID响应平台读取数据)**
__1、开发者中心批量读取物模型实时数据__

在开发者中心 __设备信息__ 详情页的 __设备调试__ 中，点击 __批量读取__ 按钮可一键批量读取物模型属性数值。


__2、模组收到查询指令并向MCU打印回调事件__

* 回调事件解析：__+QIOTEVT: 5,10211,1,1,2,3,4,5,6,7,10,11__

	__5__ ：收到下行业务事件。
	
	__10211__ ：收到平台读取物模型属性事件Code。
	
	__1__ ：请求包 ID，该参数仅在模块接收物模型数据响应时有效。
	
	__1,2,3,4,5,6,7,10,11__ ：需要读取物模型属性的ID号，分别是ID1、2、3、4、5、6、7、10、11。
	
__3、MCU根据回调事件判断回复的PkgID与物模型ID实时数据__

```c
[TX]AT+QIOTMODELTD=1,149,7
[RX]>
[TX]{"1":true,"2":100,"3":25.6,"4":1,"5":"ZWS","6":1659664758817,"7":{"8":true,"9":100},"10":[10,20,30],"11":[{"12":true,"13":100},{"12":false,"13":98}]}
[RX]OK

[RX]+QIOTEVT: 4,10210
```
<span style="color:#999AAA">提示：若QoS级别为1或以上，模块成功发送数据后将响应OK并上报+QIOTEVT: 4,10210；若QoS级别为0，模块成功发送数据后将响应OK，不上报事件。</span>

#### **示例三(主动上报物模型告警事件)**

|    功能ID    | 功能类型 | 功能名称     | 数据类型 | 数值范围 | 引用关系           |
| :----------: | :------- | :----------- | :------- | :------- | :----------------- |
|      14      | 事件     | 剩余精油不足 | BOOL     |          | 输出功能ID：15，16 |
| 15(父ID：14) | 参数     | 电池电量     | INT      | 0~100    |
| 16(父ID：14) | 参数     | 精油剩余容量 | FLOAT    | 0~300    |

MCU使用 __AT+QIOTMODELTD__ 上报物模型事件，待模块响应“>”后，MCU再发送组成后的物模型数据。

```c
[TX]AT+QIOTMODELTD=2,28
[RX]>
[TX]{"14":{"15":100,"16":32.89}}
[RX]OK

+QIOTEVT: 4,10210
```
<span style="color:#999AAA">提示：若QoS级别为1或以上，模块成功发送数据后将响应OK并上报+QIOTEVT: 4,10210；若QoS级别为0，模块成功发送数据后将响应OK，不上报事件。</span>

#### **示例四(非缓存模式读取平台下发的物模型数据)**

|    功能ID    | 功能类型 | 功能名称 | 数据类型 | 数值范围 | 引用关系       |
| :----------: | :------- | :------- | :------- | :------- | :------------- |
|      17      | 服务     | 服务     | BOOL     |          | 输出功能ID：18 |
| 18(父ID：17) | 输入参数 | 模式     | ENUM     | 0~2      |

若不配置则出厂默认为 __`0`__：关闭缓存模式。

__1、开发者中心下发物模型服务__

 在开发者中心 __设备调试__ 页面，第一步点击 __服务调用__，第二步选择要下发的服务物模型，第三步点击左下角的 __发送指令__ 即可下发物模型服务给终端。


__2、模组收到服务下发指令并向MCU打印回调事件与数据__

模组收到服务指令后，会主动向MCU打印回调事件 __+QIOTEVT: 5,10210,15__ 与下发的服务物模型数据 __{"17":{"18":1}}__。

```c
[RX]+QIOTEVT: 5,10210,15
{"17":{"18":1}}
```
#### **示例五(缓存模式读取平台下发的物模型数据)**
__1、打开下行数据缓存模式__

MCU可通过 __AT+QIOTCFG="buffer"[,<buffer_mode>]__ 指令配置下行数据缓存模式。

```c
[TX]AT+QIOTCFG="buffer",1

[RX]AT+QIOTCFG="buffer",1

OK
```

__2、开发者中心下发物模型服务__

 在开发者中心 __设备调试__ 页面，第一步点击 __服务调用__，第二步选择要下发的服务物模型，第三步点击左下角的 __发送指令__ 即可下发物模型服务给终端。


__3、模组收到服务下发指令并向MCU打印回调事件__

* 回调事件解析：__+QIOTEVT: 5,10210__

	__5__ ：收到下行业务事件。
	
	__10210__ ：收到平台读取物模型服务事件Code。
	

__4、MCU根据回调事件查询服务__
```c
[TX]AT+QIOTMODELRD=512

[RX]AT+QIOTMODELRD=512

+QIOTMODELRD: 15,0,0
{"17":{"18":1}}
OK
```

 
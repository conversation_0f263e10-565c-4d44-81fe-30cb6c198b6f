## IoT SDK 集成指南

## 功能概述

QuecIotSdk用于移远IoT SDK的入口SDK，支持用户基于移远云或私有化部署开发。
使用SDK可以帮助开发者快速完成APP开发，开发者仅需关注APP的UI和UE即可，而相对复杂的协议与错误等事项的处理可忽略。
主要功能如下:

- 初始化SDK, 执行后其它用户、设备、家居等接口功能才能正常执行
- 开启Debug模式，打印日志用于分析问题
- 设置国家码，获取全球引导域名相关服务数据
- 获取国家列表数据

## SDK集成

### 第一步: 注册开发者中心账号

在使用开发者中心前，你需要通过[https://aiot.quectel.com/](https://aiot.quectel.com/)注册一个账号。

### 第二步: 集成SDK

#### 使用CocoaPods集成

当前SDK最低支持iOS13.0系统

1. 更新 CocoaPods 至最新版本。CocoaPods 的使用请参考 [CocoaPods 官方文档](https://guides.cocoapods.org/)。
2. 在 Podfile 文件中添加以下内容：

```objc
source 'https://github.com/thridparty-cloud2/QuecPublicSpecs.git'

platform :ios, '13.0'
target 'Your_Project_Name' do
    use_frameworks!
    pod "QuecIotSdk"
    
end
```
    
3. 在项目根目录下，执行 pod update 命令进行集成。

#### 下载SDK集成

1. 下载中心下载[SDK资源](https://iot.quectelcn.com/download?menuCode=APP_SDK&resourceType=C)
2. 将下载的zip双击进行解压，将文件夹内的静态库集和资源分别集成到工程中

<a data-fancybox title="img" href="/zh/appDevelop/iosGuide/frameworks.png">![img](/zh/appDevelop/iosGuide/frameworks.png)</a>

3. 添加系统依赖库
 - libc++.tbd
 - CoreTelephony.framework
 - libz.tbd
 - SystemConfiguration.framework
 - libresolv.9.tbd
4. 集成第三方依赖库（CocoaPods集成需要use_frameworks!设置）
 - AFNetworking （4.0）
 - YYModel （1.0.4）
 - SocketRocket （0.6.0）
 - CocoaAsyncSocket （7.6.5）
5. 在"TARGETS" – "QuecTestSDK" – "Build Settings"页面，"Other Linker Flags" 下增加"-ObjC"字符
6. 开启Swift和OC混编（新建一个Swift文件，Xcode会自动创建XXX-Bridge-Header.h文件）

### 第三步: 配置userDomain和userDomainSecret

在开发者中心上创建一个app可获取userDomain和userDomainSecret

<a data-fancybox title="img" href="/zh/appDevelop/iosGuide/create_sdk_app_1.png">![img](/zh/appDevelop/iosGuide/create_sdk_app_1.png)</a>

<a data-fancybox title="img" href="/zh/appDevelop/iosGuide/create_sdk_app_2.png">![img](/zh/appDevelop/iosGuide/create_sdk_app_2.png)</a>

### 第四步: 初始化SDK


**接口说明**

初始化设置用户域和用户域秘钥, 并指定云服务类型, 设置后将调用对应的移远云国内、欧洲、北美服务

```objc
- (void)startWithUserDomain:(NSString *)userDomain
           userDomainSecret:(NSString *)userDomainSecret
           cloudServiceType:(QuecCloudServiceType)cloudServiceType;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- | 
| userDomain |	是|用户域，DMP平台创建APP后生成| 
| userDomainSecret |	是|用户域秘钥，DMP平台创建APP后生成| 
| cloudServiceType |	是|云服务类型，指定连接的云服务| 

**QuecCloudServiceType枚举定义**

```objc
typedef NS_ENUM(NSUInteger, QuecCloudServiceType) { //云服务类型
    QuecCloudServiceTypeChina = 0, //国内
    QuecCloudServiceTypeEurope,    //欧洲
    QuecCloudServiceTypeNorthAmerica,    //北美
};
```

**示例代码**

```objc
[QuecIoTAppSDK.sharedInstance startWithUserDomain:@"your userDomain" userDomainSecret:@"your domainSecret" cloudServiceType:QuecCloudServiceTypeChina];
```

#### 私有化部署初始化

**接口说明**

支持私有化部署初始化, 用户可以配置SDK接入的云服务地址和域名引导地址等信息

```objc
- (void)startWithConfig:(QuecPublicConfig *)config;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- | 
| config |	是|初始化配置模型| 

**QuecPublicConfig属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| userDomain       | NSString             | 用户域 （必填） |
| userDomainSecret       | NSString             | 用户域秘钥 （必填） |
| baseUrl       | NSString             | 请求url （必填） |
| webSocketUrl       | NSString             | websocket1.0 地址 （必填） |
| webSocketV2Url       | NSString             | websocket2.0 地址 （必填） |
| bootstrapUrl       | NSString             | 获取全球域名引导服务请求域名 （必填） |
| bootstrapPath       | NSString             | 获取全球域名引导服务请求path （必填） |
| bootstrapToken       | NSString             | 获取全球域名引导服务请求key （必填） |
| mcc       | NSString             | 域名引导国家mcc （非必填） |
| tcpAddr       | NSString             | 域名引导tcp地址 （非必填） |
| pskAddr       | NSString             | 域名引导psk地址 （非必填） |
| tlsAddr       | NSString             | 域名引导tls地址 （非必填） |
| cerAddr       | NSString             | 域名引导cer地址 （非必填） |
| reportAddr       | NSString             | 域名引导蓝牙数据上报url （非必填） |

**示例代码**

```objc
QuecPublicConfig *iotConfig = QuecPublicConfig.new;
iotConfig.userDomain = @"X.XX.XXXX.X";
iotConfig.userDomainSecret = @"XXXXXXXXXXXXXXXXXXXXXXXX";
iotConfig.baseUrl = @"https://xxx.com";
iotConfig.webSocketUrl = @"xxx://xxx.com/xx";
iotConfig.webSocketV2Url = @"xxx://xxx.com:xxxx/xx";
iotConfig.bootstrapUrl = @"https://xxx.com";
iotConfig.bootstrapPath = @"/xxx";
iotConfig.bootstrapToken = @"xxx";
[QuecIoTAppSDK.sharedInstance startWithConfig:iotConfig]
```

#### Debug模式开关

**接口说明**

在开发的过程中可以开启Debug模式，打印日志用于分析问题。

```objc
- (void)setDebugMode:(BOOL)debugMode;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- | 
| debugMode |	是|Debug模式开启或关闭|

**示例代码**

```objc
[QuecIoTAppSDK.sharedInstance setDebugMode:YES];
```

#### 设置国家码

**接口说明**

用于通过国家码获取mcc，通过mcc获取全球引导域名相关服务数据。

```objc
- (void)setCountryCode:(NSString *)countryCode;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- | 
| countryCode |	是|国家码, 如中国传入"86"|

**示例代码**

```objc
[QuecIoTAppSDK.sharedInstance setCountryCode:@"86"];
```

#### 获取国家列表数据

**接口说明**

获取移远云支持国家列表数据 (需要先调用初始化SDK 方法)。

```objc
- (NSArray<QuecCountryInfoModel *> *)getCountryData;
```

**参数说明**

**QuecCountryInfoModel属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| name       | NSString             | 国家名称 |
| internationalCode       | NSString             | 国家代码（例如："+86"） |

**示例代码**

```objc
NSArray<QuecCountryInfoModel *> *countrys = [QuecIoTAppSDK.sharedInstance getCountryData];
if (countrys.count) {
    /// Next Action
}
```

## 运行demo

### Demo介绍

> 注意
>
> - Demo需要使用到[开发者](https://iot.quectelcn.com/)平台，请先按照SDK集成第二步来获取用户域和用户域秘钥。
> - 该Demo仅用于演示SDK的运行效果，请勿直接商用, 实际使用中需要根据业务需求进行修改。

Demo应用主要包括以下功能:

- 用户管理: 手机号, 邮箱登录, 注册, 找回密码
- 设备管理: 添加设备, 删除设备, 查询设备列表, 查询设备详情, 控制设备, 设备分组
- 消息管理
- 场景,自动化

Demo下载地址: [SDK Demo](https://github.com/thridparty-cloud2/quecloud-iot-ios-sdk-demo-objc)

### Demo运行说明

1. 从gitlab上clone demo到本地
2. 修改Demo项目中的LoginViewController文件, 根据数据中心区域配置对应的用户域(UserDomain)和用户域秘钥(UserSecret)
3. pod install安装组件库
4. 在Xcode中点击运行, 即可运行Demo



# 用户账号管理

本文介绍了如何使用手机号码注册账号, 登录等功能

## 功能概述

```objc
#import <QuecUserKit/QuecUserKit.h>
/// 初始化
[QuecUserService sharedInstance]
```

### 查询手机号是否已注册

**接口说明**

用于校验手机号是否已注册

```objective-c
- (void)queryPhoneIsRegister:(NSString *)phone 
           internationalCode:(NSString *)internationalCode
                     success:(void(^)(BOOL isRegister))success
                     failure:(void(^)(NSError *error))failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- | 
| phone |	是|手机号码| 
| internationalCode |	否|国家码| 
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调，error表示失败原因	| 

**示例代码**

```objc
[[QuecUserService sharedInstance] queryPhoneIsRegister:@"account" internationalCode:@"code" success:^(BOOL isRegister) {
    if (isRegister) {
    } else {
    }
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 手机号密码登录

**接口说明**

用于用户手机号+密码登录

```objc
- (void)loginByPhone:(NSString *)phone 
            password:(NSString *)password
   internationalCode:(NSString *)internationalCode
             success:(QuecVoidBlock)success
             failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|
| --- | --- | --- |
| phone |	是|手机号码|
| password          | 是       | 密码                 |
| internationalCode | 否       | 国际代码，默认为国内 |
| success           | 否       | 接口请求成功回调     |
| failure | 否 |接口请求失败回调	|

**示例代码**

```objc
[[QuecUserService sharedInstance] loginByPhone:@"phone" password:@"password" internationalCode:@"+86" success:^{
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 手机号短信验证码登录

**接口说明**

用于手机号短信验证码登录，需先获取短信验证码

```objc
- (void)loginWithMobile:(NSString *)mobile 
                    code:(NSString *)code
      internationalCode:(NSString *)internationalCode
                 success:(QuecVoidBlock)success
                 failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|
| --- | --- | --- |
| mobile |	是|手机号	| 
| code |	是|验证码	| 
| internationalCode |	否|国际代码，默认为国内	| 
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**示例代码**

```objc
[[QuecUserService sharedInstance] loginWithMobile:@"account" code:@"code" internationalCode:@"+86" success:^{
    /// Next Action            
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 手机号密码注册

**接口说明**

用于手机号密码注册，需先获取手机验证码

```objc
- (void)registerByPhone:(NSString *)phone 
                    code:(NSString *) code
               password:(NSString *)password
      internationalCode:(NSString *)internationalCode
                success:(QuecVoidBlock)success
                failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|
| --- | --- | --- |
| phone |	是|手机号|
| code |	是|验证码|
| password |	是|密码|
| internationalCode |	否|国际代码，默认为国内|
| success           | 否       | 接口请求成功回调     |
| failure           | 否       | 接口请求失败回调     |


**示例代码**

```objc
[QuecUserService.sharedInstance registerByPhone:@"account"
                                            code:@"code"
                                         password:@"password"
                                 internationalCode:0
                                            success:^{
    /// Next Action   
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 发送手机验证码

**接口说明**

 用于密码重置、登录、注册、注销、增值服务接受人 发送手机验证码

```objc
- (void)sendVerifyCodeByPhone:(NSString *)phone
            internationalCode:(NSString *)internationalCode
                         type:(QuecVerifyCodeType)type
                      success:(QuecVoidBlock)success
                      failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|
| --- | --- | --- |
| phone |	是|手机号|
| internationalCode |是|国际代码|
| type |是|QuecVerifyCodeType|
| success           | 否       | 接口请求成功回调                                             |
| failure           | 否       | 接口请求失败回调                                             |

```objc
typedef NS_ENUM(NSUInteger, QuecVerifyCodeType)
{
    QuecVerifyCodeTypeReset = 1 ,  // 密码重置
    QuecVerifyCodeTypeLogin,        // 登录
    QuecVerifyCodeTypeRegister,     // 注册
    QuecVerifyCodeTypeLogout,       // 注销
    QuecVerifyCodeTypeAddValue,     // 增值服务接受人
};
```

**示例代码**

```objc
[QuecUserService.sharedInstance sendVerifyCodeByPhone:@"account" 
                                    internationalCode:@"code"
                                    type:QuecVerifyCodeTypeLogin success:^{
    /// Next Action   
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 验证国际手机号格式

**接口说明**

用于验证国际手机号格式

```objc
- (void)validateInternationalPhone:(NSString *)phone 
                 internationalCode:(NSString *)internationalCode
                            success:(QuecVoidBlock)success
                            failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|
| --- | --- | --- |
| phone |	是|手机号| 
| internationalCode |是|国际代码| 
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	|

**示例代码**

```objc
[QuecUserService.sharedInstance validateInternationalPhone:@"account" internationalCode:@"+86" success:^{
    /// Next Action  
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 验证短信验证码

**接口说明**

用于验证短信验证码，需先获取短信验证码

```objc
- (void)validateSmsCode:(NSString *)phone 
                smsCode:(NSString *)smsCode
      internationalCode:(NSString *)internationalCode
                   type:(NSInteger)type
                success:(QuecVoidBlock)success
                failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|
| --- | --- | --- |
| phone |	是|手机号| 
| smsCode |	是|验证码| 
| internationalCode |	否|国际代码| 
| type |否|验证码验证后是否失效，1：失效 2：不失效，默认 1| 
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**示例代码**

```objc
[[QuecUserService sharedInstance] validateSmsCode:@"account" smsCode:@"code" internationalCode:@"internationalCode" type:1 success:^{
    /// Next Action 
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 手机号重置密码

**接口说明**

 用户通过手机号+验证码重置密码

```objc
- (void)resetPasswordByPhone:(NSString *)phone 
                        code:(NSString *)code
           internationalCode:(NSString *)internationalCode
                    password:(NSString *)password
                     success:(QuecVoidBlock)success
                     failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|
| --- | --- | --- |
| code |	是|验证码	|
| phone |	否|手机号码	|
| internationalCode |	否|国际代码，配合手机号码使用，默认为国内	|
| password |	否|用户重置的密码，如果不输入默认为 ********求成功回调	|
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	|


**示例代码**

```objc
[[QuecUserService sharedInstance] resetPasswordByPhone:@"phone" code:@"code" internationalCode:@"countryCode" password:@"password" success:^{
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

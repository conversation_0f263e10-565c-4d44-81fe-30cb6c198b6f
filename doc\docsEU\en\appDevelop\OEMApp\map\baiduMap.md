# Configure Baidu Maps

#### Get Baidu Maps SDK App Key at [https://lbs.baidu.com/apiconsole/key](http://lbsyun.baidu.com/apiconsole/key).

#### Please log in with your Baidu account first, as shown below.

<a data-fancybox title="img" href="/en/appDevelop/oemapp/map/baidumap1.png">![image](/en/appDevelop/oemapp/map/baidumap1.png)</a>

(If you do not have a Baidu account, click "**立即注册(Register Now)**" in the lower right corner to register an account and log in to Baidu as prompted.)

#### After logging in, you will enter the "API Console", as shown below.

<a data-fancybox title="img" href="/en/appDevelop/oemapp/map/baidumap2.png">![image](/en/appDevelop/oemapp/map/baidumap2.png)</a>

#### Click "创建应用(Create App)" to apply for an App Key.

<a data-fancybox title="img" href="/en/appDevelop/oemapp/map/baidumap3.png">![image](/en/appDevelop/oemapp/map/baidumap3.png)</a>

#### Enter the "应用名称(App Name)", select "Android SDK" as the "应用类型(App Type)", and enter the correct SHA1 and "包名(Package Name)".

* 包名(Package Name): Get the name on Developer Center.
* SHA1: Refer to *Create Android Certificate* section.

<a data-fancybox title="img" href="/en/appDevelop/oemapp/map/baidumap4.png">![image](/en/appDevelop/oemapp/map/baidumap4.png)</a>

* After completing the above information, click "**提交(Submit)**" to generate the App Key for your app. Then you can use the App Key to start your development. Note: For the same App Key, you can enter both the SHA1 for the development version and that for the release version, so you do not need to change the App Key in the entire process from development, testing to release. The generated App Key covers all functionalities of the original App Key, and the input SHA1 is entered into the "Release Version SHA" field by default. Developers can also update the App Key by merging the SHA1 corresponding to the App Key in the development version and that corresponding to the App Key in the release version.

  

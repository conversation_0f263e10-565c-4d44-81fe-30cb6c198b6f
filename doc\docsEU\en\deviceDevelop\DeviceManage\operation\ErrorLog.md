# Exception Logs

Exception logs display the data generated when the TSL data reported by devices is inconsistent with defined features.

 

### **Prerequisites**

● A product has been created.

● The product has been developed and the features are defined.

● The device has sent uplink data to Developer Center.

 

### **Steps**

1.   Log in to Developer Center, click "**Device Management**" → " **Device Maintenance**" in the left-hand navigation bar to display the list of devices. Find the device whose exception logs you want to view and click "**View**" in the "Action" column to enter the detail page of the device. Then click "**Exception Logs**" tab.

<a data-fancybox title="img" href="/en/guide/20230601131957.png">![img](/en/guide/20230601131957.png)</a>

Parameters

| Parameter              | Description                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   |
| ---------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| Ticket                 | Unique data identifier.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| Creation Time          | The time when the data was stored.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |
| Transmission Direction | **Uplink Data:** The commands or messages that the device  reports to Developer Center.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| Sending Status         | **Pending:** When Developer Center sends downlink data  to the device through device debugging or API, and the device is offline with  a cache duration set, the status of the data will be shown as "**Pending**".  This indicates that the data will be delivered to the device if it goes  online, or sends uplink data within the cache duration. Note that the pending  status only applies to downlink data.   **Sent:** The "Sent" status for downlink data indicates that the  downlink data sent from Developer Center through device debugging or API has  been successfully delivered to the device and a response has been received by  Developer Center within 5 seconds. Similarly, the "Sent" status for  uplink data indicates that the data sent from the device has been  successfully delivered to Developer Center.   **Failed:** The "Failed" status for uplink data means that the device  has connected to the gateway successfully, but no subsequent action has been  performed, as a result of abnormal server or network. Similarly the  "Failed" status for downlink data indicates that the downlink data  sent from Developer Center through device debugging or API was not delivered  to the device, or a response was not received by Developer Center within 5  seconds. This could be due to network or server issues, or the device being  disconnected from the network. |
| Content                | Display the specific ID of the uplink  data with failed analysis. Exception data will not be sent to WebSocket or the  AMQP client.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |
| Sending Time           | The "Sending Time" of uplink  data indicates the time when Developer Center received the data sent from the  device.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          |

 
export const cmsConfig = {
  // 后端配置
  backend: {
    name: "gitlab",
    repo: "jfish.hu-group/cms",
    branch: "main",
    auth_type: "pkce",
    app_id: "50dcf72110ebadca8e3ed6ea755bd7bd9fe2fe4cc0df12b50b25add20c9886c7",
    app_secret: "gloas-6c639b9c61a2a991712bed49d0d5fa59e2d5e57c4575e2c169352cdecd8a0bab",
    base_url: "https://gitlab.com/",
    site_id: "localhost:5173",
  },

  // 本地后端配置（开发环境）- 启用以便调试子模块问题
  local_backend: true,

  // 媒体文件夹配置
  media_folder: "public/images",
  public_folder: "/images",

  // 集合配置
  collections: [
    {
      name: "introduction",
      label: "产品介绍文档",
      folder: "doc/docs/zh/introduction",
      create: true,
      slug: "{{slug}}",
      editor: {
        preview: true,
      },
      fields: [
        {
          label: "标题",
          name: "title",
          widget: "string",
          required: false,
          hint: "如果不填写，将从markdown内容中提取第一个标题"
        },
        {
          label: "描述",
          name: "description",
          widget: "text",
          required: false,
        },
        {
          label: "内容",
          name: "body",
          widget: "markdown",
          required: true
        },
      ],
    },
    {
      name: "quickstart",
      label: "快速开始文档",
      folder: "doc/docs/zh/quickStart",
      create: true,
      slug: "{{slug}}",
      editor: {
        preview: true,
      },
      fields: [
        {
          label: "标题",
          name: "title",
          widget: "string",
          required: false,
          hint: "如果不填写，将从markdown内容中提取第一个标题"
        },
        {
          label: "描述",
          name: "description",
          widget: "text",
          required: false,
        },
        {
          label: "内容",
          name: "body",
          widget: "markdown",
          required: true
        },
      ],
    },
    {
      name: "appDevelop",
      label: "应用开发文档",
      folder: "doc/docs/zh/appDevelop",
      create: true,
      slug: "{{slug}}",
      editor: {
        preview: true,
      },
      fields: [
        {
          label: "标题",
          name: "title",
          widget: "string",
          required: false,
          hint: "如果不填写，将从markdown内容中提取第一个标题"
        },
        {
          label: "描述",
          name: "description",
          widget: "text",
          required: false,
        },
        {
          label: "内容",
          name: "body",
          widget: "markdown",
          required: true
        },
      ],
    },
  ],
};

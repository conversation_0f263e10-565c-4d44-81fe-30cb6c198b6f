image: node:18 # 使用 Node.js 18 镜像

stages:
  - install
  - build
  - deploy

cache:
  key: ${CI_COMMIT_REF_SLUG}
  paths:
    - node_modules/

install_dependencies:
  stage: install
  script:
    - npm install
  only:
    - main # 仅在 main 分支运行

build_docs:
  stage: build
  script:
    - npm run docs:build # Vitepress 构建命令
  artifacts:
    paths:
      - public/ # 构建产物目录
  only:
    - main

deploy_to_pages:
  stage: deploy
  script:
    - mv public/ public_html/ # GitLab Pages 要求部署到 public_html
  artifacts:
    paths:
      - public_html/
  only:
    - main

# 定时任务

## 功能概述

设备定时管理, 支持创建、修改、删除、查询定时任务列表、查询定时任务详情等操作。

```kotlin
QuecDeviceService
```

## 定时管理 - 云定时

### 创建定时任务

**接口说明**

创建定时任务

```kotlin
fun addCornJob(
    cornJobModel: QuecCornJobModel,
    callback: QuecCallback<String>
)
```

**参数说明**

| 参数           | 	是否必传 | 说明                |	
|--------------|-------|-------------------|  
| cornJobModel | 是     | QuecCornJobModel类 |
| callback     | 是     | 请求回调              |

**QuecCornJobModel属性定义**

| 字段         | 类型                           | 描述                                                                                                               |
|------------|------------------------------|------------------------------------------------------------------------------------------------------------------|
| createTime | long                         | 创建时间                                                                                                             |
| productKey | String                       | 产品key                                                                                                            |
| deviceKey  | String                       | 设备key                                                                                                            |
| ruleId     | String                       | 规则唯一标识，修改规则实例信息时必填                                                                                               |
| type       | String                       | 定时任务类型，once: 执行一次，day-repeat: 每天重复，custom-repeat: 自定义重复，multi-section: 多段执行，random: 随机执行，delay: 延迟执行（倒计时）        |
| enabled    | boolean                      | 定时任务状态：false-停止（默认） true-启动                                                                                      |
| dayOfWeek  | String                       | 周几执行：1-周一 2-周二 3-周三 4-周四 5-周五 6-周六 7-周日, 可以多选，传多个值时使用英文逗号分隔, 当 type = custom-repeat / multi-section / random 时必填 |
| timers     | List&lt;QuecCornTimersModel> | 定时任务详细                                                                                                           |

**QuecCornTimersModel属性定义**

| 字段         | 类型            | 描述                                                                                |
|------------|---------------|-----------------------------------------------------------------------------------|
| action     | String        | 定时任务执行的命令，格式：物模型的 json 字符串                                                        |
| delay      | long          | 延迟执行时间，单位为秒, 当 type = delay 时必填，单位为 s                                             |
| endTime    | String        | 当 type 为 random 时必填，格式为 "HH:mm:ss"，如 "12:00:00"                                   |
| startTime  | String        | 当 type 为 random 时必填，格式为 "HH:mm:ss"，如 "12:00:00"                                   |
| time       | String        | 执行时间，格式为 HH:mm:ss, 当 type = once / day-repeat / custom-repeat / multi-section 时必填 |
| day-repeat | custom-repeat | multi-section 时必填                                                                 |
| timerId    | String        | 定时任务ID                                                                            |

**示例代码**

```kotlin
QuecDeviceService.addCornJob(
    QuecCornJobModel(
        System.currentTimeMillis(),
        "pk",
        "dk",
        "ruleId",
        "once",
        true,
        "1",
        listOf(
            QuecCornJobModel.QuecCornTimersModel(
                action = "[{\"modelCode\":\"switch\",\"modelName\":\"开关\",\"modelType\":\"PROPERTY\",\"dataType\":\"BOOL\",\"value\":\"true\",\"unit\":\"\"}]",
                delay = 0,
                timerId = "11:00:00"
            )
        )
    )
) {
    if (it.isSuccess) {
        val data = it.data //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 修改定时任务

**接口说明**

修改定时任务

```kotlin
fun setCronJob(
    cornJobModel: QuecCornJobModel,
    callback: QuecCallback<Unit>
)
```

**参数说明**

| 参数           | 	是否必传 | 说明                |	
|--------------|-------|-------------------|  
| cornJobModel | 是     | QuecCornJobModel类 |
| callback     | 是     | 请求回调              |

> QuecCornJobModel属性说明同上【创建定时任务】

**示例代码**

```kotlin
val job = getJob()  //云端获取到的任务
job.enabled = false
QuecDeviceService.setCronJob(job) {
    if (it.isSuccess) {
        //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 获取定时任务列表

**接口说明**

查询设备下定时任务列表

```kotlin
fun getCronJobList(
    deviceKey: String,
    productKey: String,
    type: String?,
    pageNumber: Int,
    pageSize: Int,
    callback: QuecCallback<QuecPageResponse<QuecCornJobModel>>
)
```

**参数说明**

| 参数         | 	是否必传 | 说明                                                                                                        |	
|------------|-------|-----------------------------------------------------------------------------------------------------------|  
| productKey | 是     | 产品key                                                                                                     |
| deviceKey  | 是     | 设备key                                                                                                     |
| type       | 否     | 定时任务类型，once: 执行一次，day-repeat: 每天重复，custom-repeat: 自定义重复，multi-section: 多段执行，random: 随机执行，delay: 延迟执行（倒计时） |
| pageNumber | 否     | 分页页码，默认: 1                                                                                                |
| pageSize   | 否     | 分页大小，默认: 10                                                                                               |
| callback   | 是     | 请求回调                                                                                                      |

> 回调QuecCornJobModel属性说明同上【创建定时任务】

**示例代码**

```kotlin
QuecDeviceService.getCronJobList("dk", "pk", null, 1, 10) {
    if (it.isSuccess) {
        val data = it.data //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 定时任务详情

**接口说明**

查询定时任务详情

```kotlin
fun getCronJobInfo(
    ruleId: String,
    callback: QuecCallback<QuecCornJobModel>
)
```

**参数说明**

| 参数       | 	是否必传 | 说明     |	
|----------|-------|--------|  
| ruleId   | 是     | 定时任务ID |
| callback | 是     | 请求回调   |

> 回调QuecCornJobModel属性说明同上【创建定时任务】

**示例代码**

```kotlin
QuecDeviceService.getCronJobInfo("ruleId") {
    if (it.isSuccess) {
        val data = it.data //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 批量删除定时任务

**接口说明**

批量删除定时任务

```kotlin
fun batchDeleteCornJob(
    ruleIdList: List<String>,
    callback: QuecCallback<QuecCornJobDeleteListModel>
)
```

**参数说明**

| 参数       | 	是否必传 | 说明              |	
|----------|-------|-----------------| 
| params   | 是     | String 定时任务ID数组 |
| callback | 是     | 请求回调            |

**QuecCornJobDeleteListModel属性定义**

| 字段          | 类型                              | 描述   |
|-------------|---------------------------------|------|
| successList | List&lt;QuecCornJobDeleteModel> | 成功列表 |
| failureList | List&lt;QuecCornJobDeleteModel> | 失败列表 |

**QuecCornJobDeleteModel属性定义**

| 字段     | 类型     | 描述     |
|--------|--------|--------|
| code   | String | code码  |
| msg    | String | 提示     |
| ruleId | String | 定时任务ID |

**示例代码**

```kotlin
QuecDeviceService.batchDeleteCornJob(listOf("ruleId")) {
    if (it.isSuccess) {
        val data = it.data //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 查询定时任务限制数

**接口说明**

查询产品下定时任务限制数

```kotlin
fun getProductCornJobLimit(
    productKey: String,
    callback: QuecCallback<Int>
)
```

**参数说明**

| 参数         | 	是否必传 | 说明   |	
|------------|-------|------|  
| productKey | 是     | 产品pk |
| callback   | 是     | 请求回调 |

**示例代码**

```kotlin
QuecDeviceService.getProductCornJobLimit("pk") {
    if (it.isSuccess) {
        val data = it.data //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

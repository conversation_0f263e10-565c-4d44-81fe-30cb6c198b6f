# **Error Code List**

| Enumeration Value| Description|
|----------|----------|
| -5888| Exceptions occurred for some server APIs. |
| -5889| Device messages are limited.|
| -5890| The device cannot be activated. |
| -5891| The device has been authenticated. |
| -5892| No product information is found via the ProductKey.|
| -5893| Password parsing error.|
| -5894| Sign error. |
| -5895| Rand is illegal. |
| -5896| The passed DeviceKey and ProductKey are inconsistent with those stored in the database. |
| -5897| DeviceKey is illegal. |
| -5898| Flag is illegal. |
| -5899| ClientID is not matched with the password. |
| -5900| DeviceSecret is incorrect. |
| -5901| Device information not found. |
| -5902| The gateway is not associated with the sub-device. |
| -5903| The sub-device is already online. |
| -5904| Device offline|
| -5905| Unknown error|
| -5906| Static devices cannot be authenticated. |
| -5907| Reinitiate the connection. |
| -5908| Actively close the connection. |
| -4352| Failed to get the configuration. |
| -4353| Device not authenticated. |
| -4354| Device connection failed. |
| -4355| Device reset failed. |
| -4356| Failed to delete the device. |
| -4357| Device connected. |
| -4358| Device reset error (New DeviceSecret has been received for the device with unique-certificate-per-device authentication). |
| -17920| MQTT internal error. |
| -17921| MQTT network error. |
| -17922| MQTT keep-alive interval error. |
| -17923| MQTT subscription error. |
| -17924| MQTT connection error. |
| -17925| Unsupported MQTT protocol. |
| -17926| MQTT identifier rejected. |
| -17927| Unavailable MQTT server. |
| -17928| Incorrect MQTT username or password. |
| -17929| MQTT unauthorized. |
| -17930| URL parsing failed. |
| -17931| Failed to get the module’s IP address. |
| -17932| Failed to build data packets. |
| -17933| Failed to send data packets. |
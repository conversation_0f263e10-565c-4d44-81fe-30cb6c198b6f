# Quecthing Revision History

## Version 2.2.0
>* Added gateway sub-device functionality
>* Added Modbus functionality
>* Added cloud configuration functionality

## Version 1.0.0
>* DMP-MQTT authentication and access  
>* Module basic information and status acquisition and reporting  
>* Business function - transparent data transmission (uplink and downlink)  
>* Business function - TSL model reporting, distribution, platform reading and device response  
>* Business function - reporting of built-in positioning data and external positioning data  
>* Business function - NTP time and time zone acquisition
>* Support for one-device-one-secret platform connection
>* Support for FOTA and SOTA  
>* Support for TLS encrypted communication
>* Support for near-field communication
>* Support for globalization and online product switching  
>* Support for external setting of device DK and DS
>* Support for device binding functionality
>* Support for device reset (reset device DS and binding information)
>* Support for feature reduction
>* Support for PSK
>* Support for domain name guidance function

# Device Information

This section outlines device information on the "Device Maintenance" page, including basic information and module information.

## **Prerequisites**

● A product is created.<br />
● The steps from Define Feature to Debug Device have been completed.

## **Procedures**

1.Log in to Developer Center and select "Device Maintenance" from the "Device Management" drop-down box in the left-hand menu bar. Once you are on the "Device Maintenance" page, click "View" in the "Action" column to navigate to the "Device Information" tab.

<a data-fancybox title="img" href="/en/guide/image2022-3-8_19-6-34.png">![img](/en/guide/image2022-3-8_19-6-34.png)</a>

<a data-fancybox title="img" href="/en/guide/image2022-3-8_19-7-31.png">![img](/en/guide/image2022-3-8_19-7-31.png)</a>

**Basic Information**

| **Parameter**             | **Description**                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |
| ------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| Associated   Product      | Name of the   product to which the device is associated                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |
| Device Status             | Online/Offline                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  |
| Device Name               | The default   value is the same as DeviceKey. It can be edited.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |
| DeviceKey                 | DeviceKey is   the customized unique identifier for devices under a product used for connecting   to Development Center.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
| DeviceSecret              | DeviceSecret is   issued by Developer Center after the device is certificated                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   |
| Authentication   Type     | Authentication   type when the device accesses Developer Center.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                |
| Certificate   Fingerprint | Device   certificate identifier, which is used to verify the connection permission of   the device. After a successful bidirectional authentication between the   device and Developer Center, the certificate fingerprint will be bound to the   device. Subsequent connections to Developer Center can only be made by using   this device certificate.<br />  To calculate the device certificate fingerprint, use SHA-1 hashing, such as   openssl x509 -fingerprint -sha1 -in certfile.crt. The fingerprint generated   by OpenSSL includes colons ":" and must be converted to a 40-bit   hexadecimal string without the colons before uploading.<br />   To update the device certificate fingerprint, you can reset the fingerprint   and set a new value. Once the new fingerprint is set successfully, the device   can use it to authenticate with Developer Center. |
| Device Type               | Directly   Connected Device, Gateway, Gateway sub-device                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
| Connection   Protocol     | Protocol used   by the device to connect to Developer Center. MQTT and LwM2M are currently supported.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |
| Creation Time             | The time when   the device is added to Developer Center                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |
| Previous   Online Time    | The last time   the device was online                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |
| Device SN                 | ID of the   device bound to App. It can be edited.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              |

**More Device Information** includes module information and device status.

| **Parameter**         | **Description**                                                |
| :-------------------- | :------------------------------------------------------------- |
| Device ID             | Module’s IMEI or MAC address.                                  |
| Module Type           |                                                                |
| Module Version        |                                                                |
| MCU Version           |                                                                |
| ICCID                 |                                                                |
| MCC                   | Mobile country code                                            |
| MNC                   | Mobile network code                                            |
| Positioning   Support | Whether positioning feature is supported                       |
| Protocol Version      | Data protocol version between the module and  Developer Center |
| SDK Version           | QuecThing version                                              |

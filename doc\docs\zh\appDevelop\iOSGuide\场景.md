# 场景SDK:QuecSceneSdk

## 功能概述

SDK支持用户按照一定的规则, 使一个或者多个设备执行多个任务

```objc
#import <QuecSceneKit/QuecSceneKit.h>

[QuecSceneService sharedInstance]
```

## SDK集成方式

> **注意**
>
> 场景SDK运行依赖核心库QuecIotSdk, 请先按照快速集成文档集成核心库


```objc
pod 'QuecSceneKit', '~> 0.4.0'
```

## 场景管理

### 获取场景列表

**接口说明**

获取当前场景的场景列表

```objc
- (void)getSceneListWithPageNumber:(NSInteger)pageNumber
                          pageSize:(NSInteger)pageSize
                           success:(void(^)(NSArray<QuecSceneModel *> *list, NSInteger total))success
                           failure:(QuecErrorBlock)failure;
```

**参数说明**

| 参数         | 是否必传 | 说明               |
|------------|------|------------------|
| pageNumber | 是    | 分页号              |
| pageSize   | 是    | 每一页的大小           |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	|

**示例代码**

```objc
 [QuecSceneService.sharedInstance getSceneListWithPageNumber:1
                                                    pageSize:10
                                                     success:^(NSArray<QuecSceneModel *> * _Nonnull list, NSInteger total) {
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

**QuecSceneModel属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| fid       | String             | 家庭id    |
| isCommon  | boolean            | 是否是常用场景 |
| sceneInfo | QuecSceneInfoModel | 场景详情    |

**QuecSceneInfoModel属性定义**

| 字段            | 类型                         | 描述       |
|---------------|----------------------------|----------|
| executeResult | int                        | 上次场景执行结果 |
| icon          | String                     | 场景图标     |
| id            | int                        | 场景执行结果   |
| metaDataList  | List&lt;QuecMetaDataModel> | 场景设备动作列表 |
| name          | String                     | 场景名称     |
| sceneId       | String                     | 场景Id     |

**QuecMetaDataModel属性定义**

| 字段         | 类型                                | 描述                 |
|------------|-----------------------------------|--------------------|
| actionList | NSArray<QuecSceneActionModel *> * | 设备动作列表             |
| deviceKey  | String                            | 设备dk               |
| productKey | String                            | 设备pk               |
| deviceName | String                            | 设备名称               |
| logoImage  | String                            | 设备icon             |
| deviceType | int                               | 设备类型 1 默认普通设备 2 群组 |

**QuecSceneActionModel属性定义**

| 字段       | 类型     | 描述          |
|----------|--------|-------------|
| id       | int    | 物模型功能ID     |
| code     | String | 物模型标志符      |
| dataType | String | 物模型数据类型     |
| actionId | int    | 执行动作Id      |
| name     | String | 物模型功能名称     |
| subName  | String | 物模型值subName |
| type     | String | 物模型功能类型     |
| subType  | String | 物模型subType  |

### 添加场景

**接口说明**

添加一个新的场景

```objc
- (void)addSceneWithSceneModel:(QuecSceneModel *)sceneModel success:(QuecVoidBlock)success failure:(QuecErrorBlock)failure;
```

**参数说明**

| 参数         | 是否必传 | 说明   |
|------------|------|------|
| sceneModel | 是    | 场景属性 |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	|

**示例代码**

```objc
QuecSceneModel *model = QuecSceneModel.new;
QuecSceneInfoModel *sceneInfoModel = QuecSceneInfoModel.new;
QuecMetaDataModel *metaDataModel = QuecMetaDataModel.new;
QuecSceneActionModel *actionModel = QuecSceneActionModel.new;

model.isCommon = YES;
sceneInfoModel.name = "设备开灯";
sceneInfoModel.icon = "https://xxxxxxxx.png";

actionModel.code = "switch";
actionModel.dataType = "BOOL";
actionModel.itemId = 1;
actionModel.name = "开关";
actionModel.subName = "开灯";
actionModel.subType = "RW";
actionModel.type = "PROPERTY";
actionModel.value = YES

metaDataModel.productKey = "pk";
metaDataModel.productKey = "dk";
metaDataModel.deviceType = 1;

model.sceneInfo = sceneInfoModel
sceneInfoModel.metaDataList = @[metaDataModel]
metaDataModel.actionList = @[actionModel]

[QuecSceneService.sharedInstance addSceneWithSceneModel:model success:^{
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 修改场景

**接口说明**

编写场景中的信息

```objc
- (void)editSceneWithSceneModel:(QuecSceneModel *)sceneModel success:(QuecVoidBlock)success failure:(QuecErrorBlock)failure;
```

**参数说明**

| 参数         | 是否必传 | 说明   |
|------------|------|------|
| sceneModel | 是    | 场景属性 |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	|

**示例代码**

```objc
QuecSceneModel *model = QuecSceneModel.new; //从场景列表获取到的对象
model.sceneInfo.name = "新名称";
[QuecSceneService.sharedInstance editSceneWithSceneModel:model success:^{
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 删除场景

**接口说明**

删除指定的场景

```objc
- (void)deleteSceneWithSceneId:(NSString *)sceneId success:(QuecVoidBlock)success failure:(QuecErrorBlock)failure;
```

**参数说明**

| 参数       | 是否必传 | 说明               |
|----------|------|------------------|
| sceneId  | 是    | 场景id             |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	|

**示例代码**

```objc
[QuecSceneService.sharedInstance deleteSceneWithSceneId:@"your SceneId" success:^{
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 获取场景详情

**接口说明**

获取指定场景的场景详情

```objc
- (void)getSceneInfoWithSceneId:(NSString *)sceneId success:(void(^)(QuecSceneModel * scene))success failure:(QuecErrorBlock)failure;
```

**参数说明**

| 参数       | 是否必传 | 说明               |
|----------|------|------------------|
| sceneId  | 是    | 场景id             |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	|

**示例代码**

```objc
[QuecSceneService.sharedInstance getSceneInfoWithSceneId:@"your SceneId" success:^(QuecSceneModel * _Nonnull scene) {
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

## 场景执行

### 执行场景

**接口说明**

执行指定的场景

```objc
- (void)executeSceneWithSceneId:(NSString *)sceneId
                        success:(void(^)(QuecActionExecuteResultModel *executeResultModel))success
                        failure:(QuecErrorBlock)failure;
```

**参数说明**

| 参数       | 是否必传 | 说明               |
|----------|------|------------------|
| sceneId  | 是    | 场景id             |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	|

**示例代码**

```objc
[QuecSceneService.sharedInstance executeSceneWithSceneId:@"your SceneId" success:^(QuecActionExecuteResultModel *executeResultModel) {
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

**QuecActionExecuteResultModel属性定义**

| 字段             | 类型                           | 描述     |
|----------------|------------------------------|--------|
| executeResult  | boolean                      | 执行结果   |
| failActionList | List&lt;QuecFailActionModel> | 失败结果列表 |
| failCount      | int                          | 失败结果数  |
| successCount   | int                          | 成功结果数  |

**QuecFailActionModelModel属性定义**

| 字段         | 类型     | 描述     |
|------------|--------|--------|
| productKey | String | 设备pk   |
| deviceKey  | String | 设备dk   |
| deviceName | String | 设备名    |
| imageLogo  | String | 设备logo |
| reason     | String | 原因     |

### 测试场景

**接口说明**

测试场景配置的执行情况, 在未保存场景状态到云端的情况下, 也可执行

```objc
- (void)executeTestSceneWithSceneModel:(QuecSceneModel *)sceneModel
                               success:(void(^)(QuecActionExecuteResultModel *executeResultModel))success
                               failure:(QuecErrorBlock)failure;
```

**参数说明**

| 参数         | 是否必传 | 说明   |
|------------|------|------|
| sceneId  | 是    | 场景id             |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	|

**示例代码**

```objc
QuecSceneModel *sceneModel = QuecSceneModel.new;
[QuecSceneService.sharedInstance executeTestSceneWithSceneModel:sceneModel success:^(QuecActionExecuteResultModel * _Nonnull executeResultModel) {
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

## 常用场景

### 获取常用场景列表

**接口说明**

获取常用场景列表

```objc
- (void)getCommonSceneListWithPageNumber:(NSInteger)pageNumber
                                pageSize:(NSInteger)pageSize
                                 success:(void(^)(NSArray<QuecSceneModel *> *list, NSInteger total))success
                                 failure:(QuecErrorBlock)failure;
```

**参数说明**

| 参数         | 是否必传 | 说明               |
|------------|------|------------------|
| pageNumber | 是    | 分页号              |
| pageSize   | 是    | 每一页的大小           |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	|

**示例代码**

```objc
[QuecSceneService.sharedInstance getCommonSceneListWithPageNumber:1
                                                             pageSize:10
                                                              success:^(NSArray<QuecSceneModel *> * _Nonnull list, NSInteger total) {
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 批量添加常用场景

**接口说明**

批量添加常用场景

```objc
- (void)batchAddCommonSceneWithSceneList:(NSArray *)sceneList
                                 success:(void(^)(QuecAddCommonSceneIdModel *model))success
                                 failure:(QuecErrorBlock)failure;
```

**参数说明**

| 参数          | 是否必传 | 说明               |
|-------------|------|------------------|
| sceneIdList | 是    | 场景id列表，必传        |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	|

**示例代码**

```objc
[QuecSceneService.sharedInstance batchAddCommonSceneWithSceneList:@[@"SceneID1", @"SceneID2"] success:^(QuecAddCommonSceneIdModel * _Nonnull model) {
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 批量删除常用场景

**接口说明**

批量删除常用场景

```objc
- (void)batchDeleteCommonSceneWithSceneList:(NSArray *)sceneList
                                    success:(void(^)(QuecAddCommonSceneIdModel *model))success
                                    failure:(QuecErrorBlock)failure;
```

**参数说明**

| 参数          | 是否必传 | 说明               |
|-------------|------|------------------|
| sceneIdList | 是    | 场景id列表，必传        |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	|

**示例代码**

```objc
[QuecSceneService.sharedInstance batchDeleteCommonSceneWithSceneList:@[@"SceneID1", @"SceneID2"] success:^(QuecAddCommonSceneIdModel * _Nonnull model) {
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

## 场景日志

### 获取场景日志列表

**接口说明**

获取场景日志列表

```objc
- (void)getSceneLogListWithLastExecutionId:(long)lastExecutionId
                                     limit:(NSInteger)limit
                                   success:(void(^)(NSArray<QuecSceneLogItemModel *> *list))success
                                   failure:(QuecErrorBlock)failure;
```

**参数说明**

| 参数              | 是否必传 | 说明               |
|-----------------|------|------------------|
| lastExecutionId | 否    | 最后一条执行日志的id      |
| limit           | 是    | 查询的数据数量          |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	|

**示例代码**

```objc
[QuecSceneService.sharedInstance getSceneLogListWithLastExecutionId:10086
                                                                limit:10
                                                            success:^(NSArray<QuecSceneLogItemModel *> * _Nonnull list) {
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

**QuecSceneLogItemModel属性定义**

| 字段              | 类型                                  | 描述     |
|-----------------|-------------------------------------|--------|
| sceneId         | String                              | 场景ID   |
| sceneName       | String                              | 场景名称   |
| sceneIcon       | String                              | 场景icon |
| executionId     | long                                | 执行id   |
| executionTime   | long                                | 执行时间   |
| executionResult | int                                 | 执行结果   |
| executionList   | List&lt;QuecSceneLogExecutionModel> | 执行结果列表 |

**QuecSceneLogExecutionModel属性定义**

| 字段               | 类型                            | 描述     |
|------------------|-------------------------------|--------|
| productKey       | String                        | 设备pk   |
| deviceKey        | String                        | 设备dk   |
| deviceName       | String                        | 设备名称   |
| logoImage        | String                        | 设备logo |
| executionResult  | String                        | 执行结果   |
| actionResultList | List&lt;QuecSceneLogActionModel> | 场景ID   |

**QuecSceneLogActionModel属性定义**

| 字段         | 类型                   | 描述   |
|------------|----------------------|------|
| result     | boolean              | 执行结果 |
| reason     | String               | 原因   |
| createTime | String               | 时间戳  |
| action     | QuecSceneActionModel | 物模型  |

### 获取场景日志详情

**接口说明**

获取场景日志详情

```objc
- (void)getSceneLogDetailInfoWithExecutionId:(long)executionId
                                     success:(void(^)(QuecSceneLogItemModel *detailInfo))success
                                     failure:(QuecErrorBlock)failure;
```

**参数说明**

| 参数          | 是否必传 | 说明     |
|-------------|------|--------|
| executionId | 是    | 场景日志id |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	|

**示例代码**

```objc
[QuecSceneService.sharedInstance getSceneLogDetailInfoWithExecutionId:10010 success:^(QuecSceneLogItemModel *detailInfo) {
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 清除场景日志

**接口说明**

清除场景日志

```objc
- (void)clearSceneLogWithSuccess:(QuecVoidBlock)success failure:(QuecErrorBlock)failure;
```

**参数说明**

| 参数  | 是否必传 | 说明               |
|-----|------|------------------|
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	|

**示例代码**

```objc
[QuecSceneService.sharedInstance clearSceneLogWithSuccess:^{
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

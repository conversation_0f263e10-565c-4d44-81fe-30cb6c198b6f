# 成员信息

## 功能概述

本文介绍了如何管理家庭中的成员, 包括添加成员, 修改成员信息, 移除成员, 离开家庭, 查询家庭成员列表等。

## 成员管理

### 邀请家庭成员

**接口说明**

邀请家庭成员

```objc
- (void)inviteFamilyMemberWithModel:(QuecInviteFamilyMemberParamModel *)inviteModel
                            success:(QuecVoidBlock)success
                            failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- | 
| inviteModel |	是|邀请信息模型| 
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**QuecInviteFamilyMemberParamModel属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| fid       | NSString             | 家庭Id，邀请成员时必填 |
| memberRole       | NSString             | 成员角色：2-管理员  3-普通成员，邀请成员时必填 |
| memberName       | NSString             | 成员名称，邀请成员时非必填 |
| invalidTime       | NSInteger             |  邀请失效时间，毫秒时间戳，邀请成员时必填 |
| phone       | NSString             | 手机号，邀请成员时非必填 |
| email       | NSString             | 邮箱，邀请成员时非必填 |
| uid       | NSString             | 用户Id，邀请成员时非必填 |

**示例代码**

```objc
QuecInviteFamilyMemberParamModel *inviteModel = QuecInviteFamilyMemberParamModel.new;
inviteModel.fid = @"your fid";
inviteModel.memberRole = @"3";
inviteModel.memberName= @"member name";
inviteModel.invalidTime = 1745234233467;
[QuecSmartHomeService.sharedInstance inviteFamilyMemberWithModel:inviteModel success:^{
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 家庭成员邀请的处理

**接口说明**

家庭成员邀请的处理, 其他用户邀请加入他的家庭后, 被邀请成员调用.

```objc
- (void)familyMemberInviteHandleWithFid:(NSString *)fid
                                 decide:(NSInteger)decide
                                success:(QuecVoidBlock)success
                                failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- | 
| fid |	是|家庭Id| 
| decide |	是|对于邀请的决定：0-拒绝邀请   1-同意邀请，必填| 
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**示例代码**

```objc
[QuecSmartHomeService.sharedInstance familyMemberInviteHandleWithFid:@"your fid" decide:1 success:^{
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 修改家庭成员名称

**接口说明**

修改家庭成员名称

```objc
- (void)setFamilyMemberNameWithFid:(NSString *)fid
                         memberUid:(NSString *)memberUid
                        memberName:(NSString *)memberName
                           success:(QuecVoidBlock)success
                           failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- | 
| fid |	是|家庭Id| 
| memberUid |	是|家庭成员用户Id| 
| memberName |	是|成员名称| 
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**示例代码**

```objc
[QuecSmartHomeService.sharedInstance setFamilyMemberNameWithFid:@"your fid"
                                                        memberUid:@"member uid"
                                                        memberName:@"member name"
                                                        success:^{
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 修改家庭成员角色

**接口说明**

修改家庭成员角色

```objc
- (void)setFamilyMemberRoleWithFid:(NSString *)fid
                         memberUid:(NSString *)memberUid
                        memberRole:(NSString *)memberRole
                           success:(QuecVoidBlock)success
                           failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- | 
| fid |	是|家庭Id| 
| memberUid |	是|家庭成员用户Id| 
| memberRole |	是|成员角色：2-管理员  3-普通成员|  
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**示例代码**

```objc
[QuecSmartHomeService.sharedInstance setFamilyMemberRoleWithFid:@"your fid"
                                                          memberUid:@"member uid"
                                                         memberRole:@"member name"
                                                            success:^{
        /// Next Action
    } failure:^(NSError *error) {
        NSLog(@"check error: %@", error);
    }];
```

### 移除家庭成员

**接口说明**

移除家庭成员

```objc
- (void)deleteFamilyMemberWithFid:(NSString *)fid
                        memberUid:(NSString *)memberUid
                          success:(QuecVoidBlock)success
                          failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- | 
| fid |	是|家庭Id| 
| memberUid |	是|家庭成员用户Id| 
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**示例代码**

```objc
[QuecSmartHomeService.sharedInstance deleteFamilyMemberWithFid:@"your fid"
                                                          memberUid:@"member uid"
                                                            success:^{
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 离开家庭

**接口说明**

家庭成员离开当前家庭

```objc
- (void)leaveFamilyWithFid:(NSString *)fid
                   success:(QuecVoidBlock)success
                   failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- | 
| fid |	是| 家庭Id| 
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**示例代码**

```objc
[QuecSmartHomeService.sharedInstance leaveFamilyWithFid:@"your fid"
                                                    success:^{
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 家庭中的家庭成员列表

**接口说明**

查询家庭中的家庭成员列表

```objc
- (void)getFamilyMemberListWithFid:(NSString *)fid
                        pageNumber:(NSInteger)pageNumber
                          pageSize:(NSInteger)pageSize
                           success:(void(^)(NSArray<QuecFamilyMemberItemModel *> *list, NSInteger total))success
                           failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- | 
| fid |	是|家庭id| 
| pageNumber |	否|页码，非必填，默认1| 
| pageSize |	否|页大小，非必填，默认10| 
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**QuecFamilyMemberItemModel属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| uid       | NSString             | 用户Id |
| phone       | NSString             | 手机号 |
| nikeName       | NSString             | 昵称 |
| sex       | NSString             | 性别 |
| address       | NSString             | 地址 |
| email       | NSString             | 邮箱 |
| headimg       | NSString             | 头像 |
| wechatMiniprogramUserId       | NSString             | 小程序Id |
| wechatUnionId       | NSString             | 微信Id |
| appleUserId       | NSString             | apple Id |
| twitterUserId       | NSString             | twitter Id |
| facebookUserId       | NSString             | facebook Id |
| alipayUserId       | NSString             | alipay Id |
| qqUserId       | NSString             | qq Id |
| wechatOffiaccountUserId       | NSString             | wechatOffiaccount Id |
| registerTime       | NSString             | 注册时间 |
| registerTimeTs       | NSInteger             | 注册时间戳 |
| lastLoginTime       | NSString             | 上次登录时间 |
| lastLoginTime       | NSString             | 上次登录时间戳 |
| timezone       | NSString             | 时区 |
| nationality       | NSString             | 国家 |
| province       | NSString             | 省 |
| city       | NSString             | 市 |
| lang       | NSString             | 语言 |
| status       | NSInteger             | 状态 |
| signature       | NSString             | 签名 |
| remark       | NSString             | 备注 |
| memberRole       | NSInteger             | 角色类型 |
| memberName       | NSString             | 名称 |

**示例代码**

```objc
[QuecSmartHomeService.sharedInstance getFamilyMemberListWithFid:@"your fid"
                                                         pageNumber:1
                                                           pageSize:10
                                                            success:^(NSArray<QuecFamilyMemberItemModel *> *list, NSInteger total) {
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 被邀请列表

**接口说明**

查询被邀请列表

```objc
- (void)getFamilyInviteListWithPageNumber:(NSInteger)pageNumber
                                 pageSize:(NSInteger)pageSize
                                  success:(void(^)(NSArray <QuecInviteItemModel *> *list, NSInteger total))success
                                  failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- | 
| pageNumber |	否|页码，非必填，默认1| 
| pageSize |	否|页大小，非必填，默认10| 
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

>QuecInviteItemModel属性定义同上

**示例代码**

```objc
[QuecSmartHomeService.sharedInstance getFamilyInviteListWithPageNumber:1
                                                                  pageSize:10
                                                                   success:^(NSArray<QuecInviteItemModel *> *list, NSInteger total) {
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

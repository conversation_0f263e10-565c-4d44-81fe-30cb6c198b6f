# 纯蓝牙设备添加

## 功能概述

纯蓝牙设备本身不具有直接连上云的能力, 只能由App近场绑定然后近场使用, 添加过程中不需要输入路由器的名称和密码

```objc
QuecDevicePairingService
```

设备添加流程

1. 添加配网监听器
2. 调用开始发现设备接口, 并通过配网监听器回调获取发现的设备
3. 选择发现的设备, 调用接口开始添加
4. 通过配网监听器回调, 获取设备添加状态(成功/失败)
5. 完成设备添加后, 移除配网监听器

## 功能列表

### 开始发现设备

**接口说明**

通过该接口可以发现蓝牙设备, 注意调用该接口前, 需先申请蓝牙权限

```objc
- (void)scanWithFilier:(QuecBleFilterModel * _Nullable)filter;
```

**参数说明**

| 参数   | 是否必传 | 说明            |
|------|------|---------------|
| filter | 否    | 过滤条件    |

**QuecBleFilterModel属性定义**

| 字段              | 类型            | 描述                             |
|-----------------|---------------|--------------------------------|
| mac      | String        | ble mac |
| pattern     | String        | name 正则规则 |
| filterType     | QuecBleFilterType        | filter type |

**QuecBleFilterType枚举定义**

```objc
typedef enum : NSUInteger {
    QuecBleFilterTypeMacMatch, // mac地址匹配
    QuecBleFilterTypeNamePredicate, // name正则匹配
} QuecBleFilterType;
```

**示例代码**

```objc
QuecBleFilterModel *filter = QuecBleFilterModel.new;
filter.pattern = @"Quec";
filter.filterType = QuecBleFilterTypeNamePredicate;
[QuecDevicePairingService.sharedInstance scanWithFilier:filter];
```

### 停止发现设备

**接口说明**

停止发现设备, 在发现到设备后, 或退出添加时, 必须停止发现设备, 否则会影响设备列表中设备正常的连接

```objc
- (void)stopScan;
```

**示例代码**

```objc
[QuecDevicePairingService.sharedInstance stopScan];
```

### 添加配网监听器

**接口说明**

通过该接口可以添加配网监听器, 监听配网过程, 包括扫描到设备, 更新添加进度, 配网结果等

```objc
- (void)addPairingListener:(id<QuecPairingDelegate>)listener;
```

**参数说明**

| 参数       | 是否必传 | 说明    |
|----------|------|-------|
| listener | 是    | 配网监听器 |

**QuecPairingDelegate接口定义**

```objc
@protocol QuecPairingDelegate <NSObject>
@optional
/// pairing progress. 0-1 
- (void)didUpdatePairingStatus:(QuecPairingPeripheral *)pairingPeripheral progress:(CGFloat)progress;
/// pairing result. only resp once
- (void)didUpdatePairingResult:(QuecPairingPeripheral *)pairingPeripheral result:(BOOL)result error:(NSError *)error;
@required
/// scan resp
- (void)centralDidDiscoverPairingPeripheral:(QuecPairingPeripheral *)pairingPeripheral;
@end
```

**示例代码**

```objc
[QuecDevicePairingService.sharedInstance addPairingListener:self];
```

**QuecPairingPeripheral属性定义**

| 字段              | 类型            | 描述                             |
|-----------------|---------------|--------------------------------|
| deviceName      | String        | 设备名称                           |
| productName     | String        | 产品名称                           |
| productLogo     | String        | 产品logo                         |
| bindingMode     | Int           | 绑定模式 1: 多绑 ,2: 单绑 ,3: 轮流绑      |
| activeBluetooth | boolean       | 优先蓝牙激活开关配置：true: 开启，false: 未开启 |
| peripheralModel       | QuecPeripheralModel | 外设信息                           |

**QuecPeripheralModel属性定义**

| 字段                  | 类型        | 描述   |
|---------------------|-----------|------------------------|
| deviceId                  | String    |  设备唯一标识，pk 和 dk共同决定   |
| pk          | String    | 设备productKey   |
| dk           | String    | 设备deviceKey        |
| mac                 | String    | 设备mac地址 |
| name                 | String    | 设备名称 |
| capabilitiesBitmask | int       | 设备能力值 <br/>bit0=1 表示设备支持 WAN 远场通讯能力 <br/>bit1=1 表示设备支持 WiFi LAN 近场通讯能力 <br/>bit2=1 表示设备支持 BLE 近场通讯能力 <br/>bit3=1 表示设备支持 Matter 近场通讯能力 |
| bindStatus                | int | 绑定状态    |
| endpoint                | int | 云端endpoint类型    |
| peripheral                | CBPeripheral | peripheral    |
| isConfig                | BOOL | 是否已配网    |
| isBind                | BOOL | Pure Bt是否已绑定    |
| isEnableBind                | BOOL |设备是否可绑定    |
| protocolVer                | int | 协议版本    |
| customData                | NSData | 自定义字段    |

### 移除配网监听器

**接口说明**

移除配网监听器, 在结束添加后, 必须移除监听器, 否则会导致内存泄漏

```objc
fun removePairingListener(listener: QuecPairingListener?)
```

**参数说明**

| 参数       | 是否必传 | 说明    |
|----------|------|-------|
| listener | 是    | 配网监听器 |

**示例代码**

```objc
QuecDevicePairingService.removePairingListener(listener)
```

### 开始添加设备

**接口说明**

开始添加设备, 在发现设备后, 可传入设备信息进行添加

```objc
- (void)startPairingWithDevices:(NSArray<QuecPairingPeripheral *> *)pairingDevices ssid:(NSString *)ssid pwd:(NSString *)pwd;
```

**参数说明**

| 参数      | 是否必传 | 说明                    |
|---------|------|-----------------------|
| pairingDevices | 是    | 发现的设备信息, 可传多个进行添加     |
| ssid    | 否    | 路由器名称 , 纯蓝牙设备添加时传null |
| pwd      | 否    | 路由器密码 ,纯蓝牙设备添加时传null  |   

**示例代码**

```objc
// 传入搜索发现的设备信息
[QuecDevicePairingService.sharedInstance startPairingWithDevices:@[QuecPairingPeripheral.new] ssid:@"ssid" pwd:@"ssid pwd"];
```

### 停止添加设备

**接口说明**

停止添加设备, 对于未添加成功的设备, 可调用此接口停止添加设备

```objc
- (void)cancelAllDevicePairing;
```

**示例代码**

```objc
[QuecDevicePairingService.sharedInstance cancelAllDevicePairing];
```

### 设置蓝牙绑定超时时间

**接口说明**

设置蓝牙绑定超时时间(30~60,默认60秒)

```objc
- (BOOL)setBlePairingDuration:(int)duration;
```

**参数说明**

| 参数       | 是否必传 | 说明       |
|----------|------|----------|
| duration | 是    | 超时时间,单位秒 |

**示例代码**

```objc
[QuecDevicePairingService.sharedInstance setBlePairingDuration:30];
```


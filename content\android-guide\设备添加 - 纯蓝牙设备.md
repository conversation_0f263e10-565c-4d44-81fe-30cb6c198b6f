---
title: "纯蓝牙设备添加"
description: "纯蓝牙设备添加相关文档"
---

# 纯蓝牙设备添加

## 功能概述

纯蓝牙设备本身不具有直接连上云的能力, 只能由App近场绑定然后近场使用, 添加过程中不需要输入路由器的名称和密码

```kotlin
QuecDevicePairingService
```

设备添加流程

1. 添加配网监听器
2. 调用开始发现设备接口, 并通过配网监听器回调获取发现的设备
3. 选择发现的设备, 调用接口开始添加
4. 通过配网监听器回调, 获取设备添加状态(成功/失败)
5. 完成设备添加后, 移除配网监听器

## 功能列表

### 开始发现设备

**接口说明**

通过该接口可以发现蓝牙设备, 注意调用该接口前, 需先申请以下动态权限

- android.permission.BLUETOOTH
- android.permission.BLUETOOTH_SCAN
- android.permission.BLUETOOTH_CONNECT
- android.permission.BLUETOOTH_ADMIN
- android.permission.ACCESS_FINE_LOCATION

```kotlin
fun scan(name: String?, mac: String?)
```

**参数说明**

| 参数   | 是否必传 | 说明            |
|------|------|---------------|
| name | 否    | 过滤条件: 设备名称    |
| mac  | 否    | 过滤条件: 设备mac地址 |

**示例代码**

```kotlin
QuecDevicePairingService.scan(null, null)
```

### 停止发现设备

**接口说明**

停止发现设备, 在发现到设备后, 或退出添加时, 必须停止发现设备, 否则会影响设备列表中设备正常的连接

```kotlin
fun stopScan()
```

**示例代码**

```kotlin
QuecDevicePairingService.stopScan()
```

### 添加配网监听器

**接口说明**

通过该接口可以添加配网监听器, 监听配网过程, 包括扫描到设备, 更新添加进度, 配网结果等

```kotlin
fun addPairingListener(listener: QuecPairingListener?)
```

**参数说明**

| 参数       | 是否必传 | 说明    |
|----------|------|-------|
| listener | 是    | 配网监听器 |

**QuecPairingListener接口定义**

```kotlin
interface QuecPairingListener {
x
    /**
     * 扫描到设备
     * @param deviceBean 设备信息
     */
    fun onScanDevice(deviceBean: QuecPairDeviceBean)
    
    /**
     * 更新添加进度
     * @param deviceBean 设备信息
     * @param progress 进度
     */
    fun onUpdatePairingStatus(deviceBean: QuecPairDeviceBean, progress: Float)

    /**
     * 配网结果
     * @param deviceBean 设备信息
     * @param result 配网结果
     * @param errorCode 错误码
     */
    fun onUpdatePairingResult(deviceBean: QuecPairDeviceBean, result: Boolean, errorCode: QuecPairErrorCode)
}
```

**示例代码**

```kotlin
val listener = object : QuecPairingListener {
        override fun onScanDevice(deviceBean: QuecPairDeviceBean) {
            //扫描到的设备
        }

        override fun onUpdatePairingResult(
            deviceBean: QuecPairDeviceBean,
            result: Boolean,
            errorCode: QuecPairErrorCode
        ) {
            //更新添加进度
        }

        override fun onUpdatePairingStatus(deviceBean: QuecPairDeviceBean, progress: Float) {
            //更新配网结果
        }
    }
    
QuecDevicePairingService.addPairingListener(listener)
```

**QuecPairDeviceBean属性定义**

| 字段              | 类型            | 描述                             |
|-----------------|---------------|--------------------------------|
| deviceName      | String        | 设备名称                           |
| productName     | String        | 产品名称                           |
| productLogo     | String        | 产品logo                         |
| bindingMode     | Int           | 绑定模式 1: 多绑 ,2: 单绑 ,3: 轮流绑      |
| activeBluetooth | boolean       | 优先蓝牙激活开关配置：true: 开启，false: 未开启 |
| bleDevice       | QuecBleDevice | 设备信息                           |

**QuecBleDevice属性定义**

| 字段                  | 类型        | 描述                                                                                                                                    |
|---------------------|-----------|---------------------------------------------------------------------------------------------------------------------------------------|
| id                  | String    | 设备id                                                                                                                                  |
| productKey          | String    | 设备productKey                                                                                                                          |
| deviceKey           | String    | 设备deviceKey                                                                                                                           |
| mac                 | String    | 设备mac地址                                                                                                                               |
| capabilitiesBitmask | int       | 设备能力值 <br/>bit0=1 表示设备支持 WAN 远场通讯能力 <br/>bit1=1 表示设备支持 WiFi LAN 近场通讯能力 <br/>bit2=1 表示设备支持 BLE 近场通讯能力 <br/>bit3=1 表示设备支持 Matter 近场通讯能力 |
| data                | ByteArray | 设备自定义数据                                                                                                                               |

### 移除配网监听器

**接口说明**

移除配网监听器, 在结束添加后, 必须移除监听器, 否则会导致内存泄漏

```kotlin
fun removePairingListener(listener: QuecPairingListener?)
```

**参数说明**

| 参数       | 是否必传 | 说明    |
|----------|------|-------|
| listener | 是    | 配网监听器 |

**示例代码**

```kotlin
QuecDevicePairingService.removePairingListener(listener)
```

### 开始添加设备

**接口说明**

开始添加设备, 在发现设备后, 可传入设备信息进行添加

```kotlin
fun startPairingByDevices(
    devices: MutableList<QuecPairDeviceBean>?, ssid: String?, pw: String?
)
```

**参数说明**

| 参数      | 是否必传 | 说明                    |
|---------|------|-----------------------|
| devices | 是    | 发现的设备信息, 可传多个进行添加     |
| ssid    | 否    | 路由器名称 , 纯蓝牙设备添加时传null |
| pw      | 否    | 路由器密码 ,纯蓝牙设备添加时传null  |             |

**示例代码**

```kotlin
val bean = QuecPairDeviceBean() //搜索发现的设备信息

QuecDevicePairingService.startPairingByDevices(
    mutableListOf(bean),
    null,
    null
)
```

### 停止添加设备

**接口说明**

停止添加设备, 对于未添加成功的设备, 可调用此接口停止添加设备

```kotlin
fun cancelAllDevicePairing()
```

**示例代码**

```kotlin
QuecDevicePairingService.cancelAllDevicePairing()
```

### 设置BLE配网超时时间

**接口说明**

设置BLE配网超时时间(30~60,默认60秒)

```kotlin
fun setBlePairingDuration(duration: Int): Boolean
```

**参数说明**

| 参数       | 是否必传 | 说明       |
|----------|------|----------|
| duration | 是    | 超时时间,单位秒 |

**示例代码**

```kotlin
QuecDevicePairingService.setBlePairingDuration(60)
```

# Overview

## **Introduction** 

> The QthTools-MCU Simulator is a simulation assistant that simulates the communication between the main control MCU and Quectel module. It has powerful features such as connecting to Developer Center, data interaction using AT commands, TSL, transparent transmission, OTA, and gateway sub-devices. The log debugging and log analysis features can effectively improve the user's functional understanding of the module or App, which facilitates verification and debugging of the module or App capabilities, thereby improving the efficiency of overall solution development and acceptance.

## **Tool Downloading**

<a href="https://core.acceleronix.io/download?menuCode=DEBUG_UTIL&resourceType=C" target="_blank">Download Center</a>

# 家庭信息

## 功能概述

本文介绍了如何对家庭进行管理, 包括创建家庭, 修改家庭信息, 删除家庭, 获取家庭列表等

## 通用信息

```kotlin
QuecSmartHomeService
```

**QuecSmartHomeService类属性说明**

| 字段               | 类型                           | 描述         |
|------------------|------------------------------|------------|
| enable           | BOOL                         | 家庭模式是否开启   |
| autoSwitch       | BOOL                         | 自动切换开关状态   |
| currentFamily    | QuecFamilyItemModel          | 用户当前家庭信息   |
| familys          | List&lt;QuecFamilyItemModel> | 用户所有家庭数据列表 |
| familyInviteList | List&lt;QuecInviteItemModel> | 用户被邀请家庭列表  |

**QuecFamilyItemModel属性定义**

| 字段                   | 类型                               | 描述                      |
|----------------------|----------------------------------|-------------------------|
| fid                  | String                           | 家庭Id                    |
| familyName           | String                           | 名称                      |
| familyLocation       | String                           | 家庭位置                    |
| familyCoordinates    | String                           | 家庭经纬度                   |
| addTime              | String                           | 添加时间                    |
| addTimeTs            | Int                              | 添加时间戳                   |
| memberRole           | Int                              | 角色,1-创建者  2-管理员  3-普通成员 |
| currentRoom          | QuecFamilyRoomItemModel          | 当前家庭下选中的房间信息            |
| rooms                | List&lt;QuecFamilyRoomItemModel> | 当前家庭下所有房间列表             |
| groupDeviceDeviceNum | Int                              | 群组包含设备数量                |
| deviceList           | List&lt;QuecGroupDeviceBean>     | 设备列表                    |

**QuecFamilyRoomItemModel属性定义**

| 字段       | 类型     | 描述   |
|----------|--------|------|
| frid     | String | 房间ID |
| roomName | String | 房间名称 |
| roomSort | String | 房间排序 |

**QuecInviteItemModel属性定义**

| 字段                | 类型     | 描述    |
|-------------------|--------|-------|
| fid               | String | 家庭Id  |
| familyName        | String | 名称    |
| familyLocation    | String | 家庭位置  |
| familyCoordinates | String | 家庭经纬度 |
| addTime           | String | 添加时间  |
| addTimeTs         | Int    | 添加时间戳 |
| invalidTime       | String | 邀请时间  |

## 家庭模式

### 家居模式开关

**接口说明**

启用停用家居模式, 开启后用户将处于家居模式

```kotlin
fun enabledFamilyMode(familyMode: Boolean, callback: QuecCallback<Unit>)
```

**参数说明**

| 参数         | 	是否必传 | 说明       |	
|------------|-------|----------| 
| familyMode | 	是    | 启用停用家居模式 | 
| callback   | 是     | 请求回调     |

**示例代码**

```kotlin
QuecSmartHomeService.enabledFamilyMode(true) {
    if (it.isSuccess) {
        //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 自动切换开关

**接口说明**

启用自动切换, 开启家居模式后设置. 根据位置自动切换连接家庭

```kotlin
fun enabledAutoSwitch(autoSwitch: Boolean, callback: QuecCallback<Unit>)
```

**参数说明**

| 参数         | 	是否必传 | 说明       |	
|------------|-------|----------| 
| autoSwitch | 	是    | 启用停用自动切换 | 
| callback   | 是     | 请求回调     |

**示例代码**

```kotlin
QuecSmartHomeService.enabledAutoSwitch(true) {
    if (it.isSuccess) {
        //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 获取家居模式配置

**接口说明**

查询用户的家居模式配置, 可用于判断当前家居模式是否开启

```kotlin
fun getFamilyModeConfig(callback: QuecCallback<QuecFamilyModeConfigModel>)
```

**参数说明**

| 参数       | 	是否必传 | 说明   |	
|----------|-------|------| 
| callback | 是     | 请求回调 |

**QuecFamilyModeConfigModel属性定义**

| 字段                | 类型   | 描述       |
|-------------------|------|----------|
| enabledAutoSwitch | BOOL | 自动切换是否开启 |
| enabledFamilyMode | BOOL | 家庭模式是否开启 |

**示例代码**

```kotlin
QuecSmartHomeService.getFamilyModeConfig {
    if (it.isSuccess) {
        val data = it.data //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

## 家庭管理

### 创建家庭

**接口说明**

创建家庭

```kotlin
fun addFamily(familyParamModel: QuecFamilyParamModel, callback: QuecCallback<Unit>)
```

**参数说明**

| 参数               | 	是否必传 | 说明   |	
|------------------|-------|------| 
| familyParamModel | 	是    | 家庭信息 | 
| callback         | 是     | 请求回调 |

**QuecFamilyParamModel属性定义**

| 字段                | 类型              | 描述                                          |
|-------------------|-----------------|---------------------------------------------|
| fid               | String          | 家庭Id,更改家庭信息和删除家庭时必传，创建家庭不用传                 |
| familyName        | String          | 名称，创建家庭时该参数必传，更改家庭非必传                       |
| familyLocation    | String          | 家庭位置，非必传                                    |
| familyCoordinates | String          | 家庭经纬度，WGS84坐标系，格式：40.759186,-73.928204, 非必传 |
| familyRoomList    | List&lt;String> | 房间列表,非必传                                    |

**示例代码**

```kotlin
QuecSmartHomeService.addFamily(
    QuecFamilyParamModel(
        null, "familyName",
        "location name", "110.53263738426125,25.668388834428706", listOf("room1")
    )
) {
    if (it.isSuccess) {
        val data = it.data //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 修改家庭信息

**接口说明**

修改家庭信息

```kotlin
fun setFamily(familyParamModel: QuecFamilyParamModel, callback: QuecCallback<Unit>)
```

**参数说明**

| 参数               | 	是否必传 | 说明   |	
|------------------|-------|------| 
| familyParamModel | 	是    | 家庭信息 | 
| callback         | 是     | 请求回调 |

> QuecFamilyParamModel属性定义同上

**示例代码**

```kotlin
val family = getFamilyInfo()    //从云端获取到的家庭信息
family.familyName = "new name"
QuecSmartHomeService.setFamily(family) {
    if (it.isSuccess) {
        //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 删除家庭

**接口说明**

删除家庭

```kotlin
fun deleteFamily(fid: String, callback: QuecCallback<Unit>)
```

**参数说明**

| 参数       | 	是否必传 | 说明   |	
|----------|-------|------| 
| fid      | 	是    | 家庭id | 
| callback | 是     | 请求回调 |

**示例代码**

```kotlin
QuecSmartHomeService.deleteFamily("fid") {
    if (it.isSuccess) {
        //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 查询当前家庭

**接口说明**

查询当前家庭

```kotlin
fun getCurrentFamily(
    fid: String?, currentCoordinates: String?, callback: QuecCallback<QuecFamilyItemModel>
)
```

**参数说明**

| 参数                 | 	是否必传 | 说明                                             |	
|--------------------|-------|------------------------------------------------| 
| fid                | 	是    | 家庭id                                           |  
| currentCoordinates | 	否    | 当前GPS定位坐标，WGS84坐标系，格式：40.759186,-73.928204，非必传 | 
| callback           | 是     | 请求回调                                           |

> QuecFamilyItemModel属性定义同上

**示例代码**

```kotlin
QuecSmartHomeService.getCurrentFamily("fid", null) {
    if (it.isSuccess) {
        val data = it.data //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 家庭列表

**接口说明**

查询家庭列表

```kotlin
fun getFamilyList(
    role: String?,
    pageNumber: Int,
    pageSize: Int,
    callback: QuecCallback<QuecPageResponse<QuecFamilyItemModel>>
)
```

**参数说明**

| 参数         | 	是否必传 | 说明                                          |	
|------------|-------|---------------------------------------------| 
| role       | 	否    | 角色，成员角色：1-创建者  2-管理员  3-普通成员，多个使用英文逗号分隔，非必填 | 
| pageNumber | 	是    | 页码，非必填，默认1                                  | 
| pageSize   | 	是    | 页大小，非必填，默认10                                | 
| callback   | 是     | 请求回调                                        |

> QuecFamilyItemModel属性定义同上

**示例代码**

```kotlin
QuecSmartHomeService.getFamilyList("1", 1, 10) {
    if (it.isSuccess) {
        val data = it.data //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 家庭中设备列表

**接口说明**

查询家庭中设备列表

```kotlin
fun getFamilyDeviceList(
    params: QuecFamilyDeviceListParamsModel,
    callback: QuecCallback<QuecPageResponse<QuecDeviceModel>>
)
```

**参数说明**

| 参数       | 	是否必传 | 说明   |	
|----------|-------|------| 
| params   | 	是    | 参数模型 | 
| callback | 是     | 请求回调 |

**QuecFamilyDeviceListParamsModel属性定义**

| 字段                | 类型     | 描述                   |
|-------------------|--------|----------------------|
| fid               | String | 家庭Id                 |
| isAddOwnerDevice  | BOOL   | 是否加上自己的所有设备，非必填      |
| deviceName        | String | 设备名称搜索, 非必须          |
| pageNumber        | Int    | 页码，非必填，默认1           |
| pageSize          | Int    | 页大小，非必填，默认10         |
| isGroupDeviceShow | BOOL   | 是否显示群组设备，默认缺省        |
| isAssociation     | BOOL   | 查询未被关联的设备, 默认 false  |
| secondItemCode    | String | 二级品类过滤, 默认为空         |
| pkList            | String | 增加pklist, 多pk用英文逗号隔开 |

**示例代码**

```kotlin
QuecSmartHomeService.getFamilyDeviceList(
    QuecFamilyDeviceListParamsModel(
        "fid", true, null, 1, 10, true, false, null, null
    )
) {
    if (it.isSuccess) {
        val data = it.data //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

## 常用设备

### 添加常用设备

**接口说明**

添加常用设备, 支持批量添加

```kotlin
fun addCommonUsedDevice(
    fid: String, deviceList: List<QuecDeviceEnterModel>, callback: QuecCallback<OperateDeviceListRespModel>
)
```

**参数说明**

| 参数         | 	是否必传 | 说明   |	
|------------|-------|------| 
| fid        | 	是    | 家庭id | 
| deviceList | 	是    | 设备列表 | 
| callback   | 是     | 请求回调 |

**QuecDeviceEnterModel属性定义**

| 字段 | 类型     | 描述   |
|----|--------|------|
| pk | String | 产品pk |
| dk | String | 设备dk |

> QuecOperateDeviceRespModel为批量操作通用返回类, 可根据实际返回参取需要用得到的属性

**QuecOperateDeviceRespModel属性定义**

| 字段          | 类型                        | 描述     |
|-------------|---------------------------|--------|
| successList | QuecOperateDeviceRespItem | 执行成功列表 |
| failureList | QuecOperateDeviceRespItem | 执行失败列表 |

**QuecOperateDeviceRespItem属性定义**

| 字段           | 类型     | 描述                        |
|--------------|--------|---------------------------|
| fid          | String | 家庭id                      |
| oldFrid      | String | 原房间号                      |
| nFrid        | String | 新房间号                      |
| pk           | String | 产品pk                      |
| dk           | String | 设备dk                      |
| deviceName   | String | 设备名                       |
| shareCode    | String | 分享码                       |
| isCommonUsed | BOOL   | 是否常用：true-常用，false-不是常用   |
| msg          | String | 错误提示文案 （仅failureList有此属性） |

**示例代码**

```kotlin
QuecSmartHomeService.addCommonUsedDevice(
    "fid",
    listOf(QuecDeviceEnterModel("dk", "pk"))
) {
    if (it.isSuccess) {
        val data = it.data //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 移除常用设备

**接口说明**

移除常用设备, 支持批量移除

```kotlin
fun deleteCommonUsedDevice(
    fid: String, deviceList: List<CommonDevice>, callback: QuecCallback<OperateDeviceListRespModel>
)
```

**参数说明**

| 参数         | 	是否必传 | 说明   |	
|------------|-------|------| 
| fid        | 	是    | 家庭id | 
| deviceList | 	是    | 设备列表 | 
| callback   | 是     | 请求回调 |

> QuecDeviceEnterModel QuecOperateDeviceRespModel属性定义同上

**示例代码**

```kotlin
QuecSmartHomeService.deleteCommonUsedDevice("fid", listOf(CommonDevice().apply {
    pk = "pk"
    dk = "dk"
})) {
    if (it.isSuccess) {
        val data = it.data //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 常用设备列表

**接口说明**

查询常用设备列表

```kotlin
fun getCommonUsedDeviceList(
    fid: String,
    pageNumber: Int,
    pageSize: Int,
    isGroupDeviceShow: Boolean,
    callback: QuecCallback<QuecPageResponse<QuecDeviceModel>>
)
```

**参数说明**

| 参数                | 	是否必传 | 说明            |	
|-------------------|-------|---------------| 
| fid               | 	是    | 家庭id          | 
| pageNumber        | 	否    | 页码，非必填，默认1    | 
| pageSize          | 	否    | 页大小，非必填，默认10  | 
| isGroupDeviceShow | 	否    | 是否显示群组设备，默认缺省 | 
| callback          | 是     | 请求回调          |

> QuecDeviceModel属性定义同设备SDK说明

**示例代码**

```kotlin
QuecSmartHomeService.getCommonUsedDeviceList("fid", 1, 10, true) {
    if (it.isSuccess) {
        val data = it.data //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

## 设备组

### 家庭设备组列表

**接口说明**

查询家庭设备组列表

```kotlin
fun getFamilyGroupList(
    fid: String,
    pageNumber: Int,
    pageSize: Int,
    callback: QuecCallback<QuecPageResponse<QuecFamilyDeviceGroupInfoModel>>
)
```

**参数说明**

| 参数         | 	是否必传 | 说明           |	
|------------|-------|--------------| 
| fid        | 	是    | 家庭ID         | 
| pageNumber | 	否    | 页码，非必填，默认1   | 
| pageSize   | 	否    | 页大小，非必填，默认10 | 
| callback   | 是     | 请求回调         |

**QuecFamilyDeviceGroupInfoModel属性定义**

| 字段               | 类型     | 描述              |
|------------------|--------|-----------------|
| name             | String | 名称              |
| fid              | String | 家庭ID，创建分组时该字段必传 |
| address          | String | 地址              |
| contactPhoneList | String | 联系人             |
| coordinate       | String | 经纬度             |
| coordinateSystem | String | 坐标系             |
| descrip          | String | 说明              |
| manager          | String | 管理员             |
| managerType      | String | 管理员类型           |
| parentId         | String | 父设备组ID          |
| extend           | String | 拓展字段            |
| dgid             | String | 分组ID            |
| owner            | String | 拥有者             |
| addTime          | String | 添加时间            |
| addTimeTs        | Int    | 添加时间戳           |

**示例代码**

```kotlin
QuecSmartHomeService.getFamilyGroupList("fid", 1, 10) {
    if (it.isSuccess) {
        val data = it.data //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 添加家庭设备分组

**接口说明**

添加家庭设备分组

```kotlin
fun addFamilyDeviceGroup(
    groupInfoModel: QuecFamilyDeviceGroupModel, callback: QuecCallback<Unit>
)
```

**参数说明**

| 参数             | 	是否必传 | 说明   |	
|----------------|-------|------| 
| groupInfoModel | 	是    | 分组信息 | 
| callback       | 是     | 请求回调 |

**QuecFamilyDeviceGroupModel属性定义**

| 字段               | 类型     | 描述              |
|------------------|--------|-----------------|
| name             | String | 名称              |
| fid              | String | 家庭ID，创建分组时该字段必传 |
| address          | String | 地址              |
| contactPhoneList | String | 联系人             |
| coordinate       | String | 经纬度             |
| coordinateSystem | String | 坐标系             |
| descrip          | String | 说明              |
| manager          | String | 管理员             |
| managerType      | String | 管理员类型           |
| parentId         | String | 父设备组ID          |
| extend           | String | 拓展字段            |

**示例代码**

```kotlin
QuecSmartHomeService.addFamilyDeviceGroup(
    QuecFamilyDeviceGroupModel(
        name = "name",
        fid = "fid",
    )
) {
    if (it.isSuccess) {
        //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 查询不在家庭设备组内的设备列表

**接口说明**

查询不在家庭设备组内的设备列表

```kotlin
fun getDeviceListByNotInDeviceGroup(
    fid: String,
    pageNumber: Int,
    pageSize: Int,
    groupId: String,
    callback: QuecCallback<QuecPageResponse<QuecDeviceModel>>
)
```

**参数说明**

| 参数         | 	是否必传 | 说明           |	
|------------|-------|--------------| 
| fid        | 	否    | 家庭ID         | 
| pageNumber | 	否    | 页码，非必填，默认1   | 
| pageSize   | 	否    | 页大小，非必填，默认10 | 
| groupId    | 	是    | 页设备组id       | 
| callback   | 是     | 请求回调         |

> QuecDeviceModel属性定义同设备SDK说明

**示例代码**

```kotlin
QuecSmartHomeService.getDeviceListByNotInDeviceGroup("fid", 1, 10, "groupId") {
    if (it.isSuccess) {
        val data = it.data //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

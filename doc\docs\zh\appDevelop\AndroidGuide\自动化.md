# 自动化SDK:QuecAutomateSdk

## 功能概述

SDK支持用户配置一定的规则, 使设备在指定条件下执行指定的动作

```kotlin
QuecAutomateService
```

## SDK集成方式

> **注意**
>
> 自动化SDK运行依赖核心库quec-iot-sdk, 请先按照快速集成文档集成核心库

引入依赖

```groovy
implementation 'com.quectel.app.sdk:quec-automate-sdk:2.14.0'
```

## 自动化管理

### 获取自动化列表

**接口说明**

获取自动化列表, 通过分页请求

```kotlin
fun getAutomationList(pageNumber: Int, pageSize: Int, callBack: QuecCallback<QuecAutoListModel>)
```

**参数说明**

| 参数         | 是否必传 | 说明   |
|------------|------|------|
| pageNumber | 是    | 页码号  |
| pageSize   | 是    | 分页大小 |
| callback   | 是    | 请求回调 |

**示例代码**

```kotlin
QuecAutomateService.getAutomationList(1, 10) {
    if (it.isSuccess) {
        val data = it.data //请求成功, 获取到的数据
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg   //请求失败, 错误信息
    }
}
```

**QuecAutoListModel属性定义**

| 字段              | 类型                             | 描述      |
|-----------------|--------------------------------|---------|
| hasNextPage     | boolean                        | 是否还有下一页 |
| hasPreviousPage | boolean                        | 是否有上一页  |
| isFirstPage     | boolean                        | 是否是第一页  |
| isLastPage      | boolean                        | 是否是最后一页 |
| total           | int                            | 总记录数    |
| page            | int                            | 当前页数    |
| pageSize        | int                            | 每一页的大小  |
| list            | List&lt;QuecAutoListItemModel> | 记录数据    |

**QuecAutoListItemModel属性定义**

| 字段            | 类型                                      | 描述                      |
|---------------|-----------------------------------------|-------------------------|
| automationId  | long                                    | 自动化ID                   |
| conditionType | int                                     | 触发类型。1: 满足任意，2: 满足所有    |
| name          | String                                  | 自动化名称                   |
| nameType      | int                                     | 自动化名称生成方式。1：系统生成，2：用户填写 |
| status        | boolean                                 | 启停状态。true：启用，false：停用   |
| triggerList   | List&lt;QuecAutoListItemConditionModel> | 触发条件                    |
| actionList    | List&lt;QuecAutoListItemActionModel>    | 执行动作                    |

**QuecAutoListItemConditionModel属性定义**

| 字段   | 类型     | 描述               |
|------|--------|------------------|
| name | String | 设备名称             |
| icon | String | 设备图标             |
| type | int    | 触发条件类型，0：设备，1：定时 |
| sort | int    | 条件顺序，从1开始递增      |                  

**QuecAutoListItemActionModel属性定义**

| 字段   | 类型     | 描述                         |
|------|--------|----------------------------|
| name | String | 设备/群组/场景名称                 |
| icon | String | 设备/群组/场景图标                 |
| type | int    | 执行动作类型，1：延时，2：设备，3：群组，4：场景 |
| sort | int    | 条件顺序，从1开始递增                |              

### 创建自动化

**接口说明**

创建一条自动化规则可以拆分为4个部分

1. 基础信息
    1. 名称
2. 生效时间
    1. 时间段
        1. 特定时间段(0:00 ~ 23:59:59)
        2. 全天
        3. 白天/夜晚
    2. 日期
        1. 每一天
        2. 每周的第几天
        3. 每月的第几天
        4. 指定的开始日期和结束日期
3. 触发条件
    1. 条件选择
        1. 满足任意条件
        2. 满足所有条件
    2. 条件类型 (可有多个)
        1. 指定设备的联动条件
        2. 定时
            1. 一天中的时间点
            2. 一周中哪几天
4. 执行动作 (可有多个)
    1. 执行设备联动配置
    2. 执行指定的场景
    3. 添加延时

需将4个部分的信息都填入后, 方可创建一条可用的规则

```kotlin
fun addAutomation(automationModel: QuecAutomationModel, callBack: QuecCallback<Long>)
```

**参数说明**

| 参数              | 是否必传 | 说明    |
|-----------------|------|-------|
| automationModel | 是    | 自动化信息 |
| callback        | 是    | 请求回调  |

**示例代码**

```kotlin
QuecAutomateService.addAutomation(
    QuecAutomationModel(
        "",
        0L,
        1,
        null,
        "自动化名称",
        1,
        QuecAutomationPreconditionModel().apply {
            effectDate = ""
            effectDateType = 0
            effectTimeType = 2
            location = ""
            timeZone = "GMT+08:00"
        },
        listOf(
            QuecAutomationConditionModel(
                type = 1,
                timer = QuecAutomationTimeModel(
                    0, "14:10", "", "GMT+8:00"
                ),
            )
        ),
        listOf(
            QuecAutomationActionModel(
                type = 4,
                icon = "https://xxx/xxx.png",
                name = "sence name",
                sceneId = "场景id",
                sort = 1,
            )
        )
    )
) {
    if (it.isSuccess) {
        //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg   //请求失败, 错误信息
    }
}
```

**QuecAutomationModel属性定义**

| 字段            | 类型                                    | 描述                       |
|---------------|---------------------------------------|--------------------------|
| fid           | String                                | 家庭id, 家居模式下有值, 非家居模式下传"" |
| automationId  | Long                                  | 自动化id                    |                          
| conditionType | Int                                   | 触发类型。1: 满足任意，2: 满足所有     |                          |
| icon          | String                                | 自动化icon                  |
| name          | String                                | 自动化名称                    |
| nameType      | int                                   | 自动化名称生成方式。1：系统生成，2：用户填写  |                         
| precondition  | QuecAutomationPreconditionModel       | 生效条件                     |
| conditions    | List&lt;QuecAutomationConditionModel> | 触发条件                     |
| actions       | List&lt;QuecAutomationActionModel>    | 执行动作                     |

**QuecAutomationPreconditionModel属性定义**

| 字段             | 类型     | 描述                                                        |
|----------------|--------|-----------------------------------------------------------|
| effectTimeType | int    | 生效时间类型 0:白天 ,1:夜晚 ,2:全天 ,3:特定时间段                          |
| startTime      | String | 特定开始时间, 例如 12:00:00                                       |
| endTime        | String | 特定结束时间, 例如 12:00:00                                       |
| effectDateType | int    | 生效日期类型  0:每天 ,1:每周的 ,2:每月的 ,3:指定日期                        |
| effectDate     | String | 生效日期-传输计算用 每周的 1/2/3/4/5/6/7 每月的 1...31  指定日期 12-19/12-21 |
| effectDateShow | String | 生效日期，用于展示  每天 周一....周日   1号....31号                        |
| regionName     | String | 地区名称                                                      |
| location       | String | 经纬度，经度在前纬度在后。坐标系类型：GCJ-02                                 |
| timeZone       | String | 时区id 例如: GMT+08:00                                        |

**QuecAutomationConditionModel属性定义**

| 字段         | 类型                      | 描述                   |
|------------|-------------------------|----------------------|
| type       | int                     | 触发类型。1: 满足任意，2: 满足所有 |
| icon       | String                  | 图标                   |
| name       | String                  | 条件名称                 |
| timer      | QuecAutomationTimeModel | 触发时间                 |
| productKey | String                  | 设备的productKey        |
| deviceKey  | String                  | 设备的deviceKey         |
| property   | QuecAutoProductTSLModel | 设备属性                 |
| sort       | int                     | 条件排序从1开始依次递增         |

**QuecAutomationActionModel属性定义**

| 字段         | 类型                      | 描述                                                     |
|------------|-------------------------|--------------------------------------------------------|
| type       | int                     | 执行的动作类型 1-延迟执行后面一个动作 2-修改设备属性 3-修改群组属性 3-修改群组属性 4-执行场景 |
| icon       | String                  | 图标                                                     |
| name       | String                  | 名称                                                     |
| productKey | String                  | 设备的productKey                                          |
| deviceKey  | String                  | 设备的deviceKey                                           |
| sceneId    | String                  | 场景id                                                   |
| delayTime  | int                     | 延迟时间 单位秒                                               |
| sort       | int                     | 动作排序从1开始递增                                             |
| property   | QuecAutoProductTSLModel | 设备属性                                                   |

**QuecAutomationTimeModel属性定义**

| 字段        | 类型     | 描述                             |
|-----------|--------|--------------------------------|
| type      | int    | 定时类型 0:仅一次 ,1:每天 ,2:自定义        |
| time      | String | 定时时间 23:00 24小时制               |
| dayOfWeek | String | 定时自定义时间段, 格式为 "1,3,4"，以 "," 分隔 |
| timeZone  | String | 时区id 例如: GMT+08:00             |

**QuecAutoProductTSLModel属性定义**

| 字段       | 类型     | 描述                                                                 |
|----------|--------|--------------------------------------------------------------------|
| id       | int    | 物模型id                                                              |
| code     | String | 物模型code                                                            |
| name     | String | 物模型名称                                                              |
| compare  | String | 作为条件的时候有比较符号, 属性值比较运算符：> >= < <= == != REPORT COUNT，其中REPORT为上报的数据 |
| unit     | String | 单位                                                                 |
| dataType | String | 数据类型                                                               |
| value    | String | 属性值                                                                |
| subName  | String | 条件名称                                                               |

### 编辑自动化

**接口说明**

编辑已有的自动化规则

```kotlin
fun editAutomation(automationModel: QuecAutomationModel, callBack: QuecSimpleCallBack)
```

**参数说明**

| 参数              | 是否必传 | 说明    |
|-----------------|------|-------|
| automationModel | 是    | 自动化信息 |
| callback        | 是    | 请求回调  |

**示例代码**

```kotlin
val model = getModel() //从服务器获取的 automationModel
model.name = "new name"
QuecAutomateService.editAutomation(model) {
    if (it.isSuccess) {
        //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg   //请求失败, 错误信息
    }
}
```

### 删除自动化

删除指定的自动化规则

**接口说明**

```kotlin
fun deleteAutomation(automationId: Long, callBack: QuecCallback<Any>)
```

**参数说明**

| 参数           | 是否必传 | 说明    |
|--------------|------|-------|
| automationId | 是    | 自动化id |
| callback     | 是    | 请求回调  |

**示例代码**

```kotlin
val model = getModel() //从服务器获取的 automationModel
QuecAutomateService.deleteAutomation(model.automationId) {
    if (it.isSuccess) {
        //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg   //请求失败, 错误信息
    }
}
```

### 查询设备可作为自动化条件和触发动作的物模型

**接口说明**

```kotlin
fun <T> getAutomationTSLWithProductKey(
        productKey: String,
        type: Int,
        convertor: PropertyConvertor<T>,
        callBack: QuecCallback<QuecPublishedAutoProductTSLModel<T>>
)
```

**参数说明**

| 参数         | 是否必传 | 说明                  |
|------------|------|---------------------|
| productKey | 是    | 设备的productKey       |
| type       | 是    | 查询类型：0：全部，1：条件，2：动作 |
| convertor  | 是    | 自定义物模型解析器           |
| callback   | 是    | 请求回调                |

**示例代码**

```kotlin
//ModelBasic在核心库中有定义
val modelConvertor = object : PropertyConvertor<ModelBasic<*>> {
    override fun convert(objectJson: JSONArray): List<ModelBasic<*>> {
        val modelBasics: MutableList<ModelBasic<*>> = ArrayList()
        try {
            //此方法在核心库中有定义
            return ObjectModelParse.buildModelListContent(objectJson)
        } catch (e: JSONException) {
            QLog.e("TslModelUtils", "convert error $e")
        }
        return modelBasics
    }
}
QuecAutomateService.getAutomationTSLWithProductKey("pk", 0, modelConvertor) {
    if (it.isSuccess) {
        //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg   //请求失败, 错误信息
    }
}
```

**QuecPublishedAutoProductTSLModel属性定义**

| 字段         | 类型         | 描述      |
|------------|------------|---------|
| actions    | List&lt;T> | 条件可用的属性 |
| conditions | List&lt;T> | 动作可用的属性 |

### 获取自动化详情

**接口说明**

获取自动化详情

```kotlin
fun getAutomationInfo(automationId: Long, callBack: QuecCallback<QuecAutomationModel>)
```

**参数说明**

| 参数           | 是否必传 | 说明    |
|--------------|------|-------|
| automationId | 是    | 自动化id |
| callback     | 是    | 请求回调  |

**示例代码**

```kotlin
QuecAutomateService.getAutomationInfo(1002) {
    if (it.isSuccess) {
        //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg   //请求失败, 错误信息
    }
}
```

### 开启关闭自动化

**接口说明**

可使自动化规则启动或者暂停

```kotlin
fun updateAutomationSwitchStatus(automationId: Long, open: Boolean, callBack: QuecCallback<Any>)
```

**参数说明**

| 参数           | 是否必传 | 说明    |
|--------------|------|-------|
| automationId | 是    | 自动化id |
| open         | 是    | 启动状态  |
| callback     | 是    | 请求回调  |

**示例代码**

```kotlin
QuecAutomateService.updateAutomationSwitchStatus(1002, true) {
    if (it.isSuccess) {
        //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg   //请求失败, 错误信息
    }
```

## 自动化测试

### 测试自动化

**接口说明**

可测试配置的自动化的动作是否可以执行

```kotlin
fun testAutomationInfo(actions: List<QuecAutomationActionModel>, timeout: Long, callBack: QuecCallback<Long>)
```

**参数说明**

| 参数       | 是否必传 | 说明              |
|----------|------|-----------------|
| actions  | 是    | 自动化任务           |
| timeout  | 是    | 超时时间, 单位秒       |
| callback | 是    | 请求回调, 成功时返回结果id |

**示例代码**

```kotlin
QuecAutomateService.testAutomationInfo(
    listOf(
        QuecAutomationActionModel(
            type = 4,
            icon = "https://iot-oss.quectelcn.com/quec_scene_1.png",
            name = "sence name",
            sceneId = "场景id",
            sort = 1,
        )
    ), 30
) {
    if (it.isSuccess) {
        //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg   //请求失败
    }
}
```

### 获取自动化测试的结果

**接口说明**

在执行了自动化测试后, 可以通过结果id来查询自动化执行的结果

```kotlin
fun getTestAutomationResult(testResultId: Long, callBack: QuecCallback<QuecTestAutomationResultModel>)
```

**参数说明**

| 参数           | 是否必传 | 说明   |
|--------------|------|------|
| testResultId | 是    | 结果id |
| callback     | 是    | 请求回调 |

**示例代码**

```kotlin
QuecAutomateService.getTestAutomationResult(100) {
    if (it.isSuccess) {
        val data = it.data //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg   //请求失败
    }
}
```

**QuecTestAutomationResultModel属性定义**

| 字段             | 类型                           | 描述                           |
|----------------|------------------------------|------------------------------|
| result         | int                          | 自动化执行结果 0-全部成功 1-部分成功 2-全部失败 |
| successCount   | int                          | 成功数量                         |
| failCount      | int                          | 失败数量                         |
| overallFailMsg | String                       | 错误信息                         |
| timeoutStatus  | boolean                      | 超时状态                         |
| failActionList | List&lt;QuecTestActionModel> | 失败的动作列表                      |

**QuecTestActionModel属性定义**

| 字段       | 类型                      | 描述       |
|----------|-------------------------|----------|
| name     | String                  | 动作名称     |
| icon     | String                  | 动作图标     |
| failMsg  | String                  | 失败原因     |
| property | QuecAutoProductTSLModel | 失败的物模型属性 |

## 自动化日志

### 获取自动化日志列表

**接口说明**

自动化执行之后, 可以查询其执行的记录

```kotlin
fun getAutomationLogList(
        lastLogId: Long,
        limit: Int,
        callBack: QuecCallback<List<QuecAutoLogDataModel>>
)
```

**参数说明**

| 参数        | 是否必传 | 说明       |
|-----------|------|----------|
| lastLogId | 是    | 最后一条日志id |
| limit     | 是    | 获取几条     |
| callback  | 是    | 请求回调     |

**示例代码**

```kotlin
QuecAutomateService.getAutomationLogList(0, 10) {
    if (it.isSuccess) {
        val data = it.data //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg   //请求失败
    }
}
```

**QuecAutoLogDataModel属性定义**

| 字段           | 类型                      | 描述                                           |
|--------------|-------------------------|----------------------------------------------|
| automationId | long                    | 自动化id                                        |
| logId        | long                    | 日志id                                         |
| name         | String                  | 自动化任务名称                                      |
| result       | int                     | 执行结果 0.成功：所有都执行成功即视为成 1.失败：所有都失败即视为失败 2.部分成功 |
| time         | long                    | 执行时间(时间戳)                                    |                                             
| logDetails   | List&lt;QuecLogDetails> | 日志详情                                         |

**QuecLogDetails属性定义**

| 字段          | 类型                      | 描述                  |
|-------------|-------------------------|---------------------|
| actionName  | String                  | 动作名称                |
| type        | int                     | 类型  2设备 3群组 4场景     |
| result      | boolean                 | 执行结果：true成功 false失败 |
| executeTime | long                    | 执行时间                |
| action      | QuecAutoProductTSLModel | 执行动作                |

### 获取日志详情

**接口说明**

获取自动化日志详情

```kotlin
fun getAutomationLogDetail(logId: Long, callBack: QuecCallback<QuecAutoLogDataModel>)
```

**参数说明**

| 参数       | 是否必传 | 说明   |
|----------|------|------|
| logId    | 是    | 日志id |
| callback | 是    | 请求回调 |

**示例代码**

```kotlin
QuecAutomateService.getAutomationLogDetail(200) {
    if (it.isSuccess) {
        val data = it.data //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg   //请求失败
    }
}
```

### 清除自动化日志

**接口说明**

清除自动化日志, 会删掉所有的日志

```kotlin
fun clearAutomationLogs(callBack: QuecCallback<Any>)
```

**参数说明**

| 参数       | 是否必传 | 说明   |
|----------|------|------|
| callback | 是    | 请求回调 |

**示例代码**

```kotlin
QuecAutomateService.clearAutomationLogs {
    if (it.isSuccess) {
        //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg   //请求失败
    }
}
```

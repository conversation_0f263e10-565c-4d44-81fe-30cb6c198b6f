# User Manual

## **Introduction**

Welcome to the guide on using Quectel QthTools-SN&IMEI Binding tool. The tool is designed to help you bind the IMEI your Quectel module to the SN of Developer Center and generate the corresponding csv file for easy import to Developer Center.


## **Getting Started**

To begin, simply open the program. The tool will automatically load your previously saved records from your last session, making it easier for you to resume where you left off. After opening the tool, a prompt box will pop up, reminding you that the input box for the scan code will appear only after clicking the first three columns. Please note that the last column, reserved for device name, is currently not set.

<span style="color:#999AAA">Note: Tool currently displays up to 2000 lines per page, with a maximum of 25 pages available. When the program starts, historical data will be loaded. Please note that larger datasets may require more time to load, resulting in a longer loading time.</span>


<a data-fancybox title="img" href="/en/tool/QthTools-factory/QthTools-SN2IMEI_Binding/resource/Tool-1.png"><img src="/en/tool/QthTools-factory/QthTools-SN2IMEI_Binding/resource/Tool-1.png"></a>



### **Scan QR Code and Input Data**

Double-click the first 3 columns to pop up a message box. If you click on the first column, the message will be "Please scan QR code of the module". If you click the second or third column and the first column is not empty, the message will be "Please scan QR code of the device"; if you click the second or third column and the first column is empty, the message will be "Please first click the device DeviceKey column to scan". If no input is required, you can simply click the "**Exit**" button to exit.


<span style="color:#999AAA">Note: When scanning a code, please make sure that the input box is in editable status (i.e., the cursor is flashing).</span>


<a data-fancybox title="img" href="/en/tool/QthTools-factory/QthTools-SN2IMEI_Binding/resource/Tool-2.png"><img src="/en/tool/QthTools-factory/QthTools-SN2IMEI_Binding/resource/Tool-2.png"></a>


If the input is wrong or the scan code is wrong:


<a data-fancybox title="img" href="/en/tool/QthTools-factory/QthTools-SN2IMEI_Binding/resource/Tool-3.png"><img src="/en/tool/QthTools-factory/QthTools-SN2IMEI_Binding/resource/Tool-3.png"></a>



### **File Operation - Import Data**

When importing a file, the tool will detect the overall format, but it will not detect specific content.

**Step 1：** 

Click "**文件(File)**" in the upper left corner, and then click "**导入(Import)**".

<a data-fancybox title="img" href="/en/tool/QthTools-factory/QthTools-SN2IMEI_Binding/resource/Tool-4.png"><img src="/en/tool/QthTools-factory/QthTools-SN2IMEI_Binding/resource/Tool-4.png"></a>



**Step 2：**

If the current record is populated, a pop-up window will appear asking you if you want to overwrite the current record. If there is no data in the current record, skip to **Step 3**.


<a data-fancybox title="img" href="/en/tool/QthTools-factory/QthTools-SN2IMEI_Binding/resource/Tool-5.png"><img src="/en/tool/QthTools-factory/QthTools-SN2IMEI_Binding/resource/Tool-5.png"></a>


**Step 3：**

A pop-up window will display the CSV file under Windows, please select a file and import.


<a data-fancybox title="img" href="/en/tool/QthTools-factory/QthTools-SN2IMEI_Binding/resource/Tool-6.png"><img src="/en/tool/QthTools-factory/QthTools-SN2IMEI_Binding/resource/Tool-6.png"></a>



**Step 4：**

Once the file verification is 100% successful and the progress bar for writing the file reaches 100%, the file import is complete.


<a data-fancybox title="img" href="/en/tool/QthTools-factory/QthTools-SN2IMEI_Binding/resource/Tool-8.png"><img src="/en/tool/QthTools-factory/QthTools-SN2IMEI_Binding/resource/Tool-8.png"></a>




### **File Operation – Export Data**


Click "**文件(File)**" in the upper left corner, and then click "导出(Export)". Input the exported file name, and then click "保存(Save)" button.

<a data-fancybox title="img" href="/en/tool/QthTools-factory/QthTools-SN2IMEI_Binding/resource/Tool-9.png"><img src="/en/tool/QthTools-factory/QthTools-SN2IMEI_Binding/resource/Tool-9.png"></a>

If the pop-up window shown in the following figure is displayed, it indicates that the file has been saved successfully.


<a data-fancybox title="img" href="/en/tool/QthTools-factory/QthTools-SN2IMEI_Binding/resource/Tool-10.png"><img src="/en/tool/QthTools-factory/QthTools-SN2IMEI_Binding/resource/Tool-10.png"></a>




### **File Operation – Clear Data**


After clicking "**文件(File)**" in the upper left corner, click "清除(Clear)".

<a data-fancybox title="img" href="/en/tool/QthTools-factory/QthTools-SN2IMEI_Binding/resource/Tool-11.png"><img src="/en/tool/QthTools-factory/QthTools-SN2IMEI_Binding/resource/Tool-11.png"></a>

If the currently selected data is not empty, the following pop-up window will appear. If you click "**Yes**", all data will be cleared, and if you click "**No**" or close the window, clearing all data will be canceled.

<a data-fancybox title="img" href="/en/tool/QthTools-factory/QthTools-SN2IMEI_Binding/resource/Tool-12.png"><img src="/en/tool/QthTools-factory/QthTools-SN2IMEI_Binding/resource/Tool-12.png"></a>




### **Delete Data**

Select the table row to be deleted (multiple selections are supported), right-click to pop up a menu bar, and then click "**删除行“Delete**" to delete the data.


<a data-fancybox title="img" href="/en/tool/QthTools-factory/QthTools-SN2IMEI_Binding/resource/Tool-13.png"><img src="/en/tool/QthTools-factory/QthTools-SN2IMEI_Binding/resource/Tool-13.png"></a>




### **Getting Help**

Click "**帮助****(Help)**" in the upper left corner to pop up the user manual.


<a data-fancybox title="img" href="/en/tool/QthTools-factory/QthTools-SN2IMEI_Binding/resource/Tool-14.png"><img src="/en/tool/QthTools-factory/QthTools-SN2IMEI_Binding/resource/Tool-14.png"></a>

<a data-fancybox title="img" href="/en/tool/QthTools-factory/QthTools-SN2IMEI_Binding/resource/Tool-15.png"><img src="/en/tool/QthTools-factory/QthTools-SN2IMEI_Binding/resource/Tool-15.png"></a>

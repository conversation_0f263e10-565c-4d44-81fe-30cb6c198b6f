# 预导入管理

本文介绍设备运维中设备的相关操作。

## **前提条件**

● 已创建产品

## **操作步骤**

1. 登录开发者中心后，点击左侧菜单 **“设备管理”** → **“设备运维”** ，在账号注册的归属区域中可进入预导入管理。

<a data-fancybox title="img" href="/zh/guide/20230625163627.png">![img](/zh/guide/20230625163627.png)</a>

2. 在预导入管理页面中，可添加不同认证方式的设备，添加成功的设备信息将作用于所有地区的数据中心，以支持多区域出货设备的使用。设备在某一个数据中心进行认证时，将以预导入所选择的认证方式进行验证，验证通过后会显示在当地的设备列表中，并继承设备名称、SN等预导入时设置的信息。

3. 预导入管理中对设备信息的修改不会影响各数据中心设备列表中已上线的设备数据，若想修改当地的设备信息，可在对应数据中心的设备列表中进行修改。

## **添加设备**

动态认证设备：当关闭产品级别动态认证功能时，仅已导入的设备可以通过ProductKey和ProductSecret连接平台进行认证，并获取到DeviceSecret。

静态认证设备：仅预先烧录过DeviceSecret的设备可登录平台，同时平台将拒绝DeviceKey、DeviceSecret不匹配的设备进行认证操作。

静态认证类型的设备添加成功后，可通过 **“设备详情”** 或 **“批次管理”** 页面，获取平台为设备生成的DeviceSecret。

备注：仅LwM2M、MQTT标准协议产品支持添加静态认证类型设备。

X.509证书设备：平台支持设备使用自己的X.509证书进行认证鉴权，设备接入前需要上传设备的CA证书。

添加此认证类型设备时，可选择录入对应的设备证书指纹，若不录入则会以设备初次连接时所使用的证书生成指纹，后续连接将与该指纹进行匹配验证。

设备证书指纹需采用SHA-1算法计算，如：openssl x509 -fingerprint -sha1 -in certfile.crt，openssl生成的指纹带有":"号，需将":"去除后上传40位长度的16进制字符串。

对于设备的其他操作，详见下表

| **操作** | **描述**                                                                                                                              |
| -------- | ------------------------------------------------------------------------------------------------------------------------------------- |
| 生成SN   | 点击右侧“生成SN”按钮，可以根据需要生成一定数量的SN，（每次生成最多10000个）用于App端绑定设备所用。                                    |
| 批次管理 | 用于批量获取设备信息（含DS），用户可通过勾选批次号下载通过静态认证导入平台的设备信息，或通过调用OpenAPI的方式使用烧录码获取设备信息。 |
| 导出设备 | 提供按产品维度导出预导入的设备数据。                                                                                                  |

 


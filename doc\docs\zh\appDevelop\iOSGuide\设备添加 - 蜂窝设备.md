# 蜂窝设备添加

## 功能概述

蜂窝设备启动后直接在线, 可以直接通过SN号添加

```objc
QuecDeviceService
```

## 在线设备直接添加

对于已经配网在线的设备, 可以直接进行设备添加

### 通过SN绑定设备

**接口说明**

通过SN绑定设备, 可以添加已经在线的设备

```objc
- (void)bindDeviceWithSerialNumber:(NSString *)serialNumber
                        productKey:(NSString *)productKey
                        deviceName:(NSString *)deviceName
                           success:(void(^)(QuecDeviceBindSNModel *model))success
                           failure:(QuecErrorBlock)failure;
```

**参数说明**

| 参数           | 是否必传 | 说明           |
|--------------|------|--------------|
| serialNumber | 是    | 设备序列号        |
| productKey   | 是    | 设备productKey |
| deviceName   | 否    | 设备名称         |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	|

**示例代码**

```objc
[QuecDeviceService.sharedInstance bindDeviceWithSerialNumber:@"sn"
                                                      productKey:@"pk"
                                                      deviceName:@"device name"
                                                         success:^(QuecDeviceBindSNModel *model) {
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

**QuecDeviceBindSNModel属性定义**

| 字段                  | 类型        | 描述   |
|---------------------|-----------|------------------------|
| authKey                  | NSString    |  authKey   |
| pk          | String    | 设备productKey   |
| dk           | String    | 设备deviceKey        |
| moduleType           | String    | 设备型号        |

# 开发环境配置



## __一、开发环境准备__
## 获取SDK及模组资料


点击<a href="https://iot.quectelcn.com/download?menuCode=MODULE_DEVL&resourceType=M" target="_blank">下载中心-SDK</a>下载模组SDK及模组资料，若无SDK则联系移远FAE获取。

## __二、安装驱动__

以 __EC800M__ 模组为例，介绍如何安装移远通信模块的USB驱动。可联系移远通信技术支持获取USB驱动或进入<a href="https://iot.quectelcn.com/download?menuCode=MODULE_DEVL&resourceType=M" target="_blank">下载中心-驱动</a>下载 USB驱动。

__1、安装驱动__
双击打开驱动包开始安装，选择安装目录，点击 __Next>__ 即可完成安装

<a data-fancybox title="img" href="/zh/deviceDevelop/develop/speediness/resource/QuecOpen/Speediness-QuecOpen-02.png">![img](/zh/deviceDevelop/develop/speediness/resource/QuecOpen/Speediness-QuecOpen-02.png)</a>

__2、验证是否安装成功__

将PC与开发板连接，打开PC设备管理器可查看对应的驱动是否安装成功，查看 端口(COM 和 LPT) 项，若如下图所示设备即表示安装成功。

<a data-fancybox title="img" href="/zh/deviceDevelop/develop/speediness/resource/QuecOpen/Speediness-QuecOpen-03.png">![img](/zh/deviceDevelop/develop/speediness/resource/QuecOpen/Speediness-QuecOpen-03.png)</a>

## __三、编译环境配置__

__1、下载并安装VSCode__

__2、安装及配置Ubuntu 20.04 LTS__

  

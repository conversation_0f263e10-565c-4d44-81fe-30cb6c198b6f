# 设备管理

## 功能概述

设备管理主要提供包含设备列表、设备信息、设备配置相关等操作。

在设备管理类中您将频繁使用到QuecDeviceModel类，关于属性的定义如下

**QuecDeviceModel属性定义**

| 字段                     | 类型                   | 描述                                   |
|------------------------|----------------------|--------------------------------------|
| accessType             | String               | 0-直连设备 1-网关设备 2-网关子设备                |
| activeTime             | String               | 激活时间                                 |
| activeTimeTs           | long                 | 激活时间戳                                |
| authKey                | String               | 授权key                                |
| deviceBindTime         | String               | 设备绑定时间                               |
| deviceBindTimeTs       | long                 | 设备绑定时间戳                              |
| deviceCreateTime       | String               | 设备创建时间                               |
| deviceKey              | String               | 设备key                                |
| deviceName             | String               | 设备名称                                 |
| deviceStatus           | String               | 云端设备状态：离线 在线                         |
| onlineStatus           | int                  | 云端设备状态：0-离线 1-在线                     |
| deviceType             | int                  | 设备类型：1 自有设备、 2 分享来的设备                |
| invaildTime            | String               | 失效时间                                 |
| invaildTimeTs          | long                 | 失效时间戳                                |
| lastConnTime           | String               | 最后上线时间                               |
| lastConnTimeTs         | String               | 最后上线时间戳                              |
| locateType             | String               | 支持的定位内容                              |
| ownerUid               | String               | 分享人用户ID，来自谁的分享                       |
| phone                  | String               | 已绑定用户手机号                             |
| productKey             | String               | 产品key                                |
| productName            | String               | 产品名称                                 |
| protocol               | String               | 接入协议                                 |
| uid                    | String               | 已绑定用户ID                              |
| userName               | String               | 已绑定用户昵称                              |
| verified               | String               | 设备绑定是否认证：0 未认证 1 已认证                 |
| signalStrength         | String               | 信号强度                                 |
| status                 | int                  | 绑定状态：1 正常 2 失效                       |
| lastOfflineTime        | String               | 离线时间                                 |
| lastOfflineTimeTs      | long                 | 离线时间戳                                |
| btPwd                  | String               | btPwd                                |
| bindType               | String               | 绑定类型:1 SN绑定 2 wifi绑定 3 PKDK绑定 4 蓝牙绑定 |
| authCode               | String               | 授权authCode                           |
| logoImage              | String               | 产品logo                               |
| sn                     | String               | sn                                   |
| productIcon            | String               | 产品图片                                 |
| upgradeStatus          | int                  | 设备OTA状态：0-未升级，1-升级中，2-升级成功，3-升级失败    |
| userConfirmStatus      | int                  | 设备OTA用户确认状态                          |
| planId                 | long                 | 计划id                                 |
| capabilitiesBitmask    | int                  | 设备通道能力mask                           |
| bindMode               | int                  | 绑定模式: 1 多绑模式 2 单绑模式 3 轮流模式           |
| deviceId               | String               | 设备唯一标识，pk 和 dk共同决定                   |
| onlineChannelState     | int                  | 通道在离线状态，包含近场、ws                      |
| connectingChannelState | int                  | 通道连接中状态                              |
| isNative               | int                  | 是否是移远设备，0-未知 1-移远                    |
| isMatter               | int                  | 是否是matter设备，0-否 1-是                  |
| networkType            | String               | 网络类型：1-wifi   2-蜂窝    3-NB   5-蓝牙    |
| matterInfo             | QuecDeviceMatterMeta | matter 元数据                           |
| firstItemCode          | String               | 一级品类code                             |
| firstItemName          | String               | 一级品类名称                               |
| secondItemCode         | String               | 二级品类code                             |
| secondItemName         | String               | 二级品类名称                               |
| lowPowerProduct        | boolean              | 是否为低功耗产品                             |
| lowPowerStatus         | boolean              | 低功耗开关状态                              |
| lowPowerCache          | int                  | 低功耗缓存时长，单位为秒                         |
| isCommonUsed           | boolean              | 是否常用                                 |
| fid                    | String               | 家庭id                                 |
| frid                   | String               | 房间id                                 |
| roomName               | String               | 房间名称                                 |
| shareCode              | String               | 分享码                                  |
| isShared               | boolean              | 是否来自分享                               |
| gdid                   | String               | 群组id                                 |
| isGroupDevice          | boolean              | 是否群组设备                               |
| groupDeviceDeviceNum   | int                  | 群组包含的设备数量                            |
| dps                    | NSDictionary         | dps数据                                |
| bindingCode            | String               | bindingCode                          |
| btLastUseTime          | long                 | 蓝牙最近使用时间（毫秒时间戳）                      |
| lowPowerAlive          | int                  | 低功耗心跳周期（分）                           |
| aiCapabilityStatus     | boolean              | 是否支持Ai能力                             |

## 设备管理

### 获取设备列表

**接口说明**

用于获取设备列表，需要先调用绑定接口关联设备和用户关系

```kotlin
fun getDeviceList(params: QuecDeviceListParamsModel, callback: QuecCallback<QuecPageResponse<QuecDeviceModel>>)
```

**参数说明**

| 参数       | 	是否必传 | 说明                         |	
|----------|-------|----------------------------|  
| params   | 	是    | QuecDeviceListParamsModel类 | 
| callback | 是     | 请求回调                       |

**QuecDeviceListParamsModel属性定义**

| 字段             | 类型      | 描述                    |
|----------------|---------|-----------------------|
| pageNumber     | int     | 页码 （必填）               |
| pageSize       | int     | 页大小 （必填）              |
| isAssociation  | boolean | 设备关联管理参数（非必填）         |
| secondItemCode | String  | 二级品类参数（非必填）           |
| pkList         | String  | pk列表，多个使用英文逗号分隔 （非必填） |
| deviceName     | String  | 设备名称-用于设备名称搜索（非必填）    |

> 返回值类QuecDeviceModel属性定义已在文档开头注释

**示例代码**

```kotlin
QuecDeviceService.getDeviceList(QuecDeviceListParamsModel(1, 10, true, null, null, null)) {
    if (it.isSuccess) {
        val data = it.data //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 设备重命名

**接口说明**

用于修改设备名称

```kotlin
fun updateDeviceName(
    deviceName: String,
    productKey: String,
    deviceKey: String,
    callback: QuecCallback<Unit>
)
```

**参数说明**

| 参数         | 	是否必传 | 说明   |	
|------------|-------|------|  
| deviceName | 是     | 设备名称 |
| productKey | 是     | 产品pk |
| deviceKey  | 是     | 产品dk |
| callback   | 是     | 请求回调 |

**示例代码**

```kotlin
QuecDeviceService.updateDeviceName("newName", "pk", "dk") {
    if (it.isSuccess) {
        //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 设备详情

**接口说明**

获取设备详情

```kotlin
fun getDeviceInfoByDeviceKey(
    deviceKey: String,
    productKey: String,
    callback: QuecCallback<QuecDeviceModel>
)
```

**参数说明**

| 参数         | 	是否必传 | 说明   |	
|------------|-------|------|  
| productKey | 是     | 产品pk |
| deviceKey  | 是     | 产品dk |
| callback   | 是     | 请求回调 |

> 返回值类QuecDeviceModel属性定义已在文档开头注释

**示例代码**

```kotlin
QuecDeviceService.getDeviceInfoByDeviceKey("dk", "pk") {
    if (it.isSuccess) {
        val data = it.data //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 设备物模型及业务属性值

**接口说明**

获取设备物模型及业务属性值

```kotlin
fun getProductTslAndDeviceBusinessAttributes(
    productKey: String,
    deviceKey: String,
    gatewayPk: String?,
    gatewayDk: String?,
    codeList: String?,
    type: String?,
    callback: IDeviceTSLModelCallback
)
```

**IDeviceTSLModelCallback接口定义**

```kotlin
public interface IDeviceTSLModelCallback {
    void onResultCallback(List<QuecProductTSLPropertyModel<?>> list);
    void onFail(Throwable throwable);
}
```

**参数说明**

| 参数         | 	是否必传 | 说明                                                                        |	
|------------|-------|---------------------------------------------------------------------------| 
| productKey | 是     | 产品pk                                                                      |
| deviceKey  | 是     | 设备dk                                                                      |
| gatewayPk  | 否     | 网关设备的 Product Key                                                         |
| gatewayDk  | 否     | 网关设备的 Device Key                                                          |
| codeList   | 否     | 查询的属性标识符 和查询类型配合使用，如果查询多个属性，使用英文逗号分隔                                      |
| type       | 否     | 查询类型 (查询类型可以单选和多选，如果需要查询多个类型的属性值，使用英文逗号分隔)  1 查询设备基础属性 2 查询物模型属性 3 查询定位信息 |
| callback   | 是     | 请求回调                                                                      |

**QuecProductTSLPropertyModel属性定义**

| 字段             | 类型                               | 描述             |
|----------------|----------------------------------|----------------|
| specs          | List&lt;QuecProductTSLSpecModel> | 物模型数据          |
| formatSpecs    | List&lt;QuecProductTSLSpecModel> | 物模型数据， 同上specs |
| dataType       | String                           | 数据类型           |
| attributeValue | String                           | 属性值            |
| code           | String                           | 标志符            |
| name           | String                           | 名称             |
| subType        | String                           | 读写类型           |
| itemId         | int                              | id             |
| sort           | String                           | 排序             |
| type           | String                           | 类型             |
| desc           | String                           | 描述             |

**QuecProductTSLSpecModel属性定义**

| 字段             | 类型                               | 描述               |
|----------------|----------------------------------|------------------|
| specs          | List&lt;QuecProductTSLSpecModel> | 嵌套物模型数据          |
| formatSpecs    | List&lt;QuecProductTSLSpecModel> | 嵌套物模型数据， 同上specs |
| dataType       | String                           | 数据类型             |
| code           | String                           | 标志符              |
| name           | String                           | 名称               |
| value          | String                           | value值           |
| itemId         | int                              | id               |
| unit           | String                           | 单位               |
| min            | String                           | 最小值              |
| max            | String                           | 最大值              |
| step           | String                           | 步长               |
| length         | String                           | 文本长度             |
| size           | String                           | 数组大小             |
| attributeValue | id                               | 属性值              |

**示例代码**

```kotlin
QuecDeviceService.getProductTslAndDeviceBusinessAttributes(
    "pk", "dk", null, null, null, null, object : IDeviceTSLModelCallback {
        override fun onResultCallback(list: MutableList<QuecProductTSLPropertyModel<*>>?) {
            //请求成功
        }
        override fun onFail(throwable: Throwable?) {
            //请求失败
        }
    })
```

### 设备物模型

**接口说明**

获取设备物模型

```kotlin
fun getProductTSL(
    productKey: String, callback: IDeviceTSLCallBack
)
```

**IDeviceTSLCallBack接口定义**

```kotlin
public interface IDeviceTSLCallBack {
    void onSuccess(List<ModelBasic> modelBasicList, List<TSLEvent> tslEventList, List<TSLService> tslServiceList);
 
    void onFail(Throwable throwable);
}
```

**参数说明**

| 参数         | 	是否必传 | 说明   |	
|------------|-------|------|  
| productKey | 是     | 产品pk |
| callback   | 是     | 请求回调 |

**QuecProductTSLModel属性定义**

| 字段         | 类型                                   | 描述                                    |
|------------|--------------------------------------|---------------------------------------|
| profile    | QuecProductTSLProfileModel           | profile信息                             |
| properties | List&lt;QuecProductTSLPropertyModel> | 属性，item 是 QuecProductTSLPropertyModel |
| services   | List&lt;QuecProductTSLServiceModel>  | 服务，item 是 QuecProductTSLServiceModel  |
| events     | List&lt;QuecProductTSLEventModel>    | 事件，item 是 QuecProductTSLEventModel    |

**QuecProductTSLProfileModel属性定义**

| 字段         | 类型     | 描述    |
|------------|--------|-------|
| productKey | String | 产品key |
| version    | String | 版本    |
| tslVersion | String | tls版本 |

> QuecProductTSLPropertyModel属性定义同上

**QuecProductTSLServiceModel属性定义**

| 字段         | 类型           | 描述              |
|------------|--------------|-----------------|
| inputData  | NSDictionary | 服务输入项，描述服务输入的数据 |
| outputData | NSDictionary | 服务输出项，描述服务输出的数据 |

**QuecProductTSLEventModel属性定义**

| 字段         | 类型           | 描述                |
|------------|--------------|-------------------|
| outputData | NSDictionary | 事件输出项，描述事件输出的具体事项 |

**示例代码**

```kotlin
QuecDeviceService.getProductTSL("pk", object : IDeviceTSLCallBack {
    override fun onSuccess(
        modelBasicList: MutableList<ModelBasic<Any>>?,
        tslEventList: MutableList<TSLEvent>?,
        tslServiceList: MutableList<TSLService>?
    ) {
        //请求成功
    }
    override fun onFail(throwable: Throwable?) {
        //请求失败
    }
})
```

### 设备业务属性值

**接口说明**

获取设备业务属性值

```kotlin
fun getDeviceBusinessAttributes(
    productKey: String,
    deviceKey: String,
    gatewayPk: String?,
    gatewayDk: String?,
    codeList: String?,
    type: String?,
    callback: QuecCallback<QuecProductTSLInfoModel>
)
```

**参数说明**

| 参数         | 	是否必传 | 说明                                                                       |	
|------------|-------|--------------------------------------------------------------------------|  
| productKey | 是     | 产品pk                                                                     |
| deviceKey  | 是     | 设备dk                                                                     |
| gatewayPk  | 否     | 网关设备的 Product Key                                                        |
| gatewayDk  | 否     | 网关设备的 Device Key                                                         |
| codeList   | 否     | 查询的属性标识符 和查询类型配合使用，如果查询多个属性，使用英文逗号分隔                                     |
| type       | 否     | 查询类型 (查询类型可以单选和多选，如果需要查询多个类型的属性值，使用英文逗号分隔) 1 查询设备基础属性 2 查询物模型属性 3 查询定位信息 |
| callback   | 是     | 请求回调                                                                     |

**QuecProductTSLInfoModel属性定义**

| 字段               | 类型                                       | 描述           |
|------------------|------------------------------------------|--------------|
| customizeTslInfo | List&lt;QuecProductTSLCustomInfoModel>   | 自定义物模型数据上报列表 |
| deviceLocateInfo | QuecProductTSLLocateInfoModel            | 定位信息         |
| deviceData       | QuecProductTSLDataModel                  | 设备资源数据       |
| tslResourcesInfo | List&lt;QuecProductTSLResourceInfoModel> | 资源物模型数据      |

**QuecProductTSLCustomInfoModel属性定义**

| 字段            | 类型     | 描述     |
|---------------|--------|--------|
| abId          | int    | 功能ID   |
| dataType      | String | 数据类型   |
| name          | String | 功能名称   |
| resourceCode  | String | 功能标识符  |
| resourceValue | String | 功能值    |
| subType       | String | 数据操作类型 |
| type          | String | 功能类型   |

**QuecProductTSLLocateInfoModel属性定义**

| 字段           | 类型     | 描述         |
|--------------|--------|------------|
| createTime   | double | 创建时间       |
| deviceKey    | String | deviceKey  |
| hdop         | String | 水平精度因子     |
| lat          | String | 纬度         |
| lng          | String | 经度         |
| productKey   | String | productKey |
| latType      | String | 纬度半球 N/S   |
| lngType      | String | 经度半球 W/E   |
| locateRaw    | String | 原始数据       |
| locateStatus | String | 差分定位/非差分定位 |
| locateTime   | String | 定位时间       |
| locateType   | String | 定位类型       |
| satellites   | String | 当前卫星数      |
| bdLat        | String | BD09坐标系纬度  |
| bdLng        | String | BD09坐标系经度  |
| gcjLat       | String | GCJ坐标系纬度   |
| gcjLng       | String | GCJ坐标系经度   |
| wgsLat       | String | GPS 原始坐标纬度 |
| wgsLng       | String | GPS 原始坐标经度 |

**QuecProductTSLDataModel属性定义**

| 字段              | 类型     | 描述          |
|-----------------|--------|-------------|
| battery         | double | 电池电量        |
| cellId          | int    | cellId      |
| comProtocolVer  | String | 通信协议版本      |
| dataProtocolVer | String | 数据协议版本      |
| deviceKey       | String | deviceKey   |
| iccid           | String | iccid       |
| lac             | int    | 位置区代码       |
| locator         | String | 定位支持功能      |
| mcc             | String | 移动国家代码      |
| mcuVersion      | String | MCU版本       |
| memoryFree      | int    | 剩余空间        |
| mnc             | int    | 移动网络代码      |
| phoneNum        | String | 手机号         |
| productKey      | String | productKey  |
| rsrp            | int    | 参考信号接收功率    |
| rsrq            | int    | LTE参考信号接收质量 |
| sdkVersion      | String | SDK版本       |
| sim             | String | SIM号        |
| snr             | int    | 信号与干扰加噪声比   |
| type            | String | 模组型号        |
| version         | String | 模组版本        |
| voltage         | double | 电压          |

**QuecProductTSLResourceInfoModel属性定义**

| 字段            | 类型     | 描述        |
|---------------|--------|-----------|
| deviceKey     | String | deviceKey |
| createTime    | String | 创建时间      |
| deviceId      | String | 设备id      |
| resourceCode  | String | 资源标识      |
| resourceValue | String | 资源值       |
| updateTime    | String | 更新时间      |

**示例代码**

```kotlin
QuecDeviceService.getDeviceBusinessAttributes("pk", "dk", null, null, null, null) {
    if (it.isSuccess) {
        val data = it.data //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 离线提醒开关

**接口说明**

设置设备离线提醒开关

```kotlin
fun setOfflineReminder(
    productKey: String,
    deviceKey: String,
    enableOfflineReminder: Int,
    callback: QuecCallback<Unit>
)
```

**参数说明**

| 参数                    | 	是否必传 | 说明        |	
|-----------------------|-------|-----------| 
| productKey            | 是     | 产品pk      |
| deviceKey             | 是     | 设备dk      |
| enableOfflineReminder | 是     | 0：关闭，1：开启 |
| callback              | 是     | 请求回调      |

**示例代码**

```kotlin
QuecDeviceService.setOfflineReminder("pk", "dk", 1) {
    if (it.isSuccess) {
        //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 离线提醒开关查询

**接口说明**

获取设备离线提醒开关状态

```kotlin
fun getOfflineReminder(
    productKey: String,
    deviceKey: String,
    callback: QuecCallback<Int>
)
```

**参数说明**

| 参数         | 	是否必传 | 说明             |	
|------------|-------|----------------|  
| productKey | 是     | 产品pk           |
| deviceKey  | 是     | 设备dk           |
| callback   | 是     | 请求回调 0：关闭，1：开启 |

**示例代码**

```kotlin
QuecDeviceService.getOfflineReminder("pk", "dk") {
    if (it.isSuccess) {
        val data = it.data //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 设置设备时区

**接口说明**

设置设备时区， 用于设备时区同步当前App所设置时区

```kotlin
fun setDeviceTimeZone(
    productKey: String,
    deviceKey: String,
    timeZone: String,
    timeZoneId: String?,
    callback: QuecCallback<Unit>
)
```

**参数说明**

| 参数         | 	是否必传 | 说明                         |	
|------------|-------|----------------------------|  
| productKey | 是     | 产品pk                       |
| deviceKey  | 是     | 设备dk                       |
| timeZone   | 是     | 时区,符合GMT+-HH:mm或者GMT+-HH格式 |
| timeZoneId | 是     | 时区ID                       |
| callback   | 是     | 请求回调                       |

**示例代码**

```kotlin
QuecDeviceService.setDeviceTimeZone("pk", "dk", "GMT+08:00", TimeZone.getDefault().id) {
    if (it.isSuccess) {
        //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 获取设备时区

**接口说明**

获取当前设备时区信息

```kotlin
fun getDeviceTimeZone(
    productKey: String,
    deviceKey: String,
    callback: QuecCallback<QuecDeviceTimeZoneModel>
)
```

**参数说明**

| 参数         | 	是否必传 | 说明   |	
|------------|-------|------| 
| productKey | 是     | 产品pk |
| deviceKey  | 是     | 设备dk |
| callback   | 是     | 请求回调 |

**QuecDeviceTimeZoneModel属性定义**

| 字段         | 类型     | 描述   |
|------------|--------|------|
| timeZone   | String | 设备时区 |
| timeZoneId | String | 时区id |

**示例代码**

```kotlin
QuecDeviceService.getDeviceTimeZone("pk", "dk") {
    if (it.isSuccess) {
        val data = it.data //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 获取产品说明书

**接口说明**

获取产品说明书

```kotlin
fun getProductDescription(
    productKey: String,
    callback: QuecCallback<String>
)
```

**参数说明**

| 参数         | 	是否必传 | 说明   |	
|------------|-------|------|  
| productKey | 是     | 产品pk |
| callback   | 是     | 请求回调 |

**示例代码**

```kotlin
QuecDeviceService.getProductDescription("pk") {
    if (it.isSuccess) {
        val data = it.data //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 单设备解绑

**接口说明**

设备解绑接口

```kotlin
fun unbindDevice(
    deviceKey: String,
    productKey: String,
    isInit: Boolean,
    random: String?,
    resetCredentials: String?,
    callback: QuecCallback<Unit>
)
```

**参数说明**

| 参数               | 	是否必传 | 说明                      |	
|------------------|-------|-------------------------|  
| deviceKey        | 是     | 设备dk                    |
| productKey       | 是     | 产品pk                    |
| isInit           | 否     | 是否初始化，默认false （纯蓝牙设备需填） |
| random           | 否     | （纯蓝牙设备需填）               |
| resetCredentials | 否     | 纯蓝牙设备重置凭证 （纯蓝牙设备需填）     |
| callback         | 是     | 请求回调                    |

**示例代码**

```kotlin
QuecDeviceService.unbindDevice("dk", "pk", false, null, null) {
    if (it.isSuccess) {
        //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 批量解绑设备

**接口说明**

批量解绑设备

```kotlin
fun batchUnbindDevice(
    isInit: Boolean,
    deviceList: List<QuecDeviceModel>,
    callback: QuecCallback<QuecBatchUnbindModel>
)
```

**参数说明**

| 参数         | 	是否必传 | 说明                |	
|------------|-------|-------------------|  
| isInit     | 否     | 是否初始化，默认false     |
| deviceList | 是     | QuecDeviceModel数组 |
| callback   | 是     | 请求回调              |

> QuecDeviceModel属性定义同上

**QuecBatchUnbindModel属性定义**

| 字段          | 类型                                  | 描述   |
|-------------|-------------------------------------|------|
| successList | List&lt;QuecBatchUnbindDeviceModel> | 成功列表 |
| failList    | List&lt;QuecBatchUnbindDeviceModel> | 失败列表 |

**QuecBatchUnbindDeviceModel属性定义**

| 字段         | 类型     | 描述                     |
|------------|--------|------------------------|
| pk         | String | 产品pk                   |
| dk         | String | 设备dk                   |
| gdid       | String | 群组id                   |
| shareCode  | String | 分享码                    |
| bindingSum | int    | 解绑操作后设备在云端的绑定用户数，包括伪绑定 |
| code       | int    | 错误码（仅失败列表有此属性）         |
| msg        | String | 错误提示（仅失败列表有此属性）        |

**示例代码**

```kotlin
QuecDeviceService.batchUnbindDevice(false, listOf(QuecDeviceModel("pk", "dk"))) {
    if (it.isSuccess) {
        val data = it.data //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

# Overview of App Data Communication 

## Background

After your device accesses Developer Center, the App can use Websocket and OpenAPI for message communication. See the following flow chart for details.


<a data-fancybox title="img" href="/en/quickStart/image2022-3-22_10-39-54.png">![img](/en/quickStart/image2022-3-22_10-39-54.png)</a>


The App can use the SDK provided by the Platform, or call the Platform's OpenAPI to control your device(s) remotely. For API details, see [OpenAPI User Guide](/appDevelop/OpenAPI/API_accessInstruction).

Through WebSocket, the <PERSON>pp interacts with the device(s) in real time and receives messages from the device(s), adjusts device parameters, and performs other functions. 


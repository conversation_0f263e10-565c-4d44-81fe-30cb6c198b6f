# Events Related to Interaction Between the Device and Developer Center

## __Event Callback Format：__

### __Directly Connected Device Or Gateway Device__

> __(*eventCB)(event, errcode, value, valLen)__

### __Sub-device__

> __(*eventCb)(event, errcode, subPk, subDk, value, valLen)__

## __Callback Event Description：__

* __`event`__ ：Integer type. Event identifier.
  * __`1`__ ：Bootstrap Authentication Event
  * __`2`__ ：Access Event
  * __`3`__ ：Subscription Event
  * __`4`__ ：Data Sending Event
  * __`5`__ ：Data Receiving Event
  * __`6`__ ：Deregistration Event
  * __`7`__ ：OTA Event
  * __`8`__ ：Developer Center Event
  * __`9`__ : Wake-up Event
  * __`10`__ ：Sub-device Deregistration Event
  * __`30`__ ：Data Acquisition Event

<br>

* __`subPK`__ ：const void * type. ProductKey generated when a product is created on Developer Center.
* __`subDK`__ ：const void * type. Unique identifier of the sub-device.
* __`errcode`__：Integer type. Event return code.
* __`value`__：const void * type. Data carried by the event.
* __`valLen`__：qint32_t type. Length of data carried by the event.

## __1:Bootstrap Authentication Event__

| Event ID, Event Code | Carried Value |                                                  Description                                                  |                                                                                                                   Reason and Treatment                                                                                                                   |
| :------------------: | :-----------: | :------------------------------------------------------------------------------------------------------------: | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------: |
|       1,10200       |     NULL     |                                       Successful device authentication.                                       |                                                                                                                                                                                                                                                          |
|       1,10300       |     NULL     |                                                 Other errors.                                                 |                                                                            Device internal error. Please contact Acceleronix Product Dept FAE to capture logs for analysis.                                                                            |
|       1,10404       |     NULL     |                              An error occurred when Developer Center called the API.                              |                                                                            Device internal error. Please contact Acceleronix Product Dept FAE to capture logs for analysis.                                                                            |
|       1,10421       |     NULL     |     Static devices cannot re-register.     |                                                                              Execute the correct command to configure DeviceSecret of the device with static authentication.                                                                              |
|       1,10422       |     NULL     |                                  The device is authenticated (Connection failed).                                  |                                                                                        Incorrect DeviceSecret. Please reset the DeviceSecret on Developer Center.                                                                                        |
|       1,10423       |     NULL     |                          Failed to query the product information (Connection failed).                          |                                                                                                    Incorrect ProductKey. Please check the ProductKey.                                                                                                    |
|       1,10424       |     NULL     |                                 Failed to decode PAYLOAD (Connection failed).                                 |        Possible reason 1: Incorrect ProductKey. Please check the ProductKey.<br>Possible reason 2: Failed to decode the DeviceSecret. Please check the consistency between the module DeviceSecret and the DeviceSecret assigned by Developer Center.        |
|       1,10425       |     NULL     |                               Signature verification failed (Connection failed).                               |                                                                  Illegal DeviceKey. DeviceKey must be 4 to 32 characters long and contain numbers, capital letters or lowercase letters.                                                                  |
|       1,10427       |     NULL     |                                Hash information is illegal (Connection failed).                                |                                                                            Device internal error. Please contact Acceleronix Product Dept FAE to capture logs for analysis.                                                                            |
|       1,10431       |     NULL     |                                       DK is illegal (Connection failed).                                       |                                                                  Illegal DeviceKey. DeviceKey must be 4 to 32 characters long and contain numbers, capital letters or lowercase letters.                                                                  |
|       1,10433       |     NULL     |                                                Flag is illegal.                                                |                                                                            Device internal error. Please contact Acceleronix Product Dept FAE to capture logs for analysis.                                                                            |
|       1,10434       |     NULL     |               ClientID is not matched with the password (The password contains ClientID-related information).               |                                                                            Device internal error. Please contact Acceleronix Product Dept FAE to capture logs for analysis.                                                                            |
|       1,10440       |     NULL     |                               The gateway is not associated with the sub-device.                               |                                                                                                                                                                                                                                                          |
|       1,10450       |     NULL     |                                   Device internal error (Connection failed).                                   | Possible reason 1: No available network. Please check whether the device is connected to the antenna and registered on the network. <br>Possible reason 2: Device internal error. Please contact Acceleronix Product Dept FAE to capture logs for analysis. |
|       1,10500       |     NULL     |                  Device authentication to Developer Center failed (Unknown error in the system).                  |                                                                            Device internal error. Please contact Acceleronix Product Dept FAE to capture logs for analysis.                                                                            |

## __2:Access Event__

| Event ID, Event Code | Carried Value |                                    Description                                    |                                                                                                                    Reason and Treatment                                                                                                                    |
| :------------------: | :-----------: | :--------------------------------------------------------------------------------: | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------: |
|       2,10200       |     NULL     |                              Registered successfully.                              |                                                                                                                                                                                                                                                            |
|       2,10404       |     NULL     |                An error occurred when Developer Center called the API.                |                                                                             Device internal error. Please contact Acceleronix Product Dept FAE to capture logs for analysis.                                                                             |
|       2,10430       |     NULL     |                     DeviceSecret is incorrect (Connection failed).                     |                                                                                         Incorrect DeviceSecret. Please reset the DeviceSecret on Developer Center.                                                                                         |
|       2,10431       |     NULL     |                      Device is disabled (Connection failed).                      |                                                                             Device internal error. Please contact Acceleronix Product Dept FAE to capture logs for analysis.                                                                             |
|       2,10433       |     NULL     |                                  Flag is illegal.                                  |                                                                             Device internal error. Please contact Acceleronix Product Dept FAE to capture logs for analysis.                                                                             |
|       2,10434       |     NULL     | ClientID is not matched with the password (The password contains ClientID-related information). |                                                                             Device internal error. Please contact Acceleronix Product Dept FAE to capture logs for analysis.                                                                             |
|       2,10437       |     NULL     |                             DeviceSecret is incorrect.                             |                                                            Please initiate a request to connect the device to Developer Center or wait for the device to access Developer Center automatically.                                                            |
|       2,10438       |     NULL     |                Developer Center cannot get the device information.                |                                                            Please initiate a request to connect the device to Developer Center or wait for the device to access Developer Center automatically.                                                            |
|       2,10441       |     NULL     |                 The sub-device connects to Developer Center successfully.                 |                                                                                                                                                                                                                                                            |
|       2,10450       |     NULL     |                     Device internal error (Connection failed).                     |  Possible reason 1: No available network. Please check whether the device is connected to the antenna and registered to the network. <br>Possible reason 2: Device internal error. Please contact Acceleronix Product Dept FAE to capture logs for analysis.  |
|       2,10471       |     NULL     |            The implementation version is not supported (Connection failed).            |                                                                             Device internal error. Please contact Acceleronix Product Dept FAE to capture logs for analysis.                                                                             |
|       2,10473       |     NULL     |                    Access heartbeat error (Connection timeout).                    |                                                                      No available network. Please check whether the device is connected to the antenna and registered to the network.                                                                      |
|       2,10474       |     NULL     |                        Network error (Connection timeout).                        |                                                                      No available network. Please check whether the device is connected to the antenna and registered to the network.                                                                      |
|       2,10475       |     NULL     |                                  Server changed.                                  |                                                                                    Server or product information changed. Please edit the server or product information.                                                                                    |
|       2,10476       |     NULL     |                                AP connection error.                                |                                                                      No available network. Please check whether the device is connected to the antenna and registered to the network.                                                                      |
|       2,10477       |     NULL     |                      The device is disabled (Connection failed).                      |                                                                                               Invalid DeviceKey. Please check whether the DeviceKey is valid.                                                                                               |
|       2,10478       |     NULL     |                                Device reset failed.                                | Perhaps you executed the command of configuring DeviceSecret of the device with static authentication to configure DeviceSecret of the device with dynamic authentication. Please check whether the device's authentication type is dynamic authentication. |
|       2,10500       |     NULL     |                   Registration failed (unknown error in the system).                   |                                                                             Device internal error. Please contact Acceleronix Product Dept FAE to capture logs for analysis.                                                                             |

## __3:Subscription Event__

| Event ID, Event Code | Carried Value |      Description      |                                        Reason and Treatment                                        |
| :------------------: | :-----------: | :-------------------: | :------------------------------------------------------------------------------------------------: |
|       3,10200       |     NULL     | Subscription success. |                                                                                                    |
|       3,10300       |     NULL     | Subscription failure. | Device internal error. Please contact Acceleronix Product Dept FAE to capture logs for analysis. |

## __4:Data Sending Event__

| Event ID, Event Code |              Carried Value              |                        Description                        |                                                                                                                                                                                                     Reason and Treatment                                                                                                                                                                                                     |
| :------------------: | :-------------------------------------: | :-------------------------------------------------------: | :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------: |
|       4,10200       |           NULL/qint32_t* type           |     Sent transparent transmission data successfully.     |                                                                                                                                                                                                                                                                                                                                                                                                                              |
|       4,10210       |           NULL/qint32_t* type           |                Sent TSL data successfully.                |                                                                                                                                                                                                                                                                                                                                                                                                                              |
|       4,10220       |           NULL/qint32_t* type           |             Sent location data successfully.             |                                                                                                                                                                                                                                                                                                                                                                                                                              |
|       4,10230       |          NULL/qint32_t* type          |         Sent status data successfully.         |                                                                                                                                                                                                                                                                                                                                                                                                                              |
|       4,10240       |          NULL/qint32_t* type          |     Sent device information successfully.     |                                                                                                                                                                                                                                                                                                                                                                                                                              |
|       4,10250       | <time\>,"\<time_zone\>","\<timestamp\>" |      Sent NTP information successfully. |                                                                                                                                                                                                                                                                                                                                                                                                                              |
|       4,10300       |           NULL/qint32_t* type           |       Failed to send transparent transmission data       | Possible reason 1: The length of data sent by the device is different from that configured with Write Command. Please check whether the data length is correct.<br>Possible reason 2: The length of data sent by the device is greater than the maximum length supported by the module.<br>Possible reason 3: The device has not connected to Developer Center. Please ensure the device has been connected to Developer Center. |
|       4,10310       |           NULL/qint32_t* type           |                 Failed to send TSL data.                 |    Possible reason 1: The length of data sent by the device is different from that configured with Write Command. Please check whether the data length is correct.<br>Possible reason 2: The length of data sent by the device is greater than the maximum length supported by the module.<br>Possible reason 3: The device is not connected to Developer Center. Please ensure the device is connected to Developer Center.    |
|       4,10320       |           NULL/qint32_t* type           |               Failed to send location data.               |                                                                                                                                                                                                                                                                                                                                                                                                                              |
|       4,10330       |           NULL/qint32_t* type           |  Failed to send status data.  |                                                                                                                                                                                                                                                                                                                                                                                                                              |
|       4,10340       |           NULL/qint32_t* type           |  Failed to send device information.   |                                                                                                                                                                                                                                                                                                                                                                                                                              |
|       4,10350       |          NULL/qint32_t* type          |    Failed to NTP information.                |                                                                                                                                                                                                                                                                                                                                                                                                                              |
|  4, 10400   | NULL/qint32_t* type | Sending transparent transmission data is refused. |
|  4, 10410   |   NULL/qint32_t* type |        Sending TSL data is refused.            |
|  4, 10420   |   NULL/qint32_t* type |    Sending positioning data is refused.        |


## __5:Data Receiving Event__

| Event ID, Event Code |                    Carried Value                    |                                                            Description                                                            |                                Reason and Treatment                                |
| :------------------: | :--------------------------------------------------: | :-------------------------------------------------------------------------------------------------------------------------------: | :--------------------------------------------------------------------------------: |
|       5,10200       |                   uint8_t* t type                   |                              Received the transparent transmission data issued by Developer Center.                              | The received downlink data is issued directly if the module is in unbuffered mode. |
|       5,10210       |                  TTLV pointer type                  |                                         Received the TSL data issued by Developer Center.                                         | The received downlink data is issued directly if the module is in unbuffered mode. |
|       5,10211       | uint16_t array type. Format:\<pkgId>,\<ID1>,\<ID2>… | Received the request for TSL model from Developer Center. <br>\<pkgId> request package ID issued by Developer Center. <br>\<ID> TSL ID. |                                                                                    |
|       5,10212       |            <PkgID\>,\<length\>,[\<data\>]            |                                       Received the TSL service data issued by Developer Center.                                       |                                                                                    |
|       5,10428       |                   uint8_t* t type                   |                                        Traffic limiting caused by excessive device cache.                                        |                                                                                    |
|       5,10473       |                         NULL                         |                              Failed to receive the data whose length exceeds the device cache limit.                              |                                                                                    |

## __6:Deregistration Event__

| Event ID, Event Code | Carried Value |                      Description                      |
| :------------------: | :-----------: | :---------------------------------------------------: |
|       6,10200       |     NULL     | Successful deregistration (successful disconnection). |
|       6,10201       |     NULL     | Successful sub-device deregistration (successful disconnection). |

## __7:OTA Event__

| Event ID, Event Code |                                                            Carried Value                                                            |                                                                                                                                                             Description                                                                                                                                                             |                                                                                                                                                                                            Reason and Treatment                                                                                                                                                                                            |
| :------------------: | :---------------------------------------------------------------------------------------------------------------------------------: | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------: | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------: |
|       7,10700       | String type. Format: "\<componentNo\>","\<sourceVersion\>","\<targetVersion\>",\<batteryLimit\>,\<minSignalIntensity\>,\<useSpace\> | There is an upgrade task.<br/>\<componentNo\>		Component identifier.<br/>\<sourceVersion\>		Source version.<br/>\<targetVersion\>		Target version.<br/>\<batteryLimit\>		Minimum power required for an OTA upgrade. <br/>\<minSignalIntensity\> Minimum signal strength in an OTA upgrade.      <br/>\<useSpace\> Disk space required for an OTA upgrade. |                                                                                                                                                                                                                                                                                                                                                                                                            |
|       7,10701       |                                    String type. Format: "\<componentNo\>","\<length\>","\<MD5\>"                                    |                                                             The device started downloading firmware package.<br/>\<componentNo> Component identifier.<br/>\<length> Size of the firmware package in an OTA upgrade.<br/>\<MD5> md5 value of firmware package in an OTA upgrade.                                                             |                                                                                                                                                                                                                                                                                                                                                                                                            |
|       7,10702       |                                                                NULL                                                                |                                                                                                                                                   Firmware download in progress.                                                                                                                                                   |                                                                                                                                                                                                                                                                                                                                                                                                            |
|       7,10703       |                       String type. Format: "\<componentNo\>","\<length\>","\<startaddr\>","\<piece_length\>"                       |                     The firmware package was downloaded successfully.<br/>\<componentNo> Component identifier.<br/>\<length> Size of the firmware package in an OTA upgrade.<br/>\<startaddr> Start address of the current downloaded block in the OTA upgrade package.<br/>\<piece_length> Size of the current file block.                     |                                                                                                                                                                                                                                                                                                                                                                                                            |
|       7,10704       |                                                                NULL                                                                |                                                                                                                                                   Firmware upgrading in progress.                                                                                                                                                   |                                                                                                                                                                                                                                                                                                                                                                                                            |
|       7,10705       |                                                                NULL                                                                |                                                                                                                                                    Firmware upgrade successful.                                                                                                                                                    |                                                                                                                                                                                                                                                                                                                                                                                                            |
|       7,10706       |                                                                NULL                                                                |                                                                                                                                                      Firmware upgrade failed.                                                                                                                                                      | Possible reason 1: The target version is incorrect. Please check whether the target version you entered is correct.<br> Possible reason 2: Network disconnected during the OTA upgrade. Please check whether the network or signal was interrupted during the OTA upgrade.<br> Possible reason 3:  The uploaded upgrade package is incorrect. Please check whether the uploaded upgrade package is valid. |
|       7,10707       |                                                                NULL                                                                |                                                                                                                                         Advertisement of the first device operation result.                                                                                                                                         |                                                                                                                                                                                                                                                                                                                                                                                                            |
|       7,10708       |                                                                NULL                                                                |                                                                                                                                            Waiting for sub-device operations timed out.                                                                                                                                             |                                                                                                                                                                                                                                                                                                                                                                                                            |

## __8:Developer Center Event__

| Event ID, Event Code | Carried Value |                                        Description                                        |                                                                                Reason and Treatment                                                                                |
| :------------------: | :-----------: | :----------------------------------------------------------------------------------------: | :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------: |
|       8,10428       |     NULL     |             Traffic limiting caused by high-frequency messages on the device.             |                                                  Device sends messages too frequently. Please reduce the data-sending frequency.                                                  |
|       8,10429       |     NULL     | Traffic limiting caused by an excessive number of single device activations or daily requests | Device activation code is insufficient. Please contact the local sales for price consultation and our sales will facilitate the purchase process and help to further the progress. |
|       8,10442       |     NULL     |                                       Sub-device deregistration failed.                                       |                                                                                                                                                                                    |

## __Wake-up Event__

| Event ID, Event Code | Carried Data |               Description               | 
| :------------------: | :----------: | :-------------------------------------: |
|       9,10200        |     NULL     | Device was woken up from deep sleep. |
|       9,10300        |     NULL     | 唤醒设备从深度睡眠模式错误 |

## __10:Sub-device Deregistration Event__

| Event ID, Event Code | Carried Value |        Description        |
| :------------------: | :-----------: | :-----------------------: |
|       10,10200       |     NULL     | Successful sub-device deregistration |

## __30:Data Acquisition Event__

| Event ID, Event Code | Carried Value |                                                    Description                                                    |
| :------------------: | :-----------: | :----------------------------------------------------------------------------------------------------------------: |
|         30,0         |     NULL     |                                              Data sent successfully.                                              |
|         30,1         |     NULL     |                                                Data sending failed.                                                |
|         30,2         |     NULL     | The uplink data received per second exceeded the threshold. Developer Center stops receiving uplink data. Unit: s. |
|         30,3         |     NULL     |                                       Data sending failed. Please try again.                                       |
|        30,10        |     NULL     |                                 Failed to connect to the data acquisition server.                                 |
|        30,11        |     NULL     |                              The uplink ended abnormally due to a transmission error.                              |
|        30,12        |     NULL     |                                    The data acquisition channel is not enabled.                                    |
|        30,13        |     NULL     |                                    Server connection failed. Please try again.                                    |
|        30,20        |     NULL     |                                   The data acquisition channel has been closed.                                   |

# 创建产品
产品是设备的集合，通常是将具有相同功能定义（物模型）的设备归属到一个产品下。开发者中心为每个产品颁发全局唯一的ProductKey与ProductSecret。该产品下所有设备连接平台时，均需要使用该ProductKey与ProductSecret进行认证。

创建产品时需根据引导步骤进行以下配置项，您可根据硬件产品的目标形态进行选择。

<a data-fancybox title="img" href="/zh/deviceDevelop/creatproduct/creatproduct01.png">![img](/zh/deviceDevelop/creatproduct/creatproduct01.png)</a>

各配置项描述详见下方表格：

| 参数         | 描述                                                         |
| ------------ | ------------------------------------------------------------ |
| 产品品类     | 品类用于标识产品类型，分为标准品类与行业解决方案。 <br>● 标准品类：您可在平台预定义的品类中进行选择目标品类，部分品类提供了推荐方案供快速创建与定义属性。 <br/>● 行业解决方案：平台提供不同的垂直行业解决方案，当使用官方的行业方案时，请选择该品类进行产品创建。 |
| 推荐方案     | 平台预设好的方案已包含设备类型、连网方式、数据格式的定义，您可根据产品匹配程度进行选择，若没有合适的方案，可通过自定义方案进行配置。 |
| 设备类型     | 产品下设备的类型。 <br/>● 单品设备：指集成了通信模组，可直接连接开发者中心的硬件设备。 <br/>● 网关设备：指集成了通信模组，且可以通过485、Zigbee、RS232等不同协议挂载子设备，为子设备进行代理上网，实现子设备连接开发者中心。网关具有子设备管理功能，可以维持与子设备的拓扑关系。 <br/>● 网关子设备：不直接连接物联网平台，而是通过网关代理入网的方式，接入到开发者中心。 |
| 限制网关产品 | 当设备类型选择子设备时，支持选择是否限制可关联网关产品。 <br/>● 不启用：表示子设备可通过任意网关产品设备连接开发者中心； <br/>● 启用：表示子设备仅能通过指定的网关产品设备连接开发者中心。 |
| 接入网关方式 | 当设备类型选择子设备时，需要选择关联网关方式。 <br/>● 仅代理上网：网关设备仅作为上网与数据转发通道。子设备需独立管理，移动端用户需绑定子设备后才能查询与控制子设备； <br/>● 产品整合：网关与子设备进行统一管理，移动端用户绑定网关即拥有子设备查询与控制权限。 |
| 连网方式     | 单品设备与网关设备的连网方式。 <br/>● 单品设备支持的连网方式：2G/3G/4G/5G、WiFi、NB-IoT、蓝牙 <br/>● 网关设备支持的连网方式：2G/3G/4G/5G、WiFi |
| SIM卡类型    | 当连网方式选择NB-IoT时，需要选择使用的SIM卡类型。 <br/>● 移动/联通卡：设备将直连到开发者中心提供的LwM2M Broker进行接入。 <br/>● 电信卡：设备将连接到电信平台进行接入，电信平台与开发者中心之间通过云云对接方式实现接入。 |
| 数据加密方式 | 当接入平台选择电信卡时，需要选择数据的加密方式。 <br/>● DTLS：数据包传输层使用加密安全协议进行通信。 <br/>● 明文：数据包传输不使用任何加密方式。 |
| 省电模式     | 当SIM卡类型选择电信卡时，需选择省电模式。 <br/>● PSM模式：设备在数据连接终止或周期性TAU完成后进入PSM状态，PSM态的设备处于休眠状态，不再侦听信号，平台的下行数据不会立即下发，缓存直到设备退出PSM态才会下发。 <br/>● DRX模式：非连续接收模式，由于DRX周期短，平台下行数据可以立即下发（如果设备在注册时Binding Mode支持Qmode，则按Qmode方式下发指令，即采用空闲时间窗进行激活态到休眠态的切换）。 <br/>● eDRX模式：增强型非连续接受，DRX周期要长，对时延要求高，需要根据设备是否休眠选择消息缓存或者立即下发。创建eDRX模式的产品，需要输入设备实际的eDRX周期，用于下发指令的超时时间判断。 |
| 数据格式     | 设备上下行的数据格式。 <br/>● 物模型（推荐）：是开发者中心为开发者提供的设备与平台间的数据交互协议，应用层采用JSON格式。 <br/>● 透传/自定义：如果您希望使用自定义的数据格式，可以选择为透传/自定义。 |
| 产品信息     | 最后一步需设置产品图片、产品名称 <br/>● 产品图片：产品图片将在App/小程序上展示给移动端用户。 <br/>● 产品名称：产品名称将在App/小程序上展示给移动端用户。 <br/>● 授权移联万物App：开启用移动端用户可通过移联万物App控制本产品。 |

创建完成后，平台将为产品颁发ProductKey与ProductSecret。其中ProductSecret将通过邮件方式发送到当前账户所绑定的邮箱下，请谨慎保管。

<a data-fancybox title="img" href="/zh/deviceDevelop/creatproduct/creatproduct02.png">![img](/zh/deviceDevelop/creatproduct/creatproduct02.png)</a>

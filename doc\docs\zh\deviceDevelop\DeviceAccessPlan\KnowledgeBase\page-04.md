# coolwatcher抓包步骤

本章节指导开发者使用此抓包工具对移远模组进行抓包处理，可通过该日志包分析出问题原因。

### 1. 安装驱动
双击运行 __Setup.bat__，等待安装完成。

<a data-fancybox title="img" href="/zh/deviceDevelop/KnowledgeBase/step16.png">![img](/zh/deviceDevelop/KnowledgeBase/step16.png)</a>

安装完毕后检查一下电脑设备管理器里面端口一栏中，是否有 __AP Log Port__ 端口。

<a data-fancybox title="img" href="/zh/deviceDevelop/KnowledgeBase/step17.png">![img](/zh/deviceDevelop/KnowledgeBase/step17.png)</a>

### 2. 打开抓包软件，配置端口
双击运行 __coolwatcher_usb.exe__，在弹出的窗口中选择配置文件以及端口，然后点击OK。

<a data-fancybox title="img" href="/zh/deviceDevelop/KnowledgeBase/step18.png">![img](/zh/deviceDevelop/KnowledgeBase/step18.png)</a>

点击工具栏的 __Plugins__，然后选择 __Active Tracer__。

<a data-fancybox title="img" href="/zh/deviceDevelop/KnowledgeBase/step19.png">![img](/zh/deviceDevelop/KnowledgeBase/step19.png)</a>

<a data-fancybox title="img" href="/zh/deviceDevelop/KnowledgeBase/step20.png">![img](/zh/deviceDevelop/KnowledgeBase/step20.png)</a>

可在下框输入关键字进行过滤。

<a data-fancybox title="img" href="/zh/deviceDevelop/KnowledgeBase/step21.png">![img](/zh/deviceDevelop/KnowledgeBase/step21.png)</a>

### 3. 保存日志
点击工具栏 __Tracer__，然后选择 __Save Trace(bin/trc)__。

<a data-fancybox title="img" href="/zh/deviceDevelop/KnowledgeBase/step22.png">![img](/zh/deviceDevelop/KnowledgeBase/step22.png)</a>

在弹出的窗口选择保存路径即可。


<a data-fancybox title="img" href="/zh/deviceDevelop/KnowledgeBase/step24.png">![img](/zh/deviceDevelop/KnowledgeBase/step24.png)</a>

# **常见问题**


## <span style="color:#A52A2A">__快速接入开发者中心常见问题__</span>
> __1.如何确认设备网络已具备连接上开发者中心条件？__ <br>
> 答：向模组发送 __AT+CGATT?__ 指令查询网络状态，若响应值为 __1__, 则确保模组已具备连接上开发者中心的条件。若响应值不为**1**，可通过 __AT+CSQ?__ 获取当前信号质量，若当前信号质量小于5，可能为当前环境网络信号较差或设备没有连接天线导致无法联网；若当前信号质量为99，可检查当前设备是否连接天线或SIM卡未成功插入导致。

> __2.设备初次连接开发者中心需要做什么配置？__ <br>
> 答：初次连接开发者中心前需配置产品信息ProductKey(简称PK)、ProductSecret(简称PS)。

> __3.ProductKey和ProductSecret是什么？从哪里获取ProductKey和ProductSecret？__ <br>
> 答：ProductKey(简称PK)和ProductSecret(简称PS)分别是产品号和产品密钥，是产品的唯一标识，产品下所有设备都使用同一个PK和PS。在创建产品时，PK和PS会自动下发到注册平台账号时所使用的邮箱。

> __4.设备应该如何连接开发者中心？__ <br>
> 答：<br>
> 1、向模组发送 __AT+QIOTCFG="productinfo"[,\<pk\>,\<ps\>]__ 指令配置PK、PS。 <br>
> 2、向模组发送指令 __AT+QIOTREG=1__ 连接平台。<br>


> __5.事件回调是什么？__ <br>
> 答：事件回调是基于监听的事件处理机制，例如，开发者中心下发物模型数据，模组在收到数据后将通过URC事件自动向MCU发送该日志，您即可根据该事件进行数据处理。

> __6.如何确定当前设备已连接至开发者中心？__ <br>
> 答：向模组发送 __AT+QIOTSTATE?__ 指令查询当前设备与平台之间的连接状态，若响应值为 __8__, 则确保模组已连接上开发者中心并且可进行数据业务交互。

> __7.设备如何在开发者中心认证？如何激活？__ <br>
> 答：设备在初次连接上开发者中心时，若有激活码的情况下，设备会自动认证并且激活。<br>

> __8.为什么设备连接平台失败时，事件回调码打印 "1,10422" ？__ <br>
> 答：一般是设备切换产品时导致模组DeviceSecret认证错误，或者是烧录固件时覆盖了配置文件导致模组保存的DS被清除了。
> 您只需要在平台重置一下 __DeviceSecret__，然后重新发送一下 __AT+QIOTREG=1__ 连接平台使设备重新上线。<br>

> __9.为什么进行产品配置时会返回ERROR？__ <br>
> 答：<br>
> 原因一：可能是该 QuecThing 版本不支持该配置指令，可发送 __AT+QIOTCFG=?__ 指令查看是否支持该配置。<br>
> 原因二：配置指令不支持在连接开发者中心后进行设置，故需要配置时，请先断开与开发者中心的连接。<br>

> __10.为什么输入AT指令后无响应或返回ERROR？__ <br>
> 答：<br>
> 当前设备可能不支持此AT指令，请使用 __AT+QIOTINFO?__ 查看SDK的版本号，根据该版本号查看对应的AT使用文档，并进行相应操作使用。<br>


> __11.设备连接平台时，手动注册与自动注册的区别是什么？__ <br>
> 答：<br>
>     手动注册：设备选择手动注册连接平台时，设备每次连接平台时都需要发送连接平台指令。 <br>
>     自动注册：设备选择自动注册连接平台时，设备每次上电后会自动连接平台，不需要发送连接平台指令。 <br>

> __12.设备连接开发者中心之前，我需要配置服务器信息吗？__ <br>
> 答：模组已有默认服务器信息，若无特殊需求则无需配置。<br>

> __13.设备生命周期(lifetime)是什么？设备连接开发者中心之前我需要设置吗？__ <br>
> 答：设备生命周期(lifetime)，指的是协议内部的心跳时间，若没有特殊要求不需要设置，使用默认值(120 s)即可。<br>

> __14.下行数据的缓存模式是什么？设备连接开发者中心之前我需要设置吗？__ <br>
> 答：开启缓存模式时，开发者中心下行的数据会缓存在模组内部，需要MCU发送读取指令将缓存数据读取出来；<br>
> 关闭缓存模式时，开发者中心下行数据会直接打印到串口，模组不会进行缓存；<br>
> 您可以根据自己实际需求决定是否需要设置，若不配置默认为非缓存模式。<br>

> __15. PDP上下文ID(contextID)是什么？设备连接开发者中心之前我需要设置吗？__ <br>
> 答：contextID是当模组除了使用平台功能以外，还需要使用模组进行其他传输业务时才需要设置的，若无特殊需求则不需要进行配置，默认关闭。<br>

> __16.设备与平台之间的连接加密模式是什么？设备连接开发者中心之前我需要设置吗？__ <br>
> 答：数据链路的一种加密模式，可根据实际需求进行选择，默认关闭。<br>

> __17.什么是物模型数据格式？设备连接开发者中心之前我需要设置吗？__ <br>
> 答：MCU与模组直接进行物模型数据传输时，可以选择TTLV或者JSON数据格式，两种格式区别如下：<br>
>
> * JSON格式是一种轻量级的数据交换格式，可直观查看数据内容，便于阅读与理解。<br>
> * TTLV格式是将数据压缩为较小的体积，但需运用脚本或Code调用TTLV解析库解读。TTLV格式相比JSON格式数据量更小，空间的利用率更高，不允许冗余字段的产生。<br>
> 您可以根据实际需求决定使用TTLV格式还是JSON格式，默认为TTLV格式。<br>

> __18.设备连接平台之前，我需要配置 DeviceKey 和 DeviceSecret 吗？__ <br>
> 答：__DeviceKey__ 一般不需要特别设置，模组默认使用IMEI（蜂窝模组）或者MAC地址（Wi-Fi模组）作为 __DeviceKey__。 __DeviceSecret__ 也不需要配置，模组第一次连接平台时，平台会生成 __DeviceSecret__ 并下发给模组（此步骤用户无感知）。

> __19.上行消息的\<txID\>是什么？设备连接开发者中心之前我需要设置吗？__ <br>
> 答：配置\<txID\>后，当设备发送上行物模型数据给平台时，会收到平台返回上行消息的\<txID\>(当QoS=1或者QoS=2时)。一般没有特殊要求不需要配置\<txID\>，默认不返回上行消息的\<txID\>。

> __20.设备如何在开发者中心认证？如何激活？__ <br>
> 答：设备在初次连接上开发者中心时，若有激活码的情况下，设备会自动认证并且激活。<br>

> __21.设备每次上电后都需要配置一下产品ProductKey(简称PK)、ProductSecret(简称PS)吗？__ <br>
> 答：MCU发送配置指令到模组后，模组会在内部记录配置信息，设备下次启动时该配置信息仍然有效，无需再次配置。<br>



## <span style="color:#A52A2A">__数据业务常见问题__</span>
> __1.数据格式有几种？__ <br>
> 答：数据格式有两种，分别为物模型与透传。

> __2.物模型是什么？__ <br>
> 答：物模型是指设备在开发者中心的数字化表示，并在开发者中心构建的实体数据模型，并分为属性、服务和事件三个维度。

> __3.透传数据格式是什么？__ <br>
> 答：透传数据格式是指设备上报透传数据到开发者中心，平台不做任何数据解析。

> __4.QoS是什么？__ <br>
> 答：QoS（Quality of Service）是服务质量的简称，提高服务质量就是保证传输的带宽，降低数据的丢包率以及延时抖动等，用户可根据实际的网络状态选择相对应的QoS。
>
> * QoS 当前有三个等级：0，1，2。<br>
> * 若设备与平台间需要高频且不可靠交互时，出于流量节省和高效性，可选择QoS为0。<br>
> * 若设备与平台间需要高频且需可靠交互时，出于流量节省和高效性，可选择QoS为1。<br>
> * 若设备与平台间需要高频且需可靠交互时，且需要严格遵循一发一收原则，可选择QoS为2。<br>

> __5.PkgID是什么？__ <br>
> 答：PkgID是上下行数据包的ID。例如，开发者在连续多次发送上行数据时，可能无法判别哪个数据发送成功或失败了，所以在发送数据时会先生成一个PkgID，在数据成功发送到平台后，平台再将该数据的PkgID通知给开发者，开发者即可以根据两个PkgID的一致性来判别数据是否发送成功。

> __6.如何发送物模型数据到开发者中心？__ <br>
> 答：MCU向模组发送 __AT+QIOTMODELTD__ 指令即可向开发者中心发送物模型数据。

> __7.如何接收物模型数据？__ <br>
> 答：若在缓存模式下，当开发者中心下发服务时，MCU可向模组发送 **AT+QIOTMODELRD** 指令查询数据。若在非缓存模式下，模组收到开发者中心下发的服务时会直接向MCU打印回调事件码与下行数据。

> __8.什么是缓存与非缓存模式？__ <br>
> 答：MCU读取数据可分为两种模式：缓存模式和非缓存模式。
>
> * 缓存模式是指当开发者中心下发数据时，模组收到后会主动向MCU打印回调事件码并把数据储存到模组内存里，MCU需主动发送  __AT+QIOTMODELRD__ 指令读取该下行数据。<br>
> * 非缓存模式是指当开发者中心下发数据时，模组收到后会主动向MCU打印回调事件码与下行数据。<br>

> __9.物模型数据有什么数据格式？__ <br>
> 答：物模型数据分为JSON格式与TTLV格式。
>
> * JSON格式是一种轻量级的数据交换格式，可直观查看数据内容方便阅读理解。<br>
> * TTLV格式是将数据压缩为较小的体积，但需运用脚本或Code调用TTLV解析库解读。TTLV格式相比JSON格式数据量更小，空间的利用率更高，不允许冗余字段的产生。<br>

> __10.AT指令中直接发送模式和透传发送模式的区别？__ <br>
> 答：
>
> * 直接发送模式: 支持任意字符串语句。<br>
> * 透传发送模式  支持任意字节流语句。<br>

> __11.设备上报物模型数据时，单次可上报的最大数据长度为多少字节？__ <br>
> 答：设备单次可上报的最大数据长度因模组型号而异。

> __12.为什么发送物模型数据失败，返回ERROR？__ <br>
> 答：<br>
> 原因一：当前设备还未连上开发者中心，请使用 __AT+QIOTSTATE?__ 指令查询当前设备与平台之间的连接状态。<br>
> 原因二：当前设备所发送的数据格式不正确，请检查发送的数据长度以及数据格式中是否有多余空格。<br>
> 原因三：设备上报的物模型数据长度，超过模组一次性可发送的最大物模型数据长度。<br>

> __13.为什么发送读取物模型数据指令失败，返回ERROR？__ <br>
> 答：当前设备未配置 __开启缓存模式__ ，可发送 __AT+QIOTCFG="buffer",1__ 开启缓存模式。 <br>

> __14.设备上报事件时，一定要携带属性参数吗？__ <br>
> 答：设备上报事件时，可以不携带参数。 <br>

> __15.设备上报物模型数据时，可以同时上报属性和事件吗？__ <br>
> 答：可以，设备上报物模型数据时可以一次性同时上报多条属性和事件。<br>

> __16.设备开启缓存模式时，最多可以缓存多少条数据？__ <br>
> 答：目前模组缓存模式最多可缓存 __10__ 条数据，若缓存数据未被及时读取，会被新下发的数据覆盖。 <br>

> __17.设备配置物模型数据格式为JSON格式后，模组底层发送到开发者中心的数据也是JSON格式吗？__ <br>
> 答：配置物模型数据为JSON格式后，只是MCU与模组之间交互的数据格式是JSON格式，模组底层发送至开发者中心的数据格式仍是TTLV格式。


## <span style="color:#A52A2A">__OTA升级常见问题__</span>
> __1.OTA是什么意思？__ <br>
> 答：OTA是指在线网络升级。可将升级固件包上传到开发者中心并创建好升级计划，当设备环境满足升级策略后，MCU会主动触发平台计划以确认升级。

> __2.OTA分为几种，分别是什么？__ <br>
> 答：OTA分为两种，分别是FOTA（模组固件升级）、SOTA（MCU固件升级）。

> __3.我该如何选择OTA？__ <br>
> 答：根据项目需求来定，若是给MCU升级则选择SOTA，若是给模组升级则选择FOTA。
>
> * 若用户需要在模组固件基础上进行二次开发，则需使用FOTA方式进行升级。<br>
> * 若用户使用移远模组与自身开发的设备进行开发，则需使用SOTA方式进行升级。<br>

> __4.整包和差分包的区别在哪里？__ <br>
> 答：<br>
>  1、制作方式 <br>
>   整包和差分包均可以实现固件升级，整包是用户项目通过编译器编译完整代码生成的固件；
> 而差分包是通过对比原始版本的代码差异，生成可以插入到原始固件中的固件（可称为补丁），并且只有使用特定的工具才能生成。<br>
>  2、流量 <br>
>   整包是属于完整项目编译的固件，故一般情况下，文件远大于差分包的大小，因此对于高延时、低带宽或者低功耗设备而言，差分包具备更好的优势。<br>
>  3、兼容性 <br>
>   差分包升级是基于上一个版本的固件进行修整，所以差分包升级无法兼容更低版本升级到最新版本，若需要全部升级，则需要创建多个组件或计划，所以对于大批量多版本设备而言，维护成本偏高。  

> __5.FOTA操作步骤是什么？__ <br>
> 答：<br>
> 1、用户需在开发者中心新增版本包并创建升级计划。 <br>
> 2、MCU主动向模组发送 __AT+QIOTOTAREQ__ 指令请求 OTA 升级计划，若设备升级环境满足升级策略即可确认升级。 <br>
> 3、MCU主动向模组发送 __AT+QIOTUPDATE=1__ 指令确认升级。 <br>
> 4、模组自动接收升级包后自动进入升级状态。 <br>

> __6.SOTA操作步骤是什么？__ <br>
> 答：<br>
> 1、MCU主动向模组发送 __AT+QIOTMCUVER__ 指令配置 MCU 版本号。 <br>
> 2、用户需在开发者中心新增版本包并创建升级计划。 <br>
> 3、MCU主动向模组发送 __AT+QIOTOTAREQ__ 指令请求 OTA 升级计划，若设备升级环境满足升级策略即可确认升级。 <br>
> 4、MCU主动向模组发送 __AT+QIOTUPDATE=1__ 指令确认升级。 <br>
> 5、MCU主动向模组发送 __AT+QIOTOTARD__ 指令分片读取SOTA 数据。 <br>
> 6、MCU接收完整升级包数据后则进入更新状态。 <br>
> 7、MCU升级完毕后设置新的MCU版本号并上传至开发者中心。 <br>

> __7.OTA为什么触发不了？__ <br>
> 答：<br>
> 原因一：当前设备未连接开发者中心。<br>
> 原因二：当前设备的属性不满足计划的要求，如用户剩余空间不足、当前信号弱、版本不匹配。<br>
> 原因三：用户所创建的计划不包含该设备。<br>

> __8.OTA为什么会失败？__ <br>
> 答：<br>
> 原因一：请确保当前SDK版本是否符合相关的AT指令，若2.9.2版本前的SDK使用2.9.2版本及之后的AT指令可能会发生未知错误。
>
> 原因二：设备进行OTA升级时，若设备断电、断网或与开发者中心断开连接直至超过了计划时间则都被认定为升级失败。<br>原因三：设备进行OTA升级时，若出现网络波动并持续了一段时间，设备将认为本次升级失败，并自动重新触发升级，若连续5次均失败，则认为该设备升级计划失败。<br>
>
> 原因四：若设备成功下载固件但升级失败，可能是在升级过程中损坏了升级文件，您可以关注与该升级文件储存区域相关的操作。<br>
> <br>

> __9.向平台请求 OTA 升级计划时，发送AT+QIOTOTAREQ指令和AT+QIOTOTAREQ=1指令有什么区别？__ <br>
> 答：
>
> * 使用 __AT+QIOTOTAREQ__ 指令请求OTA时，平台下发升级计划时不会携带固件的SHA256校验信息；
> * 使用 __AT+QIOTOTAREQ=1__ 指令请求OTA时，平台下发升级计划时会携带固件包的SHA256校验信息；<br>
> 您可以根据自己的需求来决定是否需要使用固件的SHA256校验信息。<br>

> __10.为什么SOTA升级时，设备发送OTA请求指令后，无法收到升级计划？__ <br>
> 答：<br>
>    原因一：平台未创建升级计划或者计划未激活。<br>
>    原因二：模组未设置MCU版本号，需要发送 __AT+QIOTMCUVER__ 指令设置MCU版本号。 <br>

> __11.为什么SOTA升级时，模组不能一次性下载所有升级包？__ <br>
> 答：模组空闲内存不够，无法一次性存放所有升级包，所以会采取分包下载，模组空闲内存大小因模组型号而异。 <br>

> __12.为什么SOTA升级时，模组不会自动下载下一块固件数据？__ <br>
> 答：SOTA升级时，当设备从开发者中心分块下载固件时，在下载完第一块后，需要MCU发送指令 __AT+QIOTUPDATE=2__ 才会下载下一块数据。

> __13.设备端如何判断设备OTA升级是否成功？__ <br>
> 答：当设备OTA升级成功时，设备端会打印事件响应码：__7,10705__ ，或在开发者中心升级计划界面查看计划升级成功。<br>


## <span style="color:#A52A2A">__设备定位常见问题__</span>
> __1.设备定位有几种方式？__ <br>
> 答：QuecThing 设备定位有三种，分别是LBS基站定位、GNSS定位、Wi-Fi定位。

> __2. LBS基站定位特点？__ <br>
> 答：LBS基站定位覆盖率高，但要求室内室外都有信号，定位精度较低，无需外置模组。

> __3. GNSS定位特点？__ <br>
> 答：GNSS定位速度快，全天候作业，不需要考虑目视通视条件，可以远距离大范围测量。但受天顶方向遮盖影响极大，在室内、隧道内精度下降。

> __4.Wi-Fi定位特点？__ <br>
> 答：Wi-Fi 定位速度快，在密集人流多的地方相当精确。但依赖于W-iFi，没有打开Wi-Fi就无法定位，并且Wi-Fi必须处于联网状态。<br>

> __5. LBS基站定位该如何使用？__ <br>
> 答：QuecThing已包含LBS基站定位组件，MCU只需使用 __AT+QIOTLOCIN__ 指令即可获取/上报设备内置定位功能的定位数据。

> __6. GNSS定位该如何使用？__ <br>
> 答：GNSS定位需要外置GNSS模组，需将GNSS模组读取的坐标系读取到MCU，MCU再使用 __AT+QIOTLOCEXT__ 指令上报设备外置定位功能的定位数据。

> __7. Wi-Fi定位该如何使用？__ <br>
> 答：若模组支持Wi-Fi定位功能，MCU可使用 __AT+QIOTLOCIN=1,"WIFI"__ 指令获取Wi-Fi定位数据，使用 __AT+QIOTLOCIN=0,"WIFI"__ 指令上报设备Wi-Fi定位数据。

> __8.我该选择哪种定位方式？__ <br>
> 答：<br>
> 若对设备的定位精度要求不高，可选择使用LBS定位；<br>
> 若对设备的定位精度要求较高，可选择使用GNSS定位；<br>
> 若对设备定位精度要求不高，并且模组支持Wi-Fi定位，可选择Wi-Fi定位。<br>

> __9.设备端如何判断设备定位数据上报成功？__ <br>
> 答：当设备上报定位数据成功时，模组会打印事件回调码：__+QIOTEVT: 4,10220__ 。 <br>

> __10.为什么发送定位数据失败，返回ERROR？__ <br>
> 答：<br>
> 原因一：当前设备还未连接上平台，请在设备连接平台之后再上报定位数据。 <br>
> 原因二：当前设备的固件不支持上报定位数据的指令，请测试:
>
> * 发送 __AT+QIOTLOCIN=?__ 查询固件是否支持上报内置定位指令。  <br>
> * 发送 __AT+QIOTLOCEXT=?__ 查看固件是否支持上报外置定位指令。 <br>


## <span style="color:#A52A2A">__网关与子设备常见问题__</span>
> __1.网关是什么？__ <br>
> 答：网关的功能是转换不同设备间的通讯协议，可以让不具有联网能力的子设备通过网关连接上平台。

> __2.子设备是什么？__ <br>
> 答：子设备是不具备联网能力的设备，需通过与网关的连接才能与平台通讯。

> __3.子设备心跳是什么？__ <br>
> 答：子设备的心跳指的是 网关需要定期检测子设备是否已断开的周期。<br>
> * 若在指定的心跳周期内，子设备发送心跳信息给网关，网关将刷新子设备的检测周期；<br>
> * 若在指定的心跳周期内，子设备没有发送心跳信息给网关，网关将认为子设备已断开，则网关关闭该接口。<br>

> __4.直连设备、网关和子设备的区别？__ <br>
> 答：
>
> * 直连设备指的是不具有网关或子设备的关联关系的产品。<br>
> * 网关和子设备指的是具备成为网关或子设备的产品。<br>

> __5.为什么子设备初次连接平台成功后，后续连接平台时设备会打印回调事件码 **1,10422** ？__ <br>
> 答：子设备初次连接平台时，不需要携带子设备DeviceSecret，但后续连接平台时需要携带DeviceSecret。 <br>


## <span style="color:#A52A2A">__其他常见问题__</span>
> __1.为什么使用Qflash烧录固件时，显示烧录失败？__ <br>
> 答：<br>
> 原因一：没有正确安装模组驱动。<br>
> 原因二：固件的存放路径下有中文或者空格。<br>

> __2.为什么设备上报告警事件，移联万物APP却没有弹窗提醒？__ <br>
> 答：开发者中心没有配置消息通知规则，用户只需在开发者中心“产品开发”->“产品”->“消息通知”配置一下消息规则即可。  

> __3.为什么开发者中心下行数据时，MCU仿真工具提示无法匹配数据？__ <br>
> 答：MCU仿真工具没有导入物模型，导致工具无法解析平台下发的物模型，需要在工具中导入物模型文件。 <br>




# 群组控制

## 功能概述

设备群组，将多个设备组织起来，形成一个群体，以便于管理和控制。

> 注意
>
> **群组功能只有在家居模式启动后, 才能使用群组功能**
>
> SDK所有需要传fid参数的地方, 均可以调用QuecSmartHomeService.currentFamily来获取

## SDK集成方式

> **注意**
>
> 群组SDK运行依赖核心库QuecIotSdk, 请先按照快速集成文档集成核心库

```objc
pod 'QuecGroupKit', '~> 0.5.0'
```

**QuecGroupBean属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| name       | NSString             | 群组名称    |
| fid  | NSString            | 家庭id |
| frid | NSString | 房间id    |
| gdid | NSString | 群组id    |
| roomName | NSString | 房间名    |
| onlineStatus | NSUInteger | 设备状态：0-离线 1-在线    |
| productKey       | NSString             | 产品pk    |
| deviceKey  | NSString            | 设备dk |
| isCommonUsed | BOOL | 是否是常用设备：0-不常用 1-常用    |
| groupDeviceDeviceNum | NSInteger | 群组包含设备数量    |
| deviceList | NSArray<QuecGroupDeviceBean *> * | 设备列表    |

**QuecGroupDeviceBean属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| productKey       | NSString             | 产品pk    |
| deviceKey  | NSString            | 设备dk |
| deviceName | NSString | 设备名    |
| logoImage | NSString | 设备logo图片    |
| frid | NSString | 房间id    |
| roomName | NSString | 房间名称    |
| onlineStatus | NSInteger | 设备云状态：0-离线 1-在线    |
| msg | NSString | 接口响应消息提示    |
| code | NSInteger | 接口响应code    |
| lowPowerProduct | BOOL | 是否为低功耗产品    |
| lowPowerStatus | BOOL | 低功耗开关状态    |
| lowPowerCache | NSInteger | 低功耗缓存时长，单位为秒    |
| lowPowerAlive | NSInteger | 低功耗心跳周期（分）    |
| lastOfflineTimeTs | NSInteger | 离线时间戳    |
| capabilitiesBitmask | QuecIotChannelBitMask | 设备通道能力mask    |

```objc
typedef NS_OPTIONS(NSUInteger, QuecIotChannelBitMask)
{
    QuecIotChannelBitMaskWan = 1 << 0,
    QuecIotChannelBitMaskLan = 1 << 1,
    QuecIotChannelBitMaskBle = 1 << 2,
    QuecIotChannelBitMaskMatter = 1 << 3
};
```

### 创建群组

**接口说明**

创建群组

```objc
- (void)createGroupWithBean:(QuecGroupCreateBean *)bean
                    success:(QuecCreateGroupSuccess)success
                    failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- | 
| bean |	是|QuecGroupCreateBean类对象| 
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**QuecGroupCreateBean属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| groupDeviceName       | NSString             | 群组名称    |
| fid  | NSString            | 家庭id |
| frid | NSString | 房间id    |
| isCommonUsed | BOOL | 是否是常用设备：0-不常用 1-常用    |
| deviceList | NSArray<QuecGroupCreateDeviceBean *> * | 设备列表    |

**QuecGroupCreateDeviceBean属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| productKey       | NSString             | 产品pk    |
| deviceKey  | NSString            | 设备dk |

**示例代码**

```objc
QuecGroupCreateBean *createBean = QuecGroupCreateBean.new;
createBean.groupDeviceName = @"group name";
createBean.fid = @"family id";
createBean.frid = @"family room id";
createBean.isCommonUsed = YES;
createBean.deviceList = @[QuecGroupCreateDeviceBean.new];
[QuecGroupService.sharedInstance createGroupWithBean:createBean success:^(QuecGroupCreateResultBean * _Nonnull result) {
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 群组详情

**接口说明**

查询群组详情

```objc
- (void)getGroupInfoWithId:(NSString *)groupId
                   success:(QuecGroupBeanSuccess)success
                   failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- | 
| groupId |	是|群组id| 
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

> 返回类型为QuecGroupBean, 属性定义同上

**示例代码**

```objc
[QuecGroupService.sharedInstance getGroupInfoWithId:@"your gdid" success:^(QuecGroupBean * _Nonnull result) {
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 群组基础信息

**接口说明**

查询群组基础信息

```objc
- (void)getGroupDeviceInfoWithId:(NSString *)groupId
                         success:(QuecGroupDeviceSuccess)success
                         failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- | 
| groupId |	是|群组id| 
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

> 返回类型为QuecDeviceModel, 属性定义在设备SDK

**示例代码**

```objc
[QuecGroupService.sharedInstance getGroupDeviceInfoWithId:@"your gdid" success:^(QuecDeviceModel * _Nonnull result) {
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 设备是否可加入群组

**接口说明**

批量判断设备是否可加入群组

```objc
- (void)checkDeviceAddGroupWithList:(NSArray<QuecGroupCreateDeviceBean *> *)deviceList
                                fid:(NSString *)fid
                            success:(QuecGroupCheckDeviceSuccess)success
                            failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- | 
| deviceList |	是|QuecGroupCreateDeviceBean类对象| 
| fid |	是|家庭id| 
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

>QuecGroupDeviceBean QuecGroupCreateDeviceBean属性定义同上

**示例代码**

```objc
QuecGroupCreateDeviceBean *createDeviceBean = QuecGroupCreateDeviceBean.new;
createDeviceBean.productKey = @"your pk";
createDeviceBean.deviceKey = @"your dk";
[QuecGroupService.sharedInstance checkDeviceAddGroupWithList:@[createDeviceBean] fid:@"fid" success:^(NSArray<QuecGroupDeviceBean *> * _Nonnull result) {
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 可添加设备列表

**接口说明**

获取可添加设备列表

```objc
- (void)getAddableListWithList:(NSArray<QuecGroupCreateDeviceBean *> *)addedList
                           fid:(NSString *)fid
                          gdid:(nullable NSString *)gdid
                      pageSize:(NSInteger)pageSize
                          page:(NSInteger)page
                       success:(QuecGroupAddableListSuccess)success
                       failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- | 
| addedList |	是|QuecGroupCreateDeviceBean| 
| fid |	是|家庭id| 
| gdid |	否|群组id| 
| pageSize |	否|分页大小，默认为 10 条| 
| page |	否|分页，默认为第 1 页| 
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

> QuecGroupCreateDeviceBean属性定义同上

**示例代码**

```objc
QuecGroupCreateDeviceBean *createDeviceBean = QuecGroupCreateDeviceBean.new;
createDeviceBean.productKey = @"your pk";
createDeviceBean.deviceKey = @"your dk";
[QuecGroupService.sharedInstance getAddableListWithList:@[createDeviceBean]
                                                        fid:@"your fid"
                                                       gdid:@"your gdid"
                                                   pageSize:10
                                                       page:1
                                                    success:^(NSArray<QuecGroupDeviceBean *> * _Nonnull result, NSUInteger total) {
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 控制群组

**接口说明**

控制群组设备

```objc
- (void)controlGroupByHttp:(NSArray<QuecIotDataPoint*> *)dps
                   groupId:(NSString *)groupId
                 extraData:(NSDictionary *)extraData
                   success:(QuecVoidBlock)success
                   failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- | 
| dps |	是|dps数据,具体格式封装同设备SDK| 
| groupId |	是|群组id| 
| extraData |	否|额外数据| 
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**extraData额外数据NSDictionary 传入说明**

```objc
{
 type 类型 1：透传 2：属性 3：服务
 dataFormat 数据类型 1：Hex 2：Text（当type为透传时，需要指定 dataFormat）
 cacheTime 缓存时间，单位为秒，缓存时间范围 1-7776000 秒，启用缓存时必须设置缓存时间
 isCache  是否启用缓存 1：启用 2：不启用，默认不启用
 isCover 是否覆盖之前发送的相同的命令 1：覆盖 2：不覆盖，默认不覆盖，启用缓存时此参数有效
 }
```

> QuecIotDataPoint属性定义同设备SDK

**示例代码**

```objc
QuecIotDataPoint *dp = QuecIotDataPoint.new;
dp.value = @(3);
dp.dataType = QuecIotDataPointDataTypeINT;
dp.Id = 2; /// tsl id
[QuecGroupService.sharedInstance controlGroupByHttp:@[dp] groupId:@"your gdid" extraData:@{} success:^{
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 查询群组物模型属性值

**接口说明**

查询群组物模型属性值

```objc
- (void)groupAttributesById:(NSString *)groupId
                   codeList:(NSString *)codeList
                    success:(void (^)(QuecProductTSLInfoModel *tslInfoModel))success
                    failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- | 
| groupId |	是|群组id| 
| codeList |	否|需要查询的属性值，用英文逗号拼接| 
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

> QuecProductTSLInfoModel属性定义同设备SDK

**示例代码**

```objc
[QuecGroupService.sharedInstance groupAttributesById:@"gdid" codeList:@"code1,code2" success:^(QuecProductTSLInfoModel * _Nonnull tslInfoModel) {
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 群组物模型属性,包含属性值

**接口说明**

查询群组物模型属性,包含属性值

```objc
- (void)groupAttributesWithTSLById:(NSString *)groupId
                        productKey:(NSString *)productKey
                          codeList:(NSString *)codeList
                           success:(void (^)(NSArray<QuecProductTSLPropertyModel *> *arrayData))success
                           failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- | 
| bean |	是|QuecGroupCreateBean类对象| 
| productKey |	是|产品pk| 
| codeList |	否|需要查询的属性值，用英文逗号拼接| 
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

> QuecProductTSLPropertyModel属性定义同设备SDK

**示例代码**

```objc
[QuecGroupService.sharedInstance groupAttributesWithTSLById:@"your gdid" productKey:@"your pk" codeList:@"code1" success:^(NSArray<QuecProductTSLPropertyModel *> * _Nonnull arrayData) {
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 编辑群组基础信息

**接口说明**

用于群组基础信息修改

```objc
- (void)editGroupBasicInfoWithId:(NSString *)groupId
                 groupDeviceName:(NSString *)groupDeviceName
                             fid:(nullable NSString *)fid
                            frid:(nullable NSString *)frid
                    isCommonUsed:(BOOL)isCommonUsed
                         success:(QuecVoidBlock)success
                         failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- | 
| groupId |	是|群组id| 
| groupDeviceName |	是| 群组名称| 
| fid |	否|家庭id| 
| frid |	否|房间id| 
| isCommonUsed |	否|是否是常用设备：0-不常用 1-常用| 
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**示例代码**

```objc
[QuecGroupService.sharedInstance editGroupBasicInfoWithId:@"your gdid" groupDeviceName:@"name" fid:@"fid" frid:@"frid" isCommonUsed:YES success:^{
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 编辑群组信息

**接口说明**

编辑群组信息

```objc
- (void)editGroupInfoWithId:(NSString *)groupId
                       name:(NSString *)groupDeviceName
                        fid:(NSString *)fid
                       frid:(NSString *)frid
               isCommonUsed:(BOOL)isCommonUsed
                 deviceList:(NSArray<QuecGroupCreateDeviceBean *> *)deviceList
                    success:(QuecVoidBlock)success
                    failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- | 
| groupId |	是|群组id| 
| groupDeviceName |	是| 群组名称| 
| fid |	否|家庭id| 
| frid |	否|房间id| 
| deviceList |	是|设备列表| 
| isCommonUsed |	否|是否是常用设备：0-不常用 1-常用| 
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**示例代码**

```objc
QuecGroupCreateDeviceBean *createDeviceBean = QuecGroupCreateDeviceBean.new;
createDeviceBean.productKey = @"your pk";
createDeviceBean.deviceKey = @"your dk";
[QuecGroupService.sharedInstance editGroupInfoWithId:@"your gdid"
                                                name:@"name"
                                                    fid:@"fid"
                                                frid:@"frid"
                                        isCommonUsed:YES
                                            deviceList:@[createDeviceBean] success:^{
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 批量移除/添加群组到常用

**接口说明**

批量移除/添加群组到常用

```objc
- (void)batchAddCommonWithIds:(NSArray<NSString *> *)groupIds
                          fid:(NSString *)fid
                 isCommonUsed:(BOOL)isCommonUsed
                      success:(QuecGroupBatchResultBlock)success
                      failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- | 
| groupIds |	是|群组id List| 
| fid |	否|家庭id|
| isCommonUsed |	否|是否是常用设备：0-不常用 1-常用| 
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**QuecGroupBatchResultBean属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| code       | NSString             | code码    |
| msg       | NSString             | 提示信息    |
| gdid       | NSString             | 群组id    |

**示例代码**

```objc
[QuecGroupService.sharedInstance batchAddCommonWithIds:@[@"group id1", @"group id2"] fid:@"fid" isCommonUsed:YES success:^(NSArray<QuecGroupBatchResultBean *> * _Nonnull successList, NSArray<QuecGroupBatchResultBean *> * _Nonnull failList) {
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 移动群组到新房间

**接口说明**

批量移动群组到新房间, 需开启家庭模式

```objc
- (void)batchMovingWithIds:(NSArray<NSString *> *)groupIds
                   newFrid:(NSString *)newFrid
                   success:(QuecGroupBatchResultBlock)success
                   failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- | 
| groupIds |	是|群组id List| 
| newFrid |	否|新房间id|
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

>QuecGroupBatchResultBean属性定义同上

**示例代码**

```objc
[QuecGroupService.sharedInstance batchMovingWithIds:@[@"group id1", @"group id2"] newFrid:@"new frid" success:^(NSArray<QuecGroupBatchResultBean *> * _Nonnull successList, NSArray<QuecGroupBatchResultBean *> * _Nonnull failList) {
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 批量删除群组

**接口说明**

批量删除群组

```objc
- (void)deleteGroupWithIds:(NSArray<NSString *> *)groupIds
                   success:(QuecGroupBatchResultBlock)success
                   failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- | 
| groupIds |	是|群组id List| 
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

>QuecGroupBatchResultBean属性定义同上

**示例代码**

```objc
[QuecGroupService.sharedInstance deleteGroupWithIds:@[@"group id1", @"group id2"] success:^(NSArray<QuecGroupBatchResultBean *> * _Nonnull successList, NSArray<QuecGroupBatchResultBean *> * _Nonnull failList) {
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 设置群组分享信息

**接口说明**

分享人设置群组分享信息

```objc
- (void)getShareCodeWithGroupId:(NSString *)groupId
              acceptingExpireAt:(long)acceptingExpireAt
           isSharingAlwaysValid:(BOOL)isSharingAlwaysValid
                sharingExpireAt:(long)sharingExpireAt
                      coverMark:(NSInteger)coverMark
                        success:(void(^)(NSString *shareCode))success
                        failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- | 
| groupId |	是|群组Id| 
| acceptingExpireAt |	是|分享二维码种子失效时间 时间戳（毫秒），表示该分享在此时间戳时间内没有使用，会失效| 
| isSharingAlwaysValid |	是|设备是否永久有效| 
| sharingExpireAt |	是|设备使用到期时间 时间戳（毫秒），表示该分享的群组，被分享人可以使用的时间<br/>如果不填，则为终生有效| 
| coverMark |	是|覆盖标志<br/>1 直接覆盖上条有效分享（默认）（覆盖原有的分享码）<br/>2 直接添加，允许多条并存<br/>3 只有分享时间延长了，才允许覆盖上条分享| 
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**示例代码**

```objc
[QuecGroupService.sharedInstance getShareCodeWithGroupId:@"your groupId" acceptingExpireAt:1745214106879 isSharingAlwaysValid:YES sharingExpireAt:sharingExpireTS(MS) coverMark:1 success:^(NSString * _Nonnull shareCode) {
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 群组分享人列表

**接口说明**

获取群组分享人列表

```objc
- (void)getSharedListsWithGroupId:(NSString *)groupId
                          success:(void(^)(NSArray<QuecShareUserModel *> *list))success
                          failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- | 
| groupId |	是|群组Id| 
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**QuecShareUserModel属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| shareInfo       | QuecShareUserShareInfoModel             | 分享信息    |
| userInfo       | QuecShareUserInfoModel             | 用户信息    |

**QuecShareUserShareInfoModel属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| acceptTime       | NSString             | 分享时间    |
| acceptingExpireAt       | NSString             | 分享失效时间    |
| coverMark       | NSInteger             |    覆盖标志 |
| deleteTime       | NSString             | 删除时间    |
| deviceName       | NSString             | 设备名称    |
| dk       | NSString             | 设备key    |
| ownerUid       | NSString             | 所有者ID    |
| pk       | NSString             | 产品key    |
| shareCode       | NSString             | 分享码    |
| shareId       | NSString             | 分享ID    |
| shareStatus       | NSInteger             |  分享状态    |
| shareTime       | NSString             | 分享时间    |
| shareUid       | NSString             | 分享用户ID    |
| sharingExpireAt       | NSString             | 分享失效时间    |
| updateTime       | NSString             | 更新时间    |

**示例代码**

```objc
[QuecGroupService.sharedInstance getSharedListsWithGroupId:@"your groupId" success:^(NSArray<QuecShareUserModel *> * _Nonnull list) {
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 分享人取消分享

**接口说明**

分享人取消分享

```objc
- (void)ownerUnShareWithShareCode:(NSString *)shareCode
                          success:(QuecVoidBlock)success
                          failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- | 
| shareCode |	是|分享码| 
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	|

**示例代码**

```objc
[QuecGroupService.sharedInstance ownerUnShareWithShareCode:@"share code" success:^{
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 被分享人取消分享

**接口说明**

被分享人取消分享

```objc
- (void)unShareWithShareCode:(NSString *)shareCode
                     success:(QuecVoidBlock)success
                     failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- | 
| shareCode |	是|分享码| 
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**示例代码**

```objc
[QuecGroupService.sharedInstance unShareWithShareCode:@"share code" success:^{
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 接受分享

**接口说明**

被分享人接受分享

```objc
- (void)acceptShareWithShareCode:(NSString *)shareCode
                         success:(QuecVoidBlock)success
                         failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- | 
| shareCode |	是|分享码| 
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**示例代码**

```objc
[QuecGroupService.sharedInstance acceptShareWithShareCode:@"share code" success:^{
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 分享群组的信息

**接口说明**

查询分享群组的信息

```objc
- (void)getShareGroupInfoWithShareCode:(NSString *)shareCode
                            success:(void (^)(QuecGroupBasicInfoBean *infoBean))success
                            failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- | 
| shareCode |	是|分享码| 
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**QuecGroupBasicInfoBean属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| gdid       | NSString             | 群组id    |
| productkey       | NSString             | 产品pk    |
| groupDeviceName       | NSString             | 群组名称    |
| onlinestatus       | NSUInteger             | 群组在离线状态 0-离线 1-在线    |
| logoImage       | NSString             | 群组logo    |
| fid       | NSString             | 家庭id    |
| frid       | NSString             | 房间id    |

**示例代码**

```objc
[QuecGroupService.sharedInstance getShareGroupInfoWithShareCode:@"share code" success:^(QuecGroupBasicInfoBean * _Nonnull infoBean) {
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

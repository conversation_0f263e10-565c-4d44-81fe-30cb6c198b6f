# 设备分组

## 功能概述

设备分组是指将多个设备归为一组，便于管理，比如将多个设备归为一组，便于进行批量控制、批量查询等操作。

> QuecDeviceModel属性定义参考[设备管理](设备管理.md)

```kotlin
QuecDeviceGroupService
```

## 设备分组

### 分组列表

**接口说明**

获取分组列表, 可先调用添加分组接口后获取分组列表数据

```kotlin
fun getDeviceGroupList(
    pageNumber: Int,
    pageSize: Int,
    extra: QuecDeviceGroupParamModel?,
    callback: QuecCallback<QuecPageResponse<QuecDeviceGroupInfoModel>>
)
```

**参数说明**

| 参数         | 	是否必传 | 说明     |	
|------------|-------|--------|  
| pageNumber | 是     | 页码     |
| pageSize   | 是     | 页数据数量  |
| infoModel  | 否     | 分组拓展信息 |
| callback   | 是     | 请求回调   |

**QuecDeviceGroupParamModel属性定义**

| 字段               | 类型     | 描述            |
|------------------|--------|---------------|
| name             | String | 名称，创建分组时该字段必传 |
| address          | String | 地址            |
| contactPhoneList | String | 联系人           |
| coordinate       | String | 经纬度           |
| coordinateSystem | String | 坐标系           |
| descrip          | String | 说明            |
| manager          | String | 管理员           |
| managerType      | String | 管理员类型         |
| parentId         | String | 父设备组ID        |
| extend           | String | 拓展字段          |

**QuecDeviceGroupInfoModel属性定义**

| 字段               | 类型     | 描述            |
|------------------|--------|---------------|
| name             | String | 名称，创建分组时该字段必传 |
| address          | String | 地址            |
| contactPhoneList | String | 联系人           |
| coordinate       | String | 经纬度           |
| coordinateSystem | String | 坐标系           |
| descrip          | String | 说明            |
| manager          | String | 管理员           |
| managerType      | String | 管理员类型         |
| parentId         | String | 父设备组ID        |
| extend           | String | 拓展字段          |
| dgid             | String | 分组ID          |
| owner            | String | 拥有者           |
| addTime          | String | 添加时间          |
| addTimeTs        | String | 添加时间戳         |

**示例代码**

```kotlin
QuecDeviceGroupService.getDeviceGroupList(1, 10, null) {
    if (it.isSuccess) {
        val data = it.data //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 添加设备分组

**接口说明**

添加设备分组

```kotlin
fun addDeviceGroup(
    groupInfoModel: QuecDeviceGroupParamModel,
    callback: QuecCallback<Unit>
)
```

**参数说明**

| 参数             | 	是否必传 | 说明   |	
|----------------|-------|------|  
| groupInfoModel | 是     | 分组信息 |
| callback       | 是     | 请求回调 |

> QuecDeviceGroupParamModel属性定义同上

**示例代码**

```kotlin
QuecDeviceGroupService.addDeviceGroup(
    QuecDeviceGroupParamModel(
        name = "name"
    )
) {
    if (it.isSuccess) {
        //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 修改设备组

**接口说明**

修改设备组信息

```kotlin
fun updateDeviceGroupInfo(
    deviceGroupId: String,
    infoModel: QuecDeviceGroupParamModel,
    callback: QuecCallback<Unit>
)
```

**参数说明**

| 参数            | 	是否必传 | 说明   |	
|---------------|-------|------|  
| deviceGroupId | 是     | 分组ID |
| infoModel     | 是     | 分组信息 |
| callback      | 是     | 请求回调 |

> QuecDeviceGroupParamModel属性定义同上

**示例代码**

```kotlin
val info = getInfo() //从云端获取的分组信息
info.name = "name"
QuecDeviceGroupService.updateDeviceGroupInfo(
    "id",
    info
) {
    if (it.isSuccess) {
        //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 删除设备组

**接口说明**

根据分组Id删除设备组

```kotlin
fun deleteDeviceGroup(
    deviceGroupId: String,
    callback: QuecCallback<Unit>
)
```

**参数说明**

| 参数            | 	是否必传 | 说明   |	
|---------------|-------|------|  
| deviceGroupId | 是     | 分组id |
| callback      | 是     | 请求回调 |

**示例代码**

```kotlin
QuecDeviceGroupService.deleteDeviceGroup("groupId") {
    if (it.isSuccess) {
        //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 获取分组详情

**接口说明**

获取分组详情信息

```kotlin
fun getDeviceGroupInfo(
    deviceGroupId: String,
    callback: QuecCallback<QuecDeviceGroupInfoModel>
)
```

**参数说明**

| 参数            | 	是否必传 | 说明   |	
|---------------|-------|------|  
| deviceGroupId | 是     | 分组id |
| callback      | 是     | 请求回调 |

> QuecDeviceGroupInfoModel属性定义同上

**示例代码**

```kotlin
QuecDeviceGroupService.getDeviceGroupInfo("groupId") {
    if (it.isSuccess) {
        val data = it.data //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 分组设备列表

**接口说明**

获取分组设备列表, 列表数据由[添加设备到设备组中]接口加入

```kotlin
fun getDeviceList(
    deviceGroupId: String?,
    deviceGroupName: String?,
    deviceKeyList: String?,
    productKey: String?,
    pageNumber: Int,
    pageSize: Int,
    callback: QuecCallback<QuecPageResponse<QuecDeviceModel>>
)
```

**参数说明**

| 参数              | 	是否必传 | 说明                                 |	
|-----------------|-------|------------------------------------|  
| deviceGroupId   | 是     | 分组id                               |
| deviceGroupName | 否     | 分组名称                               |
| deviceKeyList   | 否     | device key列表，多个device key，使用英文逗号分隔 |
| productKey      | 否     | 产品key                              |
| pageNumber      | 是     | 当前页，默认为第 1 页                       |
| pageSize        | 是     | 页大小，默认为 10 条                       |
| callback        | 是     | 请求回调                               |

> QuecDeviceModel属性定义同上

**示例代码**

```kotlin
QuecDeviceGroupService.getDeviceList("groupId", null, null, null, 1, 10) {
    if (it.isSuccess) {
        val data = it.data //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误
    }
}
```

### 添加设备到设备组中

**接口说明**

将设备添加到设备组中

```kotlin
fun addDeviceToGroup(
    deviceGroupId: String,
    deviceList: List<QuecDeviceModel>,
    callback: QuecCallback<QuecOperateDeviceToGroupModel>
)
```

**参数说明**

| 参数            | 	是否必传 | 说明   |	
|---------------|-------|------|  
| deviceGroupId | 是     | 分组id |
| deviceList    | 是     | 设备列表 |
| callback      | 是     | 请求回调 |

**QuecOperateDeviceToGroupModel属性定义**

| 字段          | 类型                                        | 描述   |
|-------------|-------------------------------------------|------|
| successList | List&lt;QuecOperateDeviceToGroupRstModel> | 成功列表 |
| failureList | List&lt;QuecOperateDeviceToGroupRstModel> | 失败列表 |

**QuecOperateDeviceToGroupFailRstModel属性定义**

| 字段   | 类型                               | 描述   |
|------|----------------------------------|------|
| data | QuecOperateDeviceToGroupRstModel | 设备信息 |
| msg  | String                           | 错误提示 |

**QuecOperateDeviceToGroupRstModel属性定义**

| 字段 | 类型     | 描述   |
|----|--------|------|
| pk | String | 产品pk |
| dk | String | 设备dk |

**示例代码**

```kotlin
QuecDeviceGroupService.addDeviceToGroup(
    "groupId",
    listOf(QuecDeviceModel("productKey", "deviceKey"))
) {
    if (it.isSuccess) {
        val data = it.data //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 删除分组中的设备

**接口说明**

将分组中的设备删除

```kotlin
fun deleteDeviceFromGroup(
    deviceGroupId: String,
    deviceList: List<QuecDeviceModel>,
    callback: QuecCallback<QuecOperateDeviceToGroupModel>
)
```

**参数说明**

| 参数            | 	是否必传 | 说明   |	
|---------------|-------|------|  
| deviceGroupId | 是     | 分组id |
| deviceList    | 是     | 设备列表 |
| callback      | 是     | 请求回调 |

> QuecOperateDeviceToGroupModel属性定义同上

**示例代码**

```kotlin
QuecDeviceGroupService.deleteDeviceFromGroup(
    "groupId",
    listOf(QuecDeviceModel("productKey", "deviceKey"))
) {
    if (it.isSuccess) {
        //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 网关设备下子设备列表

**接口说明**

查询网关设备下子设备列表

```kotlin
fun getGatewayDeviceChildList(
    deviceKey: String,
    productKey: String,
    pageNumber: Int,
    pageSize: Int,
    callback: QuecCallback<QuecPageResponse<QuecDeviceModel>>
)
```

**参数说明**

| 参数         | 	是否必传 | 说明   |	
|------------|-------|------|  
| deviceKey  | 是     | 设备dk |
| productKey | 是     | 产品pk |
| pageNumber | 是     | 页码   |
| pageSize   | 是     | 页大小  |
| callback   | 是     | 请求回调 |

> QuecDeviceModel属性定义同上

**示例代码**

```kotlin
QuecDeviceGroupService.getGatewayDeviceChildList("deviceKey", "productKey", 1, 10) {
    if (it.isSuccess) {
        val data = it.data //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 不在设备组内的设备列表

**接口说明**

查询不在设备组内的设备列表, 用于设备过滤添加进设备组

```kotlin
fun getDeviceListByNotInDeviceGroup(
    pageNumber: Int,
    pageSize: Int,
    groupId:String,
    callback: QuecCallback<QuecPageResponse<QuecDeviceModel>>
)
```

**参数说明**

| 参数         | 	是否必传 | 说明    |	
|------------|-------|-------|  
| pageNumber | 是     | 页码    |
| pageSize   | 是     | 页大小   |
| groupId    | 是     | 设备组ID |
| callback   | 是     | 请求回调  |

> QuecDeviceModel属性定义同上

**示例代码**

```kotlin
QuecDeviceGroupService.getDeviceListByNotInDeviceGroup(1, 10, "groupId") {
    if (it.isSuccess) {
        val data = it.data //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

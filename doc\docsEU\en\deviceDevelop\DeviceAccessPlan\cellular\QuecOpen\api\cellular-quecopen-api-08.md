# Device Authorization API


## **Device Authorization API Overview**

|                  Function                   |        Description         |
| :-----------------------------------------: | :------------------------: |
| [Ql_iotDBindcodeSet()](#Ql_iotDBindcodeSet) |   Sets device bind code.   |
| [Ql_iotDBindcodeGet()](#Ql_iotDBindcodeGet) |   Gets device bind code.   |
|          [Ql_iotRst()](#Ql_iotRst)          | Resets device information. |

## **API Description**

<span id="Ql_iotDBindcodeSet">  </span>

## <span style="color:#A52A2A">__Ql_iotDBindcodeSet__</span>

This function enables binding the device to the App. This function is valid only when the device is bound to the App through short-range network (including TCP/IP local area network and BLE). To bind the device , the bind code reported by the device to Developer Center must match the one in the App.

__Prototype__

```c
qbool Ql_iotDBindcodeSet(const char *bindcode, quint32_t timeout)
```

__Parameter__
* __Input__
  * __quint8_t *__ __`bindcode`__: Device bind code. Range: 0–9, A–F. If this parameter is not configured, the bind code will be randomly generated by the system. Code length is fixed to 16 bytes.
  * __quint32_t__  __`timeout`__: Allowed binding time. Range: 0-4294967295. Unit: second. When this parameter is 0, it indicates the binding is canceled.

__Return Value__
* __`True:`__ Successful execution
* __`False`__: Failed execution

__NOTE__ 
* This function is supported in SDK version 2.10.3 and above.

---

__Example__

```c
qbool ret = Ql_iotDBindcodeSet("1234567891234567", 120);
```

---

<span id="Ql_iotDBindcodeGet">  </span>

## <span style="color:#A52A2A">__Ql_iotDBindcodeGet__</span>

This function gets the device bind code.

__Prototype__

```c
quint32_t Ql_iotDBindcodeGet(char **bindcode)
```

__Parameter__
* __Input__
  * __quint8_t **__ __`bindcode`__: Device bind code.

__Return Value__

The remaining time before the device binding times out.

__NOTE__ 
* This function is supported in SDK version 2.10.3 and above.

---

__Example__

```c
quint8_t *bindcode = NULL;
quint32_t timeout = Ql_iotDBindcodeGet(&bindcode);
```

---

<span id="Ql_iotRst">  </span>

## <span style="color:#A52A2A">__Ql_iotRst__</span>

 This function resets device information, including DeviceSecret and device bind code.

__Prototype__

```c
void Ql_iotRst(void)
```

__Parameter__

None

__Return Value__
* __`True`__: Successful execution
* __`False`__: Failed execution

__NOTE__ 
* This function is supported in SDK version 2.10.0 and above.

---

__Example__

```c
qbool ret = Ql_iotRst();
```

---
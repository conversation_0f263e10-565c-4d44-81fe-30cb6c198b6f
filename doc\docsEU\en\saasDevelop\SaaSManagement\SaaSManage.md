# SaaS Management

This section outlines the first step in SaaS development: SaaS management.

## **Prerequisites**

● You have the permissions to operate the SaaS application module.

## **Steps**

### **1.Manage SaaS Application**

1.Log in to Developer Center, and click "SaaS Development" → "SaaS Management" in the left-hand navigation bar to enter "SaaS Management" page.

<a data-fancybox title="img" href="/en/guide/image2022-0706-01.png?version=1&modificationDate=1646655516000&api=v2">![img](/en/guide/image2022-0706-01.png?version=1&modificationDate=1646655516000&api=v2)</a>

2.Click "Create a SaaS Application" and enter a SaaS application name in the pop-up window. If necessary, you can also add a brief description to clarify the purpose of the SaaS application. Then click "Confirm" to create a new SaaS application.

3.Go to the "SaaS Management" page to see the created SaaS application. Click the settings icon in the upper right corner of the application card to edit application name and description. You can also click the SaaS application to authorize services, products and Apps.

### **2.Authorize a Service**

1.Click the relevant SaaS application card to access the "Service" tab page for a specific application. From there, click "Authorize a Service" to authorize a service for the SaaS application. Once a service has been authorized, the SaaS application will be able to access only the APIs associated with that service.

2.Once the authorization is successfully completed, the newly authorized service will be displayed in the service list. From there, you can click "View Document" to access the associated documentation, or "Cancel Authorization" to cancel the authorization, if needed.

<a data-fancybox title="img" href="/en/guide/image2022-0706-08.png?version=1&modificationDate=1646655516000&api=v2">![img](/en/guide/image2022-0706-08.png?version=1&modificationDate=1646655516000&api=v2)</a>

### **3.Authorize a Product**

1.Click the relevant SaaS application card, then click "Product" to access the desired "Product" tab page. Click "Authorize a Product" to authorize a product for the SaaS application. Once authorized, only the authorized products can be operated through the SaaS application.

2.Once the authorization is successfully completed, the newly authorized product will be displayed in the product list. From there, you can click "Cancel Authorization" in the "Action" column to cancel the authorization.

<a data-fancybox title="img" href="/en/guide/image2022-0706-09.png?version=1&modificationDate=1646655516000&api=v2">![img](/en/guide/image2022-0706-09.png?version=1&modificationDate=1646655516000&api=v2)</a>

### **4.Authorize an App**

1.Click the relevant SaaS application card, then click an App to access the "App" tab page. Click "Authorize an App" to authorize an App for the SaaS application. Once the authorization is successfully completed, only the authorized App can be operated through the SaaS application.

2.The newly authorized App will be displayed in the App list frow where you can click "Cancel Authorization" in the "Action" column to cancel the authorization.

<a data-fancybox title="img" href="/en/guide/image2022-0706-10.png?version=1&modificationDate=1646655516000&api=v2">![img](/en/guide/image2022-0706-10.png?version=1&modificationDate=1646655516000&api=v2)</a>

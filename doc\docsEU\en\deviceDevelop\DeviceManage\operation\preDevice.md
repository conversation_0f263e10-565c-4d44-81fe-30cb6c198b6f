# Pre-import Management

 

This section introduces device actions in "Device Maintenance".

 

## **Prerequisites**

●  A product has been created.

 

## **Steps**

1.   Log in to Developer Center, click "**Device Management**" → "**Device Maintenance**" in the left-hand navigation bar to enter the "Pre-import Management" page in the region where the account is registered.

<a data-fancybox title="img" href="/en/guide/**************.png">![img](/en/guide/**************.png)</a>

2.   On the "Pre-import Management" page, you can add devices with different authentication types. The device information that is successfully added will apply to all data centers to support the usage of devices in multiple regions. When a device is authenticated in a data center, authentication will be performed as selected in pre-import. Upon successful verification, the device will be displayed in the "Device List" of the current data center, and the device name and SN configured during the pre-import will be followed.

 

3. Modifying device information in the "Pre-import Management" does not affect the data of devices that are already online in the "Device List" of each data center. To modify local device information, please make changes in the "Device List" of the corresponding data center.

 

## Add **a Device**

Dynamic authentication: When product-level dynamic authentication is disabled, only imported devices can use ProductKey and ProductSecret to connect to Developer Center for authentication and obtain DeviceSecret.

 

Static authentication: Only devices with DeviceSecret burned in advance can be connected to Developer Center. Developer Center will reject the authentication requested by the device whose DeviceKey and DeviceSecret do not match.

 

After successfully adding a device with static authentication, you can obtain the DeviceSecret on the "Device Details" or "Manage in Batches" page.

Note: The device with static authentication can only be added to products using MQTT and LwM2M network protocols.

 

Device with X.509 Certificate: Developer Center allows the device to use its own X.509 certificate for authentication. The CA certificate must be uploaded before the device is connected to the platform.

 

When adding a device with X.509 Certificate authentication to Developer Center, you can opt to input the corresponding device certificate fingerprint. If not, the fingerprint will be automatically generated based on the certificate used during the device's initial connection. Subsequent connections will be matched and verified using the generated fingerprint.

 

To calculate the device certificate fingerprint, use SHA-1 hashing, such as openssl x509 -fingerprint -sha1 -in certfile.crt. The fingerprint generated by OpenSSL includes colons ":" and must be converted to a 40-bit hexadecimal string without the colons before uploading.

 

See the following table for other device actions.

| **Action**        | **Description**                                                                                                                                                                                                                                                                 |
| ----------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| Generate SN       | Click "**Generate  SN**" on the right. A certain number of SNs can be generated as  required (A maximum of 10,000 SNs can be generated each time) for binding  devices on the App.                                                                                              |
| Manage in Batches | Used to obtain device information (including DS) in  batches. Users can obtain information about devices connected to Developer  Center through static authentication by checking "Batch No", or  obtain device information by using the download code through calling OpenAPI. |
| Export Devices    | You can export the pre-imported device data according to different products.                                                                                                                                                                                                    |

 

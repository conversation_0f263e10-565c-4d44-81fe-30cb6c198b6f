# Resource Usage

This section introduces how to view the resource usage of a device on the “Device Details” page.

## **Prerequisites**

● A product has been created.  
● A device has connected to Developer Center and consumed resources.

## **Steps**

Log in to Developer Center, click “**Device Management**” → “**Device Maintenance**” in the left-hand navigation bar to display the "Device List” page. Click “**View**” in the "Action" column to navigate to the "Device Details" page.

<a data-fancybox title="img" href="/en/guide/2-1.jpg">![img](/en/guide/2-1.jpg)</a>

Parameter

| Parameter                            | Description                                                                                                                                                  |
| :----------------------------------- | :----------------------------------------------------------------------------------------------------------------------------------------------------------- |
| Activation Code Package Allowance    | Detailed allowance of the device activation code, including the validity period, device messages, message storage duration, and device OTA upgrade attempts. |
| Consumed Activation Codes            | The cumulative number of activation codes consumed by the device。                                                                                           |
| Last Activation Date                 | The last date the device was activated.                                                                                                                      |
| Expiration Date                      | The expiration date of the device's current activation code.                                                                                                 |
| Message Usage Details for This Month | The daily message usage for the device this month.                                                                                                           |
| OTA Upgrade Details                  | The detailed OTA upgrade information for the device this month.                                                                                              |


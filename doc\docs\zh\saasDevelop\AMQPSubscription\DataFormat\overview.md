# 数据格式概述

开发者中心通过AMQP订阅可以订阅不同类型的数据，每种数据类型都具有固定的报文格式，开发者可以根据参考此文进行消息的监听和解析。

具体订阅的消息类型见下表

| 消息类型           | 描述                                  |
|:---------------|:------------------------------------|
| 设备上下线事件        | 包含设备上线、下线、重新连接等设备状态信息               |
| 设备和模组状态        | 包含设备状态和模组状态信息，如电量、电压、信号强度、版本、ICCID等 |
| 设备命令响应数据       | 指下发到设备的指令状态信息，包括发送成功、失败等            |
| 设备信息变更         | 包括设备的增加、修改、删除、重置、认证、激活、注销等信息        |
| 设备绑定信息变更       | 终端用户针对设备的绑定信息发送变动触发的数据              |
| 设备上行透传数据       | 选择物模型或透传数据格式的产品，通过透传通道上报的设备数据       |
| 设备下行透传数据       | 选择物模型或透传数据格式的产品，通过透传通道发送的下行控制指令数据   |
| 物模型-物模型属性信息    | 选择物模型数据格式的产品下设备上下行数据                |
| 物模型-物模型事件上报-信息 | 选择物模型数据格式的产品下设备上报的事件信息，且定义级别为INFO   |
| 物模型-物模型事件上报-告警 | 选择物模型数据格式的产品下设备上报的事件信息，且定义级别为WARN   |
| 物模型-物模型事件上报-故障 | 选择物模型数据格式的产品下设备上报的事件信息，且定义级别为ERROR  |
| 物模型-物模型服务调用日志  | 选择物模型数据格式的产品下设备发送的服务类型数据            |
| 设备定位-查询定位信息    | 发送的设备定位下行数据                         |
| 设备定位-原始数据      | 设备上报的基于NMEA协议的定位数据                  |
| 设备定位信息         | 设备上报的NMEA协议经过平台解析后的定位数据             |
| 产品信息变更         | 产品的信息变更消息，包括产品的创建、修改、删除信息           |
| 产品授权信息         | 产品的授权信息变更，包括产品的授权和取消授权              |
| 用户域(App)授权信息   | 用户域(App)的授权信息变更，包括用户域(App)的授权和取消授权  |
| 物模型发布信息变更      | 新发布的物模型信息                           |
| 终端用户信息变更       | 终端用户变更信息，包括终端用户的创建、修改和删除信息          |
| 规则引擎事件         | 规则引擎推送信息                            |

>备注： 设备定位数据支持GPS与LBS两种方式，默认仅支持GPS定位数据推送，若希望推送LBS定位数据，请联系商务。


产品信息变更和物模型发布信息变更需要通过OpenAPI[创建企业用户订阅](https://iot-api.quectelcn.com/swagger-ui.html?urls.primaryName=%E6%B6%88%E6%81%AF%E8%AE%A2%E9%98%85%E7%AE%A1%E7%90%86%20(Enterprise%20API)#/%E6%B6%88%E6%81%AF%E8%AE%A2%E9%98%85/createEnterpriseSubscribeUsingPOST)，终端用户信息变更需要通过OpenAPI[创建终端用户订阅](https://iot-api.quectelcn.com/swagger-ui.html?urls.primaryName=%E6%B6%88%E6%81%AF%E8%AE%A2%E9%98%85%E7%AE%A1%E7%90%86%20(Enterprise%20API)#/%E6%B6%88%E6%81%AF%E8%AE%A2%E9%98%85/createEndUserSubscribeUsingPOST)
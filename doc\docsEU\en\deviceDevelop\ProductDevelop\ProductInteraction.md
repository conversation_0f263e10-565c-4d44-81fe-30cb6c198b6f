# Product Interaction

When applying for the service of Wonderfree App or OEM App, you can configure the "**Device Details**" in the App on the "**Product Interaction**" page.

Developer Center provides a default universal panel that dynamically generates control interfaces based on the product's defined TSL model features, which can be used for device debugging and basic operation.

In addition to the universal panel, Developer Center offers category panels for certain product categories. You can use the existing category panels or contact us to develop custom enterprise panels. Custom panels provide exclusive control interfaces that deliver superior product experiences for your customers. These panels precisely match your product features, align with brand styles, and enhance competitiveness through differentiated personalization.

## Switch Panel

Click "**Switch**" to select other types control panels.

<a data-fancybox title="img" href="/en/deviceDevelop/creatproduct/interaction01.png">![img](/en/deviceDevelop/creatproduct/interaction01.png)</a>

After switching the panels, you can enable Wonderfree App and virtual device feature authorization, and scan the virtual QR code via App for binding and testing.

<a data-fancybox title="img" href="/en/deviceDevelop/creatproduct/interaction02.png">![img](/en/deviceDevelop/creatproduct/interaction02.png)</a>

Please note that panel features are typically associated with TSL model definitions. Any inconsistency between them may result in failure to control devices through the panel.

## Multilingual Panel 

For global products, Developer Center supports multilingual text configuration for selected panels. You can click "**Configure**" to access the configuration page.

<a data-fancybox title="img" href="/en/deviceDevelop/creatproduct/interaction03.png">![img](/en/deviceDevelop/creatproduct/interaction03.png)</a>
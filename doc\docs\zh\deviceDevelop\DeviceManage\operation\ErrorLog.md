# 异常日志

异常日志是指设备上报的物模型数据与功能定义不一致时所产生的数据内容。

## **前提条件**

● 创建产品
● 完成产品开发-功能定义步骤
● 发送过设备上行数据

## **操作步骤**

1.登录开发者中心后，点击左侧菜单“设备管理”→ “设备运维”，出现设备列表页，点击设备操作栏中“查看”，跳转至“设备信息” tab页，点击“异常日志”tab页。

<a data-fancybox title="img" href="/zh/guide/20230601131957.png">![img](/zh/guide/20230601131957.png)</a>

参数说明

| **参数** | **描述**                                                     |
| -------- | ------------------------------------------------------------ |
| Ticket   | 此条数据的唯一标识                                           |
| 创建时间 | 此数据入库的时间                                             |
| 数据类型 | **上行：** 是指设备上报数据时产生的指令或消息。              |
| 发送状态 | **待发送：** 是指从用户通过设备调试或者应用的API进行的命令下行时，若设置了缓存时间，且设备不在线，下行数据将处于“**待发送**”的状态，在缓存时间内设备再次上线或发送上行数据时，将进行缓存数据下发。此状态仅在下行数据时存在。   **发送成功：** 下行发送成功是指从用户通过设备调试或者应用的API进行的命令下行，下行数据发送到了设备，并在5秒内收到响应成功的信息。上行发送成功是指从设备到开发者中心上行成功。   **发送失败：** 上行发送失败是指从设备到开发者中心网关服务连接成功，但是没有进行后续的处理，服务器、网络等环境出现了异常。下行发送失败是指从用户通过设备调试或者应用的API进行的命令下行，下行数据到设备，并在5秒内未收到响应成功的信息；大多数情况下的**发送失败**，是模组与开发者中心的连接已经断开，设备未主动注销，造成了伪连接。 |
| 数据内容 | 展示上行数据解析失败的具体ID，异常数据将不会发送到Websocket或AMQP客户端。 |
| 发送时间 | 上行时代表平台收到上行数据的时间。                           |

 


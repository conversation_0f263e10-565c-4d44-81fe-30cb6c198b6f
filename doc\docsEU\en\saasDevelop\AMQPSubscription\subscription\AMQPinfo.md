# AMQP Message Subscription Overview

Message Subscription is a data forwarding solution implemented by Developer Center using AMQP (Advanced Message Queuing Protocol). Once you've created an AccessKey, configured the message subscription rule, and started the subscription, Developer Center will forward the subscribed message type to the specified message queue as  configured. You can then obtain the subscribed message by connecting the client to the message queue.

The following figure illustrates how messages are forwarded in AMQP message subscription.

<a data-fancybox title="img" href="/en/saasDevelop/image2022-3-17_17-17-12.png">![img](/en/saasDevelop/image2022-3-17_17-17-12.png)</a>

Benefits of AMQP message subscription:

● Supports multiple consumer message queues. With AMQP, you can have multiple queues subscribed to messages from the same product. For example, Message Queue A can subscribe to the messages of Product A in the development environment, while Message Queue B subscribes to the messages of Product B in the production environment. If multiple queues subscribe to the Product B at the same time, they can all receive the same messages from Product B simultaneously.<br />
● Facilitates troubleshooting. Message subscription allows you to view the client status, accumulated messages, and message consumption rate.<br />
● Supports linear scalability. You can significantly improve the message forwarding capability by adding consumer clients.<br />
● Supports message accumulation queue. When Developer Center pushes real-time messages, they are sent immediately. However, in case of a push failure, real-time messages enter an accumulation queue, which prevents message loss. Even if messages are accumulated due to client failures or low consumption rates, real-time messages are sent along with the accumulated messages once the clients recover. 

Before using AMQP Message Subscription, you have to create a message queue and configure a subscription rule on Developer Center, see [Message Subscription](/saasDevelop/SaaSManagement/NewsSubscription). Then you must develop an AMQP client and connect it to Developer Center to subscribe to messages. For more information, see [AMQP Client Access Guide](/saasDevelop/AMQPSubscription/subscription/AMQPtoC)。


## **Limits on AMQP Message Subscription**

| Item                                    | Description                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                |
| :-------------------------------------- | :----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| Authentication timeout                  | An authentication request is sent after a connection is established. If the authentication fails within 15 seconds, the server will terminate the connection.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              |
| Data timeout                            | When a server establishes a connection with Developer Center, the heartbeat timeout period (as specified by the idle-timeout parameter in AMQP) must be observed. Range: 30–60 seconds. If no frame is transmitted within the heartbeat timeout period, Developer Center will end the connection. After the connection is established, the server must send PING packets within the heartbeat timeout period to maintain the connection. Otherwise, Developer Center will terminate the connection. <br />Note: If the connection is established by using the SDK provided by QuecCloud, the server does not need to send PING packets to maintain the connection. However, during the keep-alive time provided by the SDK, it is important to ensure that the main process does not exit. |
| Policy for message pushing retries      | Messages may not be consumed in real time due to issues such as offline consumers, or slow consumption rates. In such cases, messages are accumulated and pushed to consumers once they re-connect to Developer Center and start consuming at a stable speed. However, if the consumers fail to consume these pushed messages, the queue where the accumulated messages are stored may be blocked. To address this, Developer Center retries pushing the accumulated messages to consumers after approximately 1 minute,.                                                                                                                                                                                                                                                                  |
| Maximum number of saved messages        | A message queue can retain a maximum of 500 Mb.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |
| Message retention period                | 6 hours.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   |
| Maximum number of message queues        | Each  account can create a maximum of 50 message queues. A message queue is suspended automatically if the queue is not used for more than 30 days.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
| Maximum number of message subscriptions | Each account can create a maximum of 100 subscriptions.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    |

**<i style="color: #d20030">Disclaimer</i>**<br />
Acceleronix Developer Center may release or update its version as needed, which could result in disconnection of your AMQP client. We will provide advance notification of any releases or updates through all reasonable means. To ensure continuous use, **please make sure to use an AMQP client with a reconnection mechanism after abnormal disconnection to access Acceleronix Developer Center**. Please note that Acceleronix Developer Center cannot guarantee or promise the continuity of IoT platform and shall not be liable for any direct, indirect or other forms of loss or damage, whether contractual, tortious or otherwise, arising from your failure to use the AMQP client with a reconnection mechanism after abnormal disconnection.

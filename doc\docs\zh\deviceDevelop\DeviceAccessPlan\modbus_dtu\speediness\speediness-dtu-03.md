# 烧录配置文件

## __一、烧写固件__

<span style="color:#999AAA">提示：因为每个DTU-Modbus模组烧写程序不一样，具体烧写方式请联系移远技术支持提供。</span>

## __二、烧写配置文件至DTU设备__

<a data-fancybox title="img" href="/zh/deviceDevelop/develop/DTU_Modbus/Example-27.png">![img](/zh/deviceDevelop/develop/DTU_Modbus/Example-27.png)</a>

1、在工作栏中点击 __下载__ >__串口下载__ 按钮。<br>
2、 点击 __导入文件__ 按钮，在弹窗页面中选择生成的 xxxxxx_xxxxxxxxxx_local.bin 文件。<br>
3、 选择对应的PC连接DTU设备端口号与波特率等配置后点击 __打开串口__ 按钮。<span style="color:#999AAA">提示：可以根据开发板上的PIN脚号或对应版本的原理图找到对应的烧录串口位置。</span><br>
4、最后点击 __开始下载__ 后重启硬件设备，文本框中会显示文件下载进度，下载成功会弹出提示 __下载成功__ 弹窗。<br>
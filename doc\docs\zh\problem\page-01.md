# 产品使用问题
## **常见问题**


**1. 开发者中心只针对企业客户开放使用吗？**



答：无论是B端厂商还是C端个人客户，都可以注册开发者中心账号，然后登录使用。



**2.开发者中心可以批量添加/删除设备吗？**



答：支持批量添加设备，可通过EXCEL表格导入或者通过配置产品信息和服务器信息批量添加，不支持批量删除设备。



**3.在线的设备不能删除吗？**



答：可以删除的。



**4.一个产品下面可以创建多少设备？**



答：创建的设备数目前是不受限制的，但是如果想激活设备正常使用需要购买相应IoT平台的连接License激活码。



**5.开发者中心的产品或设备不小心删除了，还能恢复吗？**



答：平台产品或设备删除后，数据不能恢复。



**6.开发者中心提供手机APP、微信小程序等相关的服务吗？**



答：目前开发者中心仅提供设备维度的服务，您可以选择自己开发APP、微信小程序等服务，或者联系我司销售人员为贵司提供定制化开发方案。



**7.平台对于客户的数据是怎么处理的，可以通过API接口将数据转到其他平台或者客户服务器吗？**



答：可以做透传数据处理，通过API接口推送到客户服务器，也可以使用平台自定义的物模型快速接入处理。





**8.** **我在使用开发者中心的功能时，如果遇到使用的AT****命令在串口工具中返回ERROR****错误时，如何判断这个模块是否支持这些功能?**

答：1）您可以使用 **AT+QIOTREG=?** 测试命令来测试模块自带的固件版本是否支持该功能。如果支持，会返回 **+QIOTREG: (0,1) OK;** 如果不支持，则会返回 **ERROR**。

2）对于定位功能，您可以使用 **AT+QLBS=?** 测试命令来测试模块自带的固件版本是否支持该功能。如果支持，会返回 **OK**; 如果不支持，则会返回 **ERROR**。

另外，您可以查看每个模块固件版本的Release Note来判断该固件版本是否支持开发者中心功能。在使用开发者中心功能时返回**Error**错误，一般是由于模块自带的固件版本不支持该功能导致。请您使用 AT命令查询当前固件版本的信息，并联系当地的 FAE 确认以便给您提供支持的固件版本。





## **设备接入**





**1. MQTT协议设备接入的南向服务器地址和端口？**



答：国内设备连接地址如下：
- mqtt://iot-south.quectelcn.com:1883
- mqtts://iot-south.quectelcn.com:8883

海外设备连接地址如下：
- mqtt://iot-south.acceleronix.io:1883
- mqtts://iot-south.acceleronix.io:8883


**2.QuecThing SDK如何获取？**



答：如果需要移植接入开发者中心的SDK（集成QuecThing功能），请联系移远AE提供。



**3.设备接入开发者中心支持哪几种方式接入？**



答：支持四种方式接入：标准版本的AT命令，OpenCPU版本的OpenAPI，Modbus DTU零代码接入方案。



**4.为什么开发者中心中的设备一直处于“未激活”状态，如何激活设备？**



答：需要设备与平台交互的上下行数据达到一定的激活阈值且拥有剩余可用License数，设备就会自动激活。


**5.开发者中心DeviceKey的数据长度是固定的吗？**



答：对于不同的产品DeviceKey的数据长度也不同。常规产品的DeviceKey的数据长度是4~32位的英文及字母数字的组合，而对于电信AEP对接产品的DeviceKey的数据长度是固定的15位纯数字。


## **消息推送**





**1. 开发者中心的订阅是否具有重推机制？**



答：有的。



**2.开发者中心当前有实时获取结果的接口么，数据获取只有异步方式么？**



答：开发者中心的数据目前只支持异步的方式来获取，暂不支持同步。



**3.设备断电后，SaaS应用往设备下发数据，未发送成功，设备重新上电后，数据怎么处理？**



答：可使用缓存接口处理。待设备重新上电后，数据重新下发。



## **IoT平台与电信AEP平台对接**

**1. 使用自己购买的电信NB-IoT SIM卡，可以使用开发者中心么？**

答：可以接入开发者中心使用。


**2.什么样类型的模组需要使用IoT平台与电信AEP平台对接？**

答：首先判断是否为移远通信模组且是否为NB类型模组。不同的模组类型使用不同的网络制式，不同的网络制式适用不同的通信协议。一般而言，非NB模组可直接连接开发者中心，不需要再通过电信CTWing平台转发数据。


**3.设备通过IoT平台与电信AEP平台对接功能接入开发者中心使用的通信协议：MQTT还是LwM2M？**

答：通常情况下，受限于NB-IoT网络制式的网络性质，使用LwM2M协议作为通信协议。



## **OTA升级**





**1. OTA的分类？**



答：模组固件升级(FOTA，Firmware Over the Air )，MCU应用升级(SOTA，Software Over the Air )。



**2.如何使用OTA？**



答：使用烧录Quecthng固件的模组，并成功连接开发者中心。



**3.整包升级和差分包升级的区别？**



答：OTA两种类型最大的区别莫过于他们的”出发点“（我们对两种不同升级包的创建，并使用它进行OTA升级）。我们创建整包时不需要old包，可以直接使用base包进行升级，因此整包一般用来升级整个固件【∞ -> B】，而差分包一般用于两个特定的点【A->B】。



**4.如何获取模组升级包？**



答：联系移远技术支持。



**5.什么是升级计划？**



答：在升级设备前，需创建或选择一个已有的升级计划；计划包含了需要升级的设备、升级时间、升级的组件配置等信息。



**6.什么是升级模型？**



答：模型是用户自主创建，由一个或多个组件构成的集合体。用于创建升级计划时对需要升级的产品组件进行选择和规划。模型在当前项目下唯一；组件命名在同一模型下具有唯一性。



**7.为什么有些升级计划可以删除，有些升级计划无法删除？**



答：创建升级计划时，可以选择“激活计划”和“保存”，已激活的升级计划无法删除，保存未激活的升级计划可以删除。





**8.升级计划开始后为什么无法结束？**

答：对于一个设备，不管是单组件或多组件，只要确认升级了，设备不升级完成或升级失败，升级计划无法结束（不管是计划时间到期，还是点强制终止）。

**9.MQTT或LwM2M协议连接开发者中心，使用OTA升级的区别？**



答：确认升级后，MQTT必须得计划升级失败（升级次数用完）或升级成功升级计划才会结束，LwM2M超过48小时升级计划会自动结束。



## **LTE模组通用**





**1. 检查模块网络注册状态？**



答：AT+CREG?;+CGREG?;+CEREG?;+CSQ;+QNWINFO

![img](/zh/problem/image999.png)



**2.如何配置APN？**



答：根据使用的运营商SIM卡更改相应APN，使用AT+CGDCONT=`<cid>,<PDP_type>,<APN>`



| 运营商 | APN                             |
| :----- | :------------------------------ |
| 移动   | cmiot、china mobile、cment      |
| 电信   | ctnet、chn-unicom、ctnb         |
| 联通   | unim2m.njm2mapn                 |
| ARM    | stream.co.uk                    |
| Orange | orange.item.spc、orange.m2m.spc |


## **NB模组通用**





**1.如何检查模组信号强度？**



答：检查地区运营商信号强，使用AT+NUESTATS



**2.如何检查模组注网状态？**



答：AT+CEREG? 查询 EPS网络注册状态，返回 +CEREG:`<n>,<stat>`



| stat | EPS 注册状态                                   |
| :--- | :--------------------------------------------- |
| 0    | 未注册，UE 目前未搜索运营商进行注册            |
| 1    | 已注册，本地网络                               |
| 2    | 未注册，但 UE 目前正在连接或搜索运营商进行注册 |
| 3    | 注册被拒绝                                     |
| 4    | 未知（例如不在 E-UTRAN 范围内）                |
| 5    | 已注册，漫游网络                               |





## **设备定位LBS**





**1.定位时间取的是什么时间？**



答：上报定位方式为LBS时，其定位时间为调用基站定位解析后的时间；上报定位方式为GGA或RMC时，其定位时间为amqp中定位消息推送中的时间，但消息推送中的时间是UTC时间，不是东八区的时间，因此需要在推送消息时间的基础上再加8小时。



**2.上报定位信息后设备定位页面显示定位时间和定位方式，但经纬度没有显示，定位地图中就不显示定位地点吗？**



答：是的，定位地图中的定位地点是根据经纬度信息进行描点的，若经纬度信息为空，则定位地图中将不显示定位地点。

**3.开发者中心的设备定位展示使用的是什么地图？**



答：中文版使用的是高德地图，英文版使用的是谷歌地图。





## **物模型与透传**





**1.什么是物模型？与透传的区别是什么？**



答：物模型是指设备在开发者中心的数字化表示，并在IoT平台构建的实体数据模型。分为属性、服务和事件三个维度。定义了这三个维度，发布以后，即完成了物模型产品的定义。物模型产品需要在平台上选择“物模型”数据格式。 透传数据是指开发者中心将设备数据推送到应用SaaS平台，推送数据不做解析或处理。



**2.模组收到的物模型数据为什么展示为hex string？如何转换为Json格式？**



答：物模型的TTLV定义如下表所示，按照表中规范转换即可。



| 字段名            | 长度  | 内容                                                                    |
| :---------------- | :---- | :---------------------------------------------------------------------- |
| 数据标识（id）    | 13bit | 范围1-8191，在不同的命令（cmd）内唯一（如物模型，设备状态，模组信息等） |
| 数据类型（type）  | 3bit  | 范围0-7，详见下表                                                       |
| 数据长度（length) |       | 详见下表                                                                |
| 数据值（value）   |       | 详见下表                                                                |





| 数据类型   | 字节编码（二进制3bit） | 数据长度                                                                             | 数据值   |
| :--------- | :--------------------- | :----------------------------------------------------------------------------------- | :------- |
| 布尔-false | 000                    | 无                                                                                   | 无       |
| 布尔-true  | 001                    | 无                                                                                   | 无       |
| 枚举和数值 | 010                    | 标识位1bit(0为正 1为负) <br>衰减10的N次方4bit <br>数据字节数3bit（数据域的字节数-1） |          |
| 二进制数据 | 011                    | 数据字节数2B                                                                         | 数据内容 |
| 结构体     | 100                    | 元素个数2B                                                                           |          |
| 保留字段   | 101-111                |                                                                                      |          |



**3.模组如何将Json格式的物模型数据发送至开发者中心？**



答：转换方式同Q2，会提供完成的MCU Demo、物模型相关协议文档与代码。

**4.物模型服务中定义了多个输入/输出参数，在页面调用物模型服务时必须填写所有输入参数吗？设备上报物模型服务数据必须上报定义的全部输出参数吗？**



答：在发送物模型服务数据时，发送的输入/输出参数可以不是定义的全部参数，可以按照实际需求发送部分定义参数。

**5.在设备上报物模型属性时，是否可以一次上报多条物模型数据？同时上报多条物模型属性数据功能ID相同，是否能上报成功？**



答：设备上报物模型属性时可以一次上报多条物模型数据，但是平台有去重功能，如果一次上报的物模型数据中，所有数据的功能ID相同，例如：[{“id”:2,”type”:”int”,”value”:9},{“id”:2,”type”:”int”,”value”:11}]，平台会自动去重，数据日志中以JSON格式展示，只会展示最后一条：[{“id”:2,”type”:”int”,”value”:11}]，但是切换为其他格式展示会展示全部的数据；运行状态页面只会展示最后一条数据。

**6.在上报物模型属性时，假如定义的属性功能ID为1，数据类型为int类型，模组上报时上报数据为float类型，是否可以上报成功？**



答：可以上报成功，开发者中心对于设备上报的物模型数据采取较为包容的处理方式，上报的物模型数据与定义内容不同也可以上报成功。





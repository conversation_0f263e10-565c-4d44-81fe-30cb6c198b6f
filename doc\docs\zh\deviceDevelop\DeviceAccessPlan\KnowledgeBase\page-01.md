# AT指令说明

## __AT指令接入方案说明__ 

使用AT命令开发，主控MCU通过串口与模组之间使用AT指令即可完成相对应操作。模组对外封装统一的 IOT 平台 AT 指令。

## __AT指令通信原理__

AT指令物理层基于串口，由 __ASCII码文本__ 构成。AT指令通用方案是 __外挂MCU__ 对接主流方案之一，是应用于 __模组__ 与 __外挂MCU__ 应用之间的连接与通信的指令。AT指令接入方案有着更低的开发成本并转换成了简单串口编程，原理如下图所示。

<a data-fancybox title="img" href="/zh/deviceDevelop/develop/speediness/resource/AT/Speediness-AT-27.png">![img](/zh/deviceDevelop/develop/speediness/resource/AT/Speediness-AT-27.png)</a>


## __AT指令的格式符号__


|  符号  | 符号名称 |                                符号功能                                 |
| :----: | :------: | :---------------------------------------------------------------------: |
| \<CR\> |  回车符  |            终止命令行，与换行符一并发送；十六进制为：0x0D；             |
| \<LF\> |  换行符  |            终止命令行，与回车符一并发送；十六进制为：0x0A；             |
| \<…\>  |  尖括号  | 参数名称；<span style="color:#999AAA">实际命令中不包括尖括号 < ></span> |
| \[…\]  |  中括号  | 可选参数；<span style="color:#999AAA">实际命令中不包括方括号 [ ]</span> |


## __AT指令的语法__

AT命令的前缀 __AT__ 或 __at__ 必须加在每个命令行的开头。AT指令分 __测试指令__、__查询命令__、__设置参数指令__、__执行命令指令__ 四类。参见下表。

|            命令             | 命令名称 |         命令功能         |
| :-------------------------: | :------: | :----------------------: |
|       AT+ < CMD > =?        | 测试命令 | 询问模块支持哪些设置参数 |
|        AT+ < CMD >?         | 查询命令 | 取模块的当前被设置的参数 |
| AT+ < CMD >=p1[,p2[,p3[…]]] | 设置命令 |       设置模块参数       |
|         AT+ < CMD >         | 执行命令 |    让模块执行某个功能    |

## __AT指令的响应__

当模组处理完一条命令后，将会响应 __OK__、__ERROR__ 或 __+CME ERROR: \<err\>__，表示已经准备好接收新命令。通常，命令后面跟随形式为\<CR\>\<LF\>\<response\>\<CR\>\<LF\> 的响应。在使用 AT 命令过程出现错误时，若返回 __+CME ERROR: \<err\>__，可以根据 __\<err\> 错误码__ 快速判断出错原因。

AT 命令响应的格式主要分为以下两种

*	 \<CR\>\<LF\>+CMD:\<Parameter\>\<CR\>\<LF\>\<CR\>\<LF\>OK\<CR\>\<LF\>

*	\<CR\>\<LF\>\<Parameter\>\<CR\>\<LF\>\<CR\>\<LF\>OK\<CR\>\<LF\>
 

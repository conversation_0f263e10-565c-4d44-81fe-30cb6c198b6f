# SDK introduction 

## __1. Quec<PERSON><PERSON> SDK__

**QuecOpen** is an application mode with a module as a **main processor** and or a **slave module**. **QuecOpen** solution can **simplify the development process of Internet of Things applications**, **streamline hardware structure design**, and thus it **reduces product costs**. **SDK** of **QuecOpen** solution adopts modular design to separate core protocol service and hardware abstraction layer and provides flexible configuration options and multiple compilation modes, which is suitable for different development platforms and environments of different devices.


<a data-fancybox title="img" href="/en/deviceDevelop/develop/speediness/resource/QuecOpen/Speediness-QuecOpen-01.png">![img](/en/deviceDevelop/develop/speediness/resource/QuecOpen/Speediness-QuecOpen-01.png)</a>


## __2. QuecThing SDK__
QuecThing SDK provides a unified API definition for the module adaptation layer, and carries out secondary abstraction on chip basic SDK, shielding complex module technical details, which is convenient for you to get started quickly. Developers only need to implement specific business features according to module capabilities. After compilation and download, you can run QuecThing SDK on the module and connect the device to Developer Center to realize the intellectualization of the device. At the same time, in order to speed up the development efficiency, QuecThing SDK provides built-in and unified communication protocol and APIs for the module application layer to call, so developers can directly use and develop specific features without paying too much attention to the module model.



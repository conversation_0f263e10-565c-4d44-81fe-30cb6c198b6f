# Create an Upgrade Plan

You need to create an upgrade plan on the platform for OTA upgrades in batches.

This section introduces how to create an upgrade plan.

## Prerequisites

1、  The firmware version package to be upgraded has been added. See [**Add a Firmware Upgrade Package**](/deviceDevelop/DeviceManage/ota/part02/page-01) for details.

2、  The firmware has been verified (optional). See [**Verify Firmware (Optional)**](/deviceDevelop/DeviceManage/ota/part02/page-02) for details.

## Steps

1、  Log in to Developer Center, click "**Device Management**" → "**OTA Upgrade**" in the left-hand navigation bar. 

<a data-fancybox title="img" href="/en/guide/ota/ota09.png">![img](/en/guide/ota/ota09.png)</a>

2、  Click "**Create an Upgrade Plan**" button on the "**Upgrade Plan**" tab to configure basic parameters.

<a data-fancybox title="img" href="/en/guide/ota/ota10.png">![img](/en/guide/ota/ota10.png)</a>

<a data-fancybox title="img" href="/en/guide/ota/ota101.png">![img](/en/guide/ota/ota101.png)</a>

**Parameters:**



| **Parameter**                                         | **Description**                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  |
| ----------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| Plan Name                                             | The upgrade plan name must be unique under a Developer  Center account and cannot be edited after activation. The length cannot  exceed 100 characters. <br /> **Note:** For an enterprise  user, the upgrade name must be unique under the enterprise account and its  sub-accounts.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |
| Product to Be Upgraded                                | Select the product to  which the devices in the upgrade plan belongs. <br /> **Note:** The product to be  upgraded cannot be changed once the plan is saved successfully.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
| Set Blacklist and  Whitelist                          | ● Blacklist: The  devices in the blacklist are not allowed to be upgraded. <br />●    Whitelist: Only the  devices in the whitelist are allowed to be upgraded.    If the blacklist  and whitelist are not set, all the devices under the product can be upgraded.  <br />●   If the blacklist  and whitelist are set at the same time, the blacklist has a higher priority  than the whitelist. <br /> **Note:** After the  plan is saved successfully, you need to edit the blacklist and whitelist in  the plan details. The activated plans cannot be edited.                                                                                                                                                                                                                                                                                                                                                                                                                                |
| Time Zone                                             | A required  parameter.  <br />1）	The planned time is under this time zone. <br />2）	Developer Center will only push the upgrade plan during the push period in this time zone.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |
| Planned Time                                          | Set the upgrade plan period. The plan will  automatically end if it exceeds the end time. The devices to be upgraded but  not upgraded in the plan cannot continue to be upgraded.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               |
| Push Period                                           | An optional parameter. Configure the period of  Developer Center automatically pushing the upgrade plan.  You can only configure hour to hour with the interval  of at least one hour.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |
| Upgrade Mode                                          | ● Manual upgrade: The  device cannot directly obtain the OTA upgrade plan. Developer Center pushes  the upgrade plan to the device for OTA upgrade after the application queries  and confirms the upgrade by the OpenAPI;  <br />●   Silent upgrade:  Developer Center directly pushes the upgrade plan to the device for OTA  upgrade.  <br /> **Note:** Bluetooth  devices only support silent upgrade currently.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
| Push Period for Auto-Confirmation Upgrade/Push Period | An optional parameter, If you do not configure this parameter, the push period will default to 24 hours.<br />● Push Period for Auto-Confirmation Upgrade: If you enable auto-confirmation upgrade, Developer Center will only actively push upgrade plans for devices with auto-confirmation upgrade within the push period of the corresponding time zone .<br />● Push Period: Developer Center will push silent upgrade plans whose options for "Push Plan Actively" are "Yes" within this push period of the corresponding time zone to the device.<br />For example, if you create a silent upgrade plan at 10:00 (Beijing Time) on January 1st and set the push period to 00:00–02:00, the platform will actively push the plan to the device for upgrade at 00:00 on January 2nd. If the device is already being upgraded and the upgrade has been confirmed, the push will be terminated. Note: During the push period, the upgrade plan will be pushed to the device every 10 minutes. |
| Push Plan Actively                                    | ●	Yes (Default): After this plan is activated, Developer Center will actively push the upgrade plan to the device. <br />●	No. Only when the device sends an upgrade request to Developer Center can it obtain the upgrade plan.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |
| Effective Period for Push Rejection                   | This parameter is required only when the upgrade mode is the silent upgrade and the selected option for "Push Plan Actively" is "Yes". <br />●  For the scenario  where the device refuses to upgrade, if the device is not suitable for  upgrade due to the current situation and reports a rejection, the platform  will push the upgrade plan again after the rejection time until the device  confirms the upgrade or the plan expires/ends.  <br />●  Valid time range:  10–1440 minutes (24 hours). Default: 120 minutes.  <br />●  For example, when  this parameter is set to 50 minutes, if a device receives an upgrade plan at  10:00 and rejects the upgrade at 10:10, the next time when the platform  actively pushes the plan to the device will be 11:00. The specific time is  subject to the actual time of platform issuing plan.                                                                                                                                             |
| Upgrade Strategy                                      | ●  Retry Times: The  number of times allowed to re-upgrade the device if an upgrade fails.   <br /> ● Minimum RSSI: If  the signal strength reported by the device is lower than this value, an OTA  upgrade will not be performed. <br /> ●  Minimum Battery  Capacity: If the reported battery power is lower than this value, OTA upgrade  will not be performed. <br /> **Note:** If you need the minimum signal strength and minimum  battery level to take effect, the verification logic of this upgrade strategy  is required.                                                                                                                                                                                                                                                                                                                                                                                                                                                           |

 

3、  For upgrade components and version information configuration, the first option is to select upgrade component(s) from an existing upgrade plan and reuse it.

 <a data-fancybox title="img" href="/en/guide/ota/ota11.png">![img](/en/guide/ota/ota11.png)</a>

4、  The second option is to add a new upgrade component. Click "**Confirm**" after configuring parameters to complete the operation.

 <a data-fancybox title="img" href="/en/guide/ota/ota12.png">![img](/en/guide/ota/ota12.png)</a>

 <a data-fancybox title="img" href="/en/guide/ota/ota13.png">![img](/en/guide/ota/ota13.png)</a>

**Parameters:**

| **Parameter**        | **Description**                                                                                                                                                                                                                                                                                                                                                                                           |
| -------------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| Component Type       | Select the component type for the upgrade. <br /> **Note:**  Sub-devices only support the module firmware upgrade currently.                                                                                                                                                                                                                                                                              |
| Component Identifier | Enter the component identifier of the module firmware  or MCU firmware to be verified. <br /> **Note:** Please ensure  the correctness of the component identifier. See [**AT command solution**](/deviceDevelop/DeviceAccessPlan/cellular/AT/API/cellular-at-03)for details. See [**QuecOpen solution**](/deviceDevelop/DeviceAccessPlan/cellular/QuecOpen/api/cellular-quecopen-api-02) for details.                                      |
| Upgrade Mode         | Select the upgrade mode for component upgrade.                                                                                                                                                                                                                                                                                                                                                            |
| Target Version       | Select the target version of this upgrade component. <br /> **Note:** If you select the full-package upgrade, the firmware  version package will be matched based on the selected target version.                                                                                                                                                                                                         |
| Specified Version    | This parameter is optional for a full-package upgrade.   You can enter English, number, underline "_", hyphen "-", English full stop "." and English semicolon ";".  Up to 8 version  numbers are supported and please separate multiple version numbers with  English semicolons ";".  The firmware  can only be upgraded after the device version number is matched with  the specified version number. |
| Source Version       | The original version before the component is upgraded.  This parameter is only required for delta package upgrades. <br /> **Note:** If you select the delta package upgrade, the firmware package will be matched  based on your selected target version and the source version.                                                                                                                         |

 

5、  View the added components in the upgrade component list. Components are upgraded in the order in which they are added.

 <a data-fancybox title="img" href="/en/guide/ota/ota14.png">![img](/en/guide/ota/ota14.png)</a>

6、  Select "Manual Upgrade" for the "Upgrade Mode".

a)  For upgrade plans in the Chinese Mainland, you must configure the Chinese upgrade texts, and for upgrade plans beyond the Chinese Mainland, you must configure the English upgrade texts.

b)  Other language texts are optional based on your actual requirements.

7、  Upgrade texts can be omitted if you select the silent upgrade.

 <a data-fancybox title="img" href="/en/guide/ota/ota15.png">![img](/en/guide/ota/ota15.png)</a>

8、  Click "**Add**" on the "**Configure Upgrade Text**" page, select the corresponding language, enter the upgrade text and then click "**Confirm**".

**Note:** You can [**submit a ticket**](/personalCenter/WorkOrder) to apply for a new language if our supported languages cannot meet your business requirements.

 <a data-fancybox title="img" href="/en/guide/ota/ota16.png">![img](/en/guide/ota/ota16.png)</a>

9、  Click "**Save**" after the plan is configured. Now the plan is not activated but in the saved status and you can edit the plan to change the content.

**Note:** Products to be upgraded do not support this feature.

10、Click "**Activate Plan**" and then the plan is in the activated status. Now the plan cannot be edited. The devices in the plan will enter the OTA upgrade process.

**Note:** Once the upgrade plan is activated, all information in the plan cannot be edited.

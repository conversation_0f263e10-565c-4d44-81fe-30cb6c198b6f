# 数据业务交互 相关指令
## **相关AT指令列表**

该AT指令主要实现终端设备在连接上移远 IoT 平台后进行数据交互的功能，其中有收发透传数据、收发物模型数据及请求信息上报等。  

|              指令名称              |         功能描述          |
| :--------------------------------: | :-----------------------: |
|    [AT+QIOTSEND](#AT+QIOTSEND)     |    发送透传数据至平台     |
|      [ AT+QIOTRD](#AT+QIOTRD)      |  读取平台下发的透传数据   |
| [AT+QIOTMODELTD](#AT+QIOTMODELTD)  |   发送物模型数据至平台    |
| [ AT+QIOTMODELRD](#AT+QIOTMODELRD) | 读取平台下发的物模型数据  |
|    [AT+QIOTINFO](#AT+QIOTINFO)     | 请求QuecThing上报设备信息 |

## **相关AT指令详情**

<span id="AT+QIOTSEND">  </span>
## <span style="color:#A52A2A">__AT+QIOTSEND (发送透传数据)__</span>

* __说明__：该命令用于发送透传数据至平台。
* __最大响应时间__ ：300 毫秒。
* __特性说明__：该命令立即生效。

### 测试命令

* __发送：__

    ```c
    AT+QIOTSEND=?
    ```
* __响应：__

    响应支持的发送模式与数据长度范围

    ```c
    +QIOTSEND: (支持的<mode>范围),(支持的<length>范围),<data>
    
    OK
    ```


### **设置命令**

* __发送：__

    ```c
    AT+QIOTSEND=<mode>,<length>[,<data>]
    ```
* __响应：__

    若指定可选参数且连接已经建立
    * 若已配置上行消息需应答PkgID
        ```c
        +QIOTSEND: <txid>
        
        OK
        ```
    * 否则
        ```c
        OK
        ```
        若省略可选参数且连接已经建立

    ```c
    >
    ```
    响应>后，输入长度等于\<length>的数据

    * 若已配置上行消息需应答PkgID    
        ```c
        +QIOTSEND: <txid>
        
        OK
        ```
    * 否则
        ```c
        OK
        ```
    
    若出现任何错误
     ```c
    ERROR
     ```

---

* __参数：__
    * __`<mode>`__ 整型。发送模式。<br>
        * __`0`__ ：QoS=0 最多发送一次
        * __`1`__ ：QoS=1 至少发送一次 
        * __`2`__ ：QoS=2 准确一次送达

    * __`<length>`__：整型。待发送数据长度。单位：字节。
        * 若设置命令中指定\<data\>，长度范围以测试命令实际返回值为准
        * 若设置命令中不指定\<data\>，长度范围以模块性能为准
    * __`<data>`：__ 发送数据。
        * 若设置命令中指定\<data\>，待发送数据仅可为字符串类型
        * 若设置命令中不指定\<data\>，待发送数据为任意字节流类型，以模块性能为准
    * __`<txid>`：__ 整型。
        * Qos=0时txid为0
        * Qos>0时，返回发送消息txid(1~65535)

* __备注：__
    * __2.3.3及以上的QuecTing版本支持。  \<mode\>参数为1及以上则会有事件回调__

---

__示例1 (发送透传数据)__  
<span style="color:#999AAA">提示：业务数据交互期间请保证设备正常在线。</span>

发送“__ABCabc123456__”透传数据上行到平台，响应 __>__ 后发送 “ABCabc123456”。
```c
[TX]AT+QIOTSEND=1,12

[RX]> 
[TX]ABCabc123456
[RX]OK

[RX]+QIOTEVT: 4,10200
```

***

<span id="AT+QIOTRD">  </span>
## <span style="color:#A52A2A">__AT+QIOTRD (读取透传数据)__</span>

* __说明__：该命令用于读取平台下发的透传数据。
* __最大响应时间__ ：300 毫秒。
* __特性说明__： 该命令立即生效。

### 测试命令

* __发送：__

    ```c
    AT+QIOTRD=?
    ```
*  __响应：__

    响应读取的数据长度

    ```c
    +QIOTRD: (支持的<req_length>范围)
    
    OK
    ```
### **查询命令**

* __发送：__

    ```c
    AT+QIOTRD?
    ```
* __响应：__

    响应剩余数据包个数

    ```c
    +QIOTRD: <remain_pieces>
    
    OK
    ```

### **设置命令**

* __发送：__

    ```c
    AT+QIOTRD=<req_length>
    ```
* __响应：__

    读取透传数据成功
    ```c
    +QIOTRD: <cur_len>,<remain_len>,<remain_pieces>
    <data>
    ```
    否则
     ```c
    ERROR
     ```

---

* __参数：__
    * __`<req_length>`__： 整型。读取的数据长度。长度范围以测试命令实际返回值为准。
    * __`<cur_len>`__： 整型。实际读取的数据长度。
    * __`<data>`__： 字节流类型。读取的数据。
    * __`<remain_len>`__： 整型。当前数据包剩余未读的数据长度。单位：字节。
    * __`<remain_pieces>`__： 整型。剩余数据包个数。

* __备注：__
    * __2.3.3及以上版本支持__

***

#### **示例**
 <span style="color:#999AAA">提示：业务数据交互期间请保证设备正常在线。</span>

__示例1 (缓存模式读取下行数据)__  
__i.__  平台下发“123456”的透传数据。  
__ii.__ 模组打印回调事件 __{+QIOTEVT: 5,10200}__。  
__iii.__ 发送 __AT+QIOTRD?__ 查询未读数据包个数。  
__iiii.__ 发送 __AT+QIOTRD=1024__ 查询读取的数据。  
```c
[RX]+QIOTEVT: 5,10200
[TX]AT+QIOTRD?

[RX]+QIOTRD: 1

[RX]OK
[TX]AT+QIOTRD=1024

[RX]+QIOTRD: 6,0,0
[RX]123456
[RX]OK
```

__示例2 (非缓存模式读取下行数据)__
在非缓存下， IoT 平台下行透传数据模组会将数据主动打印到串口。
```c
[RX]+QIOTEVT: 5,10200,6
123456
```
***
<span id="AT+QIOTMODELTD">  </span>
## <span style="color:#A52A2A">__AT+QIOTMODELTD (发送物模型数据)__</span>

* __说明__：该命令用于发送物模型数据至平台。设置命令中若指定\<PkgID>，则发送数据为应答平台数据请求；若省略\<PkgID>，则发送数据至平台;<br>
    * __上报成功且QoS等级为1或2时，MCU接收事件+QIOTEVT: 4,10210表示物模型数据发送成功__

* __最大响应时间__ ：300 毫秒。
* __特性说明__：该命令立即生效。


### **测试命令**

* __发送：__

    ```c
    AT+QIOTMODELTD=?
    ```
* __响应：__

    响应支持的参数范围

    ```c
    +QIOTMODELTD: (支持的<mode>范围),<length>,(支持的 <PkgID>范围)
    
    OK
    ```

### **设置命令**

* __发送：__

    ```c
    AT+QIOTMODELTD=<mode>,<length>[,<PkgID>]
    ```
* __响应：__

    ```c
    >
    响应>后，输入长度等于<length>的字节流数据。
    ```
    若配置上行消息需应答PkgID，则为应答平台查询的消息

    ```c
    OK
    ```

     若配置上行消息无需应答PkgID，则为主动发送上行消息
     * 其中当配置AT+QIOTCFG="txid_mode",1 返回内容为
         ```c
         +QIOTMODELTD: <txid>
     
         OK
         ```
     * 否则返回内容为
     
         ```c
         OK
         ```
     
     * 若出现任何错误
     
        ```c
        ERROR
        ```
---

* __参数：__
    * __`<mode>`__ 整型。发送模式<br>
        * __`0`__ ：QoS=0 最多发送一次 
        * __`1`__ ：QoS=1 至少发送一次
        * __`2`__ ：QoS=2 准确一次送达

    * __`<length>`__： 整型。待发送数据长度。长度范围以具体模块性能为准。
    * __`<PkgID>`：__ 整型。请求包ID。范围：1~65534。仅当设备需响应平台数据请求时需指定该参数。
    * __`<txid>`：__ 整型。
        * Qos = 0时txid为0
        * Qos > 0时，返回发送消息txid(1~65535)

* __备注：__
    * __2.3.3及以上版本支持。若发送JSON格式则需2.8.2及以上版本支持。若指定可选参数\<PkgID\>则需2.9.2及以上版本支持。__


---

#### 示例
<span style="color:#999AAA">前提：在开发者中心根据实际项目需求添加物模型功能，业务数据交互期间请保证设备正常在线。</span>

__示例1 (发送物模型属性数据)__

* 物模型 ID：1；类型：布尔值；内容：true ；
* 物模型 ID：2；类型：整型；内容：100；
* 物模型 ID：3；类型：浮点型；内容：28.56 ；
* 物模型 ID：4；类型：枚举；内容：1 ；
* 物模型 ID：5；类型：文本型；内容：Hello World ；
* 物模型 ID：6；类型：日期；内容：1658030723000 ；
* 物模型 ID：7；类型：结构体；结构体内容：{"1":true,"2":100} ；

```c
[TX]AT+QIOTMODELTD=1,93

[RX]> 
[TX]{"1":true,"2":100,"3":28.56,"4":1,"5":"Hello World","6":1658030723000,"7":{"1":true,"2":100}}
[RX]OK

[RX]+QIOTEVT: 4,10210
```

__示例2 (发送物模型事件数据)__

物模型事件 ID：2；{ 输出参数 ID：1；类型：浮点型；内容：25.86 }；

```c
[TX]AT+QIOTMODELTD=1,17

[RX]> 
[TX]{"2":{"1":25.86}}
[RX]OK

[RX]+QIOTEVT: 4,10210
```

__示例3 (响应平台物模型请求包)__

__i.__ 在开发者中心 __点击读取物模型按钮__。
__ii.__ 模组打印回调事件{+QIOTEVT: 5,10211,7,1}。
__iii.__ 响应请求包 ID为 __7__ 的物模型数据。

```c
[TX]AT+QIOTMODELTD=1,11,7

[RX]> 
[TX]{"1":25.14}
[RX]OK

[RX]+QIOTEVT: 4,10210
```

__示例4 (发送物模型属性数组结构体数据)__

* 物模型 ID：8；类型：数组；最大元素个数：3；
    * 元素类型：结构体
      * 物模型 ID：1；类型：布尔值；
      * 物模型 ID：2；类型：整型；
      * 物模型 ID：3；类型：枚举；
      * 物模型 ID：4；类型：文本型；

```c
[TX]AT+QIOTMODELTD=1,120

[RX]> 
[TX]{"8":[{"1":true,"2":10,"3":1,"4":"ABC123"},{"1":false,"2":10,"3":1,"4":"ABC123"},{"1":false,"2":10,"3":1,"4":"ABC123"}]}
[RX]OK

[RX]+QIOTEVT: 4,10210
```

***
<span id="AT+QIOTMODELRD">  </span>

## <span style="color:#A52A2A">__AT+QIOTMODELRD (读取物模型数据)__</span>

* __说明__：该命令用于读取平台下发的物模型数据。
* __最大响应时间__ ：300 毫秒。
* __特性说明__： 该命令立即生效。

### **测试命令**

* __发送：__

    ```c
    AT+QIOTMODELRD=?
    ```
* __响应：__

     响应支持的读取的数据范围

    ```c
    +QIOTMODELRD: (支持的<req_length>范围)
    
    OK
    ```

### **查询命令**

* __发送：__

    ```c
    AT+QIOTMODELRD?
    ```
* __响应：__

    响应剩余数据包个数

    ```c
    +QIOTMODELRD: <remain_pieces>
    
    OK
    ```

### **设置命令**

* __发送：__

    ```c
    AT+QIOTMODELRD=<req_length>
    ```
* __响应：__

     若指定可选参数且连接已经建立

    ```c
    +QIOTMODELRD: <cur_len>,<remain_len>,<remain_pieces>
    <data>    
        
    OK
    ```
     若出现任何错误
     ```c
    ERROR
     ```

---

* __参数：__
    * __`<req_length>`__： 整型。读取的数据长度。长度范围以测试命令实际返回值为准。
    * __`<cur_len>`__：整型。实际读取的数据长度。
    * __`<data>`__： 字节流类型。读取的物模型数据。
    * __`<remain_len>`__： 整型。当前数据包剩余未读的数据长度。单位：字节。
    * __`<remain_pieces>`__： 整型。剩余数据包个数。

* __备注：__
    * __2.3.3及以上版本支持__

---

#### **示例**
 <span style="color:#999AAA">前提：在开发者中心根据实际项目需求添加物模型，业务数据交互期间请保证设备正常在线。</span>

__示例1 (非缓存模式下读取物模型数据)__

* 物模型 ID：1；类型：布尔值；内容：true ；
* 物模型 ID：2；类型：整型；内容：100；
* 物模型 ID：3；类型：浮点型；内容：28.56 ；
* 物模型 ID：4；类型：枚举；内容：1 ；
* 物模型 ID：5；类型：文本型；内容：Hello World ；
* 物模型 ID：6；类型：日期；内容：1658030723000 ；
* 物模型 ID：7；类型：结构体；结构体内容：{"1":true,"2":100} ；

```c
[RX]+QIOTEVT: 5,10210,93
[RX]{"1":true,"2":100,"3":28.56,"4":1,"5":"Hello World","6":1658030723000,"7":{"1":true,"2":100}}
```

__示例2 (缓存模式下读取物模型数据)__

* 物模型 ID：1；类型：布尔值；内容：true ；
* 物模型 ID：2；类型：整型；内容：100；
* 物模型 ID：3；类型：浮点型；内容：28.56 ；
* 物模型 ID：4；类型：枚举；内容：1 ；
* 物模型 ID：5；类型：文本型；内容：Hello World ；
* 物模型 ID：6；类型：日期；内容：1658030723000 ；
* 物模型 ID：7；类型：结构体；结构体内容：{"1":true,"2":100} ；

```c
[RX]+QIOTEVT: 5,10210
[TX]AT+QIOTMODELRD=1024 
[RX]+QIOTMODELRD: 93,0,0
[RX]{"1":true,"2":100,"3":28.56,"4":1,"5":"Hello World","6":1658030723000,"7":{"1":true,"2":100}}

[RX]OK
```
***

<span id="AT+QIOTINFO">  </span>

## <span style="color:#A52A2A">__AT+QIOTINFO (请求QuecThing上报设备信息)__</span>

* __说明__：该命令用于请求QuecThing上报设备信息。
* __最大响应时间__ ：300 毫秒。
* __特性说明__：该命令立即生效。

### **测试命令**

* __发送：__

    ```c
    AT+QIOTINFO=?
    ```
* __响应：__


    ```c
    +QIOTINFO: "up"
    
    OK
    ```

### **查询命令**

* __发送：__

    ```c
    AT+QIOTINFO?
    ```
* __响应：__

    响应上报信息的关键字及数据

    ```c
    +QIOTINFO: <item1>,<value1>
    [+QIOTINFO: <item2>,<value2>
    [...]]
    OK
    ```
    若出现任何错误
    ```c
    ERROR
    ```

### **设置命令**

* __发送：__

    ```c
    AT+QIOTINFO="up"
    ```
* __响应：__

    上报设备信息成功

    ```c
    OK
    ```
     或者
     ```c
    ERROR
     ```

---

* __参数：__
    * __`<itemN>`__： 字符串类型。上报信息的关键字。
    * __`<valueN>`__：字符串类型。数据。

* __备注：__
    * __2.3.3及以上版本支持__

---

#### **示例**

__示例1 (上报设备信息)__

```c
[TX]AT+QIOTINFO="up"

[RX]OK
```

__上报设备包含信息，如下：__

__设备状态__

|    字段     |     描述     |
| :---------: | :----------: |
|    type     |   模组型号   |
|   version   |   模组版本   |
| mcu_version |   MCU版本    |
|   cell_id   |    基站ID    |
|   icc_id    |   SIM卡号    |
|     mcc     | 移动国家代码 |
|     mnc     | 移动网络代码 |
|     lac     |  位置区代码  |
|  phone_num  |   phone号    |
|   sim_num   |    SIM号     |
|   sdk_ver   |  SDK版本号   |

__设备信息__

|      字段       |            描述             |
| :-------------: | :-------------------------: |
|     battery     |       电量（百分比）        |
|     voltage     |        电压（伏特）         |
| signal_strength |      信号强度（RSSI）       |
|   memory_free   |      剩余空间（字节）       |
|      rsrp       |  参考信号接收功率（RSRP）   |
|      rsrq       | LTE参考信号接收质量（RSRQ） |
|       snr       |  信号与干扰加噪声比（SNR）  |



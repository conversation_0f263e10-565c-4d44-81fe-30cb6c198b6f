---
title: "账号详情"
description: "账号详情相关文档"
---

# 账号详情

本文介绍了如何对用户的信息进行管理

## 功能概述

在该模块中，您可以调用对象 QuecUserService 获取当前用户的所有信息及相关的登录注册方法。QuecUserModel数据模型如下表所示：

| 参数            | 类型     | 说明     |
|---------------|--------|--------|
| nikeName      | String | 昵称     |
| phone         | String | 手机号    |
| registerTime  | String | 注册时间   |
| sex           | String | 性别     |
| timezone      | String | 时区     |
| uid           | String | 用户ID   |
| wchartId      | String | 微信ID   |
| wchartName    | String | 微信昵称   |
| address       | String | 地址     |
| email         | String | 邮箱     |
| headimg       | String | 头像     |
| lang          | String | 语言     |
| lastLoginTime | String | 上次登录时间 |
| nationality   | String | 国籍     |

```objc
#import <QuecUserKit/QuecUserKit.h>
/// 初始化 用户管理相关服务
[QuecUserService sharedInstance]
/// 初始化 消息管理相关服务
[QuecUserMessageService sharedInstance]
```

### 设置登录状态失效回调

**接口说明**

当登录状态失效时, 该回调会触发

```objc
- (void)setTokenInvalidCallBack:(QuecVoidBlock)callBack;
```

**参数说明**

| 参数             | 是否必传 | 说明     |
|----------------|-----|--------|
| callBack            | 是    | 设置回调 |

**示例代码**

```objc
[[QuecUserService sharedInstance] setTokenInvalidCallBack:^{
    /// 登录失效处理
}];
```

### 修改手机号

**接口说明**

用于修改用户登录手机号

```objc
- (void)updatePhone:(NSString *)newPhone
newInternationalCode:(NSString *)newInternationalCode
       newPhoneCode:(NSString *)newPhoneCode
           oldPhone:(NSString *)oldPhone
oldInternationalCode:(NSString *)oldInternationalCode
       oldPhoneCode:(NSString *)oldPhoneCode
            success:(QuecVoidBlock)success
            failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|
| --- | --- | --- |
| newPhone |	是|新手机号码| 
| newInternationalCode |	是|新手机号码国际代码| 
| newPhoneCode |	是|新手机号码接收到的验证码| 
| oldPhone |	是|原手机号码| 
| oldInternationalCode |	是|原手机号码国际代码| 
| oldPhoneCode |	是|原手机号码接收到的验证码| 
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	|

**示例代码**

```objc
[QuecUserService.sharedInstance updatePhone:@"newPhone"
                        newInternationalCode:@"newInternationalCode"
                                newPhoneCode:@"newPhoneCode"
                                    oldPhone:@"oldPhone"
                        oldInternationalCode:@"oldInternationalCode"
                                oldPhoneCode:@"oldPhoneCode"
                                    success:^{
    /// Next Action 
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 修改用户头像

**接口说明**

传入图片地址修改用户头像

```objc
- (void)updateUserIconWithImagePath:(NSString *)imagePath success:(QuecVoidBlock)success failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|
| --- | --- | --- |
| imagePath |	是|图片地址|
| success |	否|接口请求成功回调	|
| failure |	否|接口请求失败回调	|

**示例代码**

```objc
[[QuecUserService sharedInstance] updateUserIconWithImagePath:@"avatarUrl" success:^{
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```
### 修改用户昵称

**接口说明**

用于修改用户昵称

```objc
- (void)updateUserNickName:(NSString *)nikeName success:(QuecVoidBlock)success failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|
| --- | --- | --- |
| nikeName |	是|昵称|  
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	|


**示例代码**

```objc
[[QuecUserService sharedInstance] updateUserNickName:@"nickname" success:^{
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 修改用户性别

**接口说明**

用于修改用户性别

```objc
- (void)updateUserSex:(NSInteger)sex success:(QuecVoidBlock)success failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|
| --- | --- | --- |
| sex |	是|性别 0：男 1：女 2：保密|  
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	|

**示例代码**

```objc
[QuecUserService.sharedInstance updateUserSex:2 success:^{
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 修改密码

**接口说明**

用于用户修改登录密码

```objc
- (void)updatePassword:(NSString *)newPassword 
           oldPassword:(NSString *)oldPassword
               success:(QuecVoidBlock)success
               failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|
| --- | --- | --- |
| newPassword |	是| 新密码 | 
| oldPassword |	是|原密码|  
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	|

**示例代码**

```objc
[[QuecUserService sharedInstance] updatePassword:@"new pwd" oldPassword:@"old pwd" success:^{
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 删除用户

**接口说明**

用于当前用户删除

```objc
- (void)deleteUser:(NSInteger)type success:(QuecVoidBlock)success failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|
| --- | --- | --- |
| type |	否| 删除类型：1- 立即删除 2- 7天后删除，默认为 7 天后删除 |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	|

**示例代码**

```objc
[[QuecUserService sharedInstance] deleteUser:_type success:^{
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 用户信息读取

**接口说明**

用于获取用户信息

```objc
- (void)getUserInfoWithSuccess:(void(^)(QuecUserModel *userModel))success failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|
| --- | --- | --- |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	|

**示例代码**

```objc
[QuecUserService.sharedInstance getUserInfoWithSuccess:^(QuecUserModel *userModel) {
    /// Next Action
}  failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 退出登录

**接口说明**

用户退出登录

```objc
- (void)logoutWithSuccess:(QuecVoidBlock)success failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|
| --- | --- | --- |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	|

**示例代码**

```objc
[[QuecUserService sharedInstance] logoutWithSuccess:^{
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 是否登录

**接口说明**

检查用户是否登录

```objc
- (void)checkUserLoginState:(void(^)(BOOL isLogin))completion;
```

**参数说明**

|参数|	是否必传|说明|
| --- | --- | --- |
| completion | 是       | 回调 |


**示例代码**

```objc
[QuecUserService.sharedInstance checkUserLoginState:^(BOOL isLogin) {
    if (isLogin) {
        
    } else{
        
    }
    }];
```

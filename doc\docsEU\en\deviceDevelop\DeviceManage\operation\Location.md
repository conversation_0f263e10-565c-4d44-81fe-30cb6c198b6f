# Device Positioning

This section provides information on device positioning. If your device has positioning capabilities, the reported positioning data will be parsed and displayed. Currently, Developer Center can parse Global Positioning System (GPS), Location Based Service (LBS), and Wireless Fidelity (WIFI) positioning data using triangulation based on wireless router signal.

## **Prerequisites**

● A product has been created.<br />
● A device that supports the positioning feature has been connected to Developer Center successfully.<br />
● Positioning data has been reported.

## **Steps**

Log in to Developer Center, click "Device Management"→ "Device Maintenance” from the left-hand navigation bar to display the list of devices. Find the device you want to view and click "View" in the Action column to enter "Device Information" page, and then click "Location" tab to view location-related data for the device.

<a data-fancybox title="img" href="/en/guide/image2022-3-10_14-11-40.png?version=1&modificationDate=1646892109000&api=v2">![img](/en/guide/image2022-3-10_14-11-40.png?version=1&modificationDate=1646892109000&api=v2)</a>

Parameters

| **Parameter**                | **Description**                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    |
| :--------------------------- | :----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| Positioning Time             | The most recent time when the positioning data was reported                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
| Number of Visible Satellites | When the   positioning data is GPS, the number of satellites currently in view for   positioning may be reported.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  |
| Positioning State            | **Differential   Positioning**: A satellite signal receiver (Satellite Ground   Station) is at a fixed position, enabling it to calculate satellite positioning   error. The handheld receiver uses the satellite signals to calculate   coordinates, which are then refined by synthesizing the positioning error data   provided by the ground station, resulting in improved accuracy. **Non-differential   Positioning**: A single receiver is used for positioning to get the   absolute coordinates of the receiver antenna. **Valid Positioning**: Accurate   information such as latitude and longitude have been obtained. **Invalid   Positioning**: Invalid latitude and longitude, speed and direction information   has been obtained. **Estimating**: indicates an approximate estimate   of the current location information and positioning state. |
| Positioning Method           | GNSS: Global   Navigation Satellite System; GPS: Global Positioning System (United States);   GL: GLONASS (Russia); GA: Galileo (European Union); BD/PQ: Navigation   Satellite System (China); LBS: Location Based Services; WIFI: A location   tracking system based on wireless router signal                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   |
| Longitude and Latitude       | The latitude and longitude reported based on WGS-84 coordinate system                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              |


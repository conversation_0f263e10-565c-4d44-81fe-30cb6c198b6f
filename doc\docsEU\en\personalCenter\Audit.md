# Logs

All user operations in Developer Center will be recorded in logs.

When you have multiple sub-accounts, you can track data change records and identify corresponding operators within Developer Center through logs.

Log Field:

| **Field**   | **Description**                                              |
| ----------- | ------------------------------------------------------------ |
| ID          | A unique identifier for each event log.                      |
| Type        | Event type: user, product, device, system, application, OTA upgrade, message subscription, CA certificate, and permission. |
| Operation   | Operations performed: addition, modification, deletion, import, export, registration, upload, login, download, and others. |
| Time        | Time at which the event was triggered.                       |
| Username    | User or system that produced the event.                      |
| Description | Description of the log. You can click "**More**" to view a specific log. |
| Remarks     | You can add your own remarks to differentiate logs.          |

Note: Logs will be stored for 6 months and will be deleted after the expiration.


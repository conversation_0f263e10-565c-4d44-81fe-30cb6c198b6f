# 创建产品
## __场景说明__
香薰生活已经慢慢成为了一种潮流，外形典雅时尚的香薰机除了是精油的好伴侣以外，还可以充当摆设品。星级酒店，连锁餐饮业，甚至高级服装品牌店，美发沙龙也开始了运用香味营销。我们基于 __具有QuecThing功能的移远模组__、 __开发者中心__ ，设备端到平台构建一款智能 __香薰机__ 解决方案样例，带您体验快速连接 IOT 平台。无论人在何处，都可以通过 __开发者中心__ 进行实时批量查询香薰机剩余精油容量、切换运行模式等功能。   本文介绍如何在 __开发者中心__ 创建产品。

## __概述__

开发者中心是移远提供的 IoT 一站式开发平台，下面将从登录平台到新建产品等三个环节来演示开发者中心的操作步骤。

## __一、登录开发者中心__

登录<a href="https://iot.quectelcn.com" target="_blank">开发者中心</a>，未注册可单击<a href="https://iot.quectelcn.com/registerType" target="_blank">立即注册</a>完成注册。


## __二、创建产品__
创建产品是产品开发流程的第一步，产品是开发者中心定义一类设备的抽象描述，用于对同类设备进行管理。在创建产品时，需要选择数据协议，其中包含 __物模型__ 与 __透传/自定义__ 两种数据格式，以下将以创建物模型数据格式产品作为示例，用户可根据实际业务场景选择其一。

* __物模型概述__

	__物模型__ 是指设备在开发者中心的数字化表示，并在开发者中心构建的实体数据模型。开发者中心定义的标准数据格式为 __物模型__ ，是平台与设备间通讯的数据格式。物模型分为 __属性__ 、__服务__ 、__事件__ 三个维度。定义了物模型在发布以后，即完成了物模型产品的定义，平台对数据进行解析检验和处理。

* __透传/自定义概述__ 

	__透传__ 即透明传输，指的是在通讯中不管传输的业务内容如何，只负责设备与平台的数据传输交互，而不对业务数据内容做任何改变，开发者中心也不会对其业务数据进行解析。在硬件设备中，或许会存在一些 __硬件配置低__ 或自身 __无法构建物模型__ 与开发者中心进行通讯的设备。针对这类设备，您可以选择 __透传/自定义__ 将原始数据透传至平台。



<span style="color:#999AAA">提示：ProductKey(以下简称为"PK")、ProductSecret(以下简称为"PS")码需用户保存好，设备的认证与产品删除等将会需要使用到该PK、PS码。</span>

### __创建物模型数据产品__
点击 __创建产品__ 来创建一个新的产品，产品名称为 __“智能香氛机”__ 。本次数据格式采用 __物模型__ 方式为例来演示，如下图所示。
<span style="color:#999AAA">提示：联网方式按照实际的模组类型选择，截图采用2345G演示。</span>

<span style="color:#999AAA">提示：对已有标准物模型的品类，创建产品后，可手动添加标准功能。</span>

<a data-fancybox title="img" href="/zh/deviceDevelop/develop/speediness/resource/platform/platform-02.png">![img](/zh/deviceDevelop/develop/speediness/resource/platform/platform-02.png)</a>


* __产品名称__：产品所具有的功能命名。
* __产品品类__：开发者中心已为各大行业品类预定义了标准功能，创建产品时若选择标准品类，则可选择添加标准功能。点击**查看功能**，可在弹出的列表框中查看该产品的标准功能。 <span style="color:#999AAA">提示：若功能定义不同，仍可进入草稿模式进行修改。</span>
* __设备类型__：**单品设备**指可直接接入网络并访问开发者中心的设备；**网关设备**与**网关子设备**指通过 Wi-Fi 连网的方式访问开发者中心的设备。本文档所述LTE模块为单品设备。
* __连网方式__：选择设备功能对应的连网方式。
* __数据格式__：开发者中心支持物模型与透传/自定义两种数据格式，可根据实际的实际业务场景选择其一。


## __三、产品配置（可选）__

创建好产品后，需要对此"产品"进行配置,配置信息包括
* __授权移联万物APP__
* __配置控制面板__（移联万物APP默认 __面板__ 为公版面板，如需定制请联系移远销售）


"**产品交互**" 界面如下图所示：
<a data-fancybox title="img" href="/zh/deviceDevelop/develop/speediness/resource/platform/platform-03.png">![img](/zh/deviceDevelop/develop/speediness/resource/platform/platform-03.png)</a>



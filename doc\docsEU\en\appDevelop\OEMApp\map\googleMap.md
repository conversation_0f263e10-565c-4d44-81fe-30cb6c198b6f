# Configure Google Maps

### Apply for Google API Key to Set Up Your Google Cloud

##### Log in to Google Cloud with your Google account (login website: [https://console.cloud.google.com/welcome?hl=en](https://console.cloud.google.com/welcome?hl=en)).

##### Start a free trial

<a data-fancybox title="img" href="/en/appDevelop/oemapp/map/googlemap102.png">![image](/en/appDevelop/oemapp/map/googlemap102.png)</a>

##### Create a new project

Click "**Select a project**" > "**NEW PROJECT**", and enter the project name.

<a data-fancybox title="img" href="/en/appDevelop/oemapp/map/googlemap103.png">![image](/en/appDevelop/oemapp/map/googlemap103.png)</a>

<a data-fancybox title="img" href="/en/appDevelop/oemapp/map/googlemap104.png">![image](/en/appDevelop/oemapp/map/googlemap104.png)</a>

<a data-fancybox title="img" href="/en/appDevelop/oemapp/map/googlemap105.png">![image](/en/appDevelop/oemapp/map/googlemap105.png)</a>

##### Select the created project and enter the API library 

Click the navigation bar on the left side, select "**APIs & Services**" and click "**Library**".

<a data-fancybox title="img" href="/en/appDevelop/oemapp/map/googlemap106.png">![image](/en/appDevelop/oemapp/map/googlemap106.png)</a>

<a data-fancybox title="img" href="/en/appDevelop/oemapp/map/googlemap107.png">![image](/en/appDevelop/oemapp/map/googlemap107.png)</a>

##### Enable all required "Maps" APIs

For example, if you want to enable the API "**Maps SDK for Android**", the operation steps are as follows.

1. Click "**Maps SDK for Android**" to enter the product details page.
2. Click "**ENABLE**",  and the tag "API Enabled" will be displayed.

<a data-fancybox title="img" href="/en/appDevelop/oemapp/map/googlemap108.png">![image](/en/appDevelop/oemapp/map/googlemap108.png)</a>

<a data-fancybox title="img" href="/en/appDevelop/oemapp/map/googlemap109.png">![image](/en/appDevelop/oemapp/map/googlemap109.png)</a>

View all the enabled APIs on the "**Google Maps Platform**" page. All APIs that need to be enabled are shown in the figure below.

<a data-fancybox title="img" href="/en/appDevelop/oemapp/map/googlemap110.png">![image](/en/appDevelop/oemapp/map/googlemap110.png)</a>

##### After all the APIs are enabled, generate an API key

1. Click the navigation bar on the left side, select "**APIs & Services**" and click "**Credentials**".
2. Click "**CREATE CREDENTIALS**" > "**API key**" to generate an API key.

<a data-fancybox title="img" href="/en/appDevelop/oemapp/map/googlemap111.png">![image](/en/appDevelop/oemapp/map/googlemap111.png)</a>

<a data-fancybox title="img" href="/en/appDevelop/oemapp/map/googlemap112.png">![image](/en/appDevelop/oemapp/map/googlemap112.png)</a>

<a data-fancybox title="img" href="/en/appDevelop/oemapp/map/googlemap113.png">![image](/en/appDevelop/oemapp/map/googlemap113.png)</a>

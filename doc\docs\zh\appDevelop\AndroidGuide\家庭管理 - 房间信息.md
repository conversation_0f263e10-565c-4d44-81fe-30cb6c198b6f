# 房间信息

## 功能概述

本文介绍了如何管理家庭下房间信息, 包括创建房间, 修改房间名称, 删除房间, 移入设备到房间, 设置房间排序, 房间中设备列表等。

## 房间管理

### 创建房间

**接口说明**

创建家庭下房间

```kotlin
fun addFamilyRoom(fid: String, roomName: String, callback: QuecCallback<Unit>)
```

**参数说明**

| 参数       | 	是否必传 | 说明   |	
|----------|-------|------| 
| fid      | 	是    | 家庭id | 
| roomName | 	是    | 房间名称 | 
| callback | 是     | 请求回调 |

**示例代码**

```kotlin
QuecSmartHomeService.addFamilyRoom("fid", "roomName") {
    if (it.isSuccess) {
        //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 修改房间名称

**接口说明**

修改房间名称

```kotlin
fun setFamilyRoom(frid: String, roomName: String, callback: QuecCallback<Unit>)
```

**参数说明**

| 参数       | 	是否必传 | 说明     |	
|----------|-------|--------| 
| frid     | 	是    | 家庭房间id | 
| roomName | 	是    | 房间名称   | 
| callback | 是     | 请求回调   |

**示例代码**

```kotlin
QuecSmartHomeService.setFamilyRoom("fid", "newRoomName") {
    if (it.isSuccess) {
        //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 删除房间

**接口说明**

删除房间, 支持批量删除

```kotlin
fun deleteFamilyRooms(fridList: List<String>, callback: QuecCallback<Unit>)
```

**参数说明**

| 参数       | 	是否必传 | 说明     |	
|----------|-------|--------| 
| fridList | 	是    | 房间id列表 | 
| callback | 是     | 请求回调   |

**示例代码**

```kotlin
QuecSmartHomeService.deleteFamilyRooms(listOf("fid")) {
    if (it.isSuccess) {
        //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 移入设备到房间

**接口说明**

移入设备到房间, 支持批量添加

```kotlin
fun addDeviceInFamilyRoom(
    deviceList: List<QuecAddDeviceEnterModel>, callback: QuecCallback<QuecFamilyAddDeviceModel>
)
```

**参数说明**

| 参数         | 	是否必传 | 说明   |	
|------------|-------|------| 
| deviceList | 	是    | 设备列表 | 
| callback   | 是     | 请求回调 |

**QuecAddDeviceEnterModel属性定义**

| 字段      | 类型     | 描述   |
|---------|--------|------|
| pk      | String | 产品pk |
| dk      | String | 设备dk |
| oldFrid | String | 原房间号 |
| nFrid   | String | 新房间号 |

**QuecFamilyAddDeviceModel属性定义**

| 字段          | 类型                                    | 描述     |
|-------------|---------------------------------------|--------|
| successList | List&lt;QuecFamilyAddDeviceListModel> | 执行成功列表 |
| failureList | List&lt;QuecFamilyAddDeviceListModel> | 执行失败列表 |

**QuecFamilyAddDeviceListModel属性定义**

| 字段      | 类型     | 描述                        |
|---------|--------|---------------------------|
| oldFrid | String | 原房间号                      |
| nFrid   | String | 新房间号                      |
| pk      | String | 产品pk                      |
| dk      | String | 设备dk                      |
| code    | Int    | 错误码（仅failureList有此属性）     |
| String  | String | 错误提示文案 （仅failureList有此属性） |

**示例代码**

```kotlin
QuecSmartHomeService.addDeviceInFamilyRoom(
    listOf(
        QuecAddDeviceEnterModel(
            "pk",
            "dk",
            null,
            "newFrid"
        )
    )
) {
    if (it.isSuccess) {
        //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 设置房间排序

**接口说明**

设置房间排序, 设置房间显示优先级

```kotlin
fun setFamilyRoomSort(
    roomSortList: List<QuecSortDeviceEnterModel>, callback: QuecCallback<Unit>
)
```

**参数说明**

| 参数           | 	是否必传 | 说明      |	
|--------------|-------|---------| 
| roomSortList | 	是    | 待排序房间数组 | 
| callback     | 是     | 请求回调    |

**QuecSortDeviceEnterModel属性定义**

| 字段       | 类型     | 描述                                    |
|----------|--------|---------------------------------------|
| frid     | String | 家庭房间ID                                |
| roomSort | String | 房间顺序，从 0 开始累加，数字小的排列在列表前面，可以不连续，不允许相等 |

**示例代码**

```kotlin
QuecSmartHomeService.setFamilyRoomSort(
    listOf(
        QuecSortDeviceEnterModel("frid1", 0),
        QuecSortDeviceEnterModel("frid2", 1),
    )
) {
    if (it.isSuccess) {
        //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 房间中设备列表

**接口说明**

查询房间中设备列表

```kotlin
fun getFamilyRoomDeviceList(
    frid: String,
    pageNumber: Int,
    pageSize: Int,
    isGroupDeviceShow: Boolean,
    callback: QuecCallback<QuecPageResponse<QuecDeviceModel>>
)
```

**参数说明**

| 参数                | 	是否必传 | 说明            |	
|-------------------|-------|---------------| 
| frid              | 	是    | 家庭房间id        | 
| pageNumber        | 	否    | 页码，非必填，默认1    | 
| pageSize          | 	否    | 页大小，非必填，默认10  | 
| isGroupDeviceShow | 	否    | 是否显示群组设备，默认缺省 | 
| callback          | 是     | 请求回调          |

> QuecDeviceModel属性定义同设备SDK说明

**示例代码**

```kotlin
QuecSmartHomeService.getFamilyRoomDeviceList("frid", 1, 10, true) {
    if (it.isSuccess) {
        val data = it.data //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 家庭下的房间列表

**接口说明**

查询家庭中的房间列表

```kotlin
fun getFamilyRoomList(
    fid: String,
    pageNumber: Int,
    pageSize: Int,
    callback: QuecCallback<QuecPageResponse<QuecFamilyRoomItemModel>>
)
```

**参数说明**

| 参数         | 	是否必传 | 说明           |	
|------------|-------|--------------| 
| fid        | 	是    | 家庭id         | 
| pageNumber | 	否    | 页码，非必填，默认1   | 
| pageSize   | 	否    | 页大小，非必填，默认10 | 
| callback   | 是     | 请求回调         |

> QuecFamilyRoomItemModel属性定义同上

**示例代码**

```kotlin
QuecSmartHomeService.getFamilyRoomList("fid", 1, 10) {
    if (it.isSuccess) {
        val data = it.data //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 编辑设备信息

**接口说明**

编辑设备信息, 设置房间信息 是否常用等

```kotlin
fun setDeviceInfo(
    modelArray: List<QuecSetDeviceInfoModel>, callback: QuecCallback<QuecFamilySetDeviceModel>
)
```

**参数说明**

| 参数         | 	是否必传 | 说明          |	
|------------|-------|-------------| 
| modelArray | 	否    | 待设置房间信息设备列表 | 
| callback   | 是     | 请求回调        |

**QuecSetDeviceInfoModel属性定义**

| 字段           | 类型     | 描述                                    |
|--------------|--------|---------------------------------------|
| fid          | String | 家庭Id                                  |
| dk           | String | 设备dk                                  |
| pk           | String | 产品pk                                  |
| deviceName   | String | 设备名称                                  |
| isCommonUsed | BOOL   | 是否常用：true-常用，false-不是常用               |
| type         | int    | 设备类型：1-家庭中的设备，2-用户接收分享的设备，3-用户多绑模式的设备 |
| oldFrid      | String | 移出房间ID                                |
| selectFrid   | String | 移入房间ID                                |
| shareCode    | String | 分享码                                   |

**示例代码**

```kotlin
QuecSmartHomeService.setDeviceInfo(
    listOf(
        QuecSetDeviceInfoModel(
            fid = "fid",
            dk = "dk",
            pk = "pk",
            deviceName = "deviceName",
            isCommonUsed = true,
            type = 3
        )
    )
) {
    if (it.isSuccess) {
        //请求成功
    } else {
        val code = it.code //请求失败,
    }
}
```

# 消息管理

本文介绍了如何对消息进行管理, 包括设备告警和事件消息, 自动化和场景执行结果消息

## 功能概述

```kotlin
QuecUserMessageService
```

### 查询消息列表

**接口说明**

查询用户的消息列表(根据消息类型查询，标签查询调用下方新Api)

```kotlin
fun getUserMessageListByPageNumber(
    pageNumber: Int,
    pageSize: Int,
    msgType: Int,
    isRead: Boolean,
    deviceKey: String?,
    productKey: String?,
    callback: QuecCallback<QuecPageResponse<QuecUserMessageListModel>>
)
```

**参数说明**

| 参数         | 	是否必传 | 说明                                                    |
|------------|-------|-------------------------------------------------------|
| pageNumber | 	否    | 查询的列表页，默认为 1	                                         | 
| pageSize   | 	否    | 查询的页大小，默认 10	                                         | 
| msgType    | 	否    | 1：设备通知，2：设备告警，3：设备故障，4：系统消息；接收的消息类型任意组合，多个类型使用英文逗号分隔	 | 
| isRead     | 	否    | 是否已读	                                                 | 
| deviceKey  | 	否    | device key	                                           | 
| productKey | 	否    | product key	                                          | 
| callback   | 是     | 请求回调                                                  |

**QuecUserMessageListModel属性定义**

| 参数               | 	类型     | 说明                                       |
|------------------|---------|------------------------------------------|
| msgId            | String  | 消息ID                                     |
| msgType          | int     | /// 消息类型  1-设备通知  2-设备告警  3-设备故障  4-系统消息 |
| isRead           | boolean | 是否已读                                     |
| title            | String  | 标题                                       |
| content          | String  | 内容                                       |
| pk               | String  | Product Key                              |
| dk               | String  | Device Key                               |
| addTime          | long    | 添加时间                                     |
| readTime         | long    | 阅读时间                                     |
| shareCode        | String  | 分享code                                   |
| sceneIcon        | String  | 场景图标                                     |
| sceneName        | String  | 场景名称                                     |
| sceneExecuteTime | String  | 场景执行时间                                   |
| executeId        | String  | 场景执行Id                                   |
| logId            | String  | 自动化日志id                                  |
| automationId     | String  | 自动化Id                                    |
| firstLabel       | String  | 消息一级分类                                   |
| secondaryLabel   | String  | 消息二级分类                                   |

**示例代码**

```kotlin
QuecUserMessageService.getUserMessageListByPageNumber(1, 10, 1, false, null, null) {
    if (it.isSuccess) {
        val value = it.data //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 阅读消息

**接口说明**

单个及批量阅读消息(根据标签阅读调用下面新Api)

```kotlin
fun readMessageByMsgIdList(
    msgIdList: String?,
    msgType: Int,
    callback: QuecCallback<QuecReadMsgModel>
)
```

**参数说明**

| 参数        | 	是否必传 | 说明                                                    |
|-----------|-------|-------------------------------------------------------|
| msgIdList | 	否    | 阅读的消息ID列表 多个ID使用英文逗号分隔,如果不传，会阅读所有消息                   | 
| msgType   | 	否    | 1：设备通知，2：设备告警，3：设备故障，4：系统消息；接收的消息类型任意组合，多个类型使用英文逗号分隔	 | 
| callback  | 是     | 请求回调                                                  |

**QuecReadMsgModel属性定义**

| 参数          | 	类型                           | 说明   |
|-------------|-------------------------------|------|
| successList | List&lt;QuecReadMsgListModel> | 成功列表 |
| failureList | List&lt;QuecReadMsgListModel> | 失败列表 |

**QuecReadMsgListModel属性定义**

| 参数         | 类型     | 说明                |
|------------|--------|-------------------|
| msgID      | String | 消息ID              |
| failureMsg | String | 阅读失败原因(仅失败列表有此字段) |

**示例代码**

```kotlin
QuecUserMessageService.readMessageByMsgIdList("msgIdList", 1) {
    if (it.isSuccess) {
        val value = it.data //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 删除消息

**接口说明**

删除用户消息

```kotlin
fun deleteMessageByMsgId(msgId: String?, language: String?, callback: QuecCallback<Unit>)
```

**参数说明**

| 参数       | 	是否必传 | 说明                      |
|----------|-------|-------------------------|
| msgId    | 	是    | 阅读的消息ID列表 多个ID使用英文逗号分隔	 |
| language | 	否    | 语音	                     |
| callback | 是     | 请求回调                    |

**示例代码**

```kotlin
QuecUserMessageService.deleteMessageByMsgId("msgId", "cn") {
    if (it.isSuccess) {
        //删除成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 阅读消息

**接口说明**

通过标签标记阅读消息

```kotlin
fun readMessageByMsgIdList(
    msgIdList: String?,
    firstLabelList: String?,
    secondaryLabelList: String?,
    callback: QuecCallback<QuecReadMsgModel>
)
```

**参数说明**

| 参数                 | 	是否必传 | 说明                                                        |
|--------------------|-------|-----------------------------------------------------------|
| msgIdList          | 是     | 阅读的消息ID列表 多个ID使用英文逗号分隔,如果不传，会阅读所有消息                       |
| firstLabelList     | 是     | 1:系统 2:智能 3:设备，多个使用","拼接                                  |
| secondaryLabelList | 是     | 1:隐私 2:服务 3:自动化 4:场景 5:设备通知 6:设备告警 7:设备故障 8:ota，多个使用","拼接 |
| callback           | 是     | 请求回调                                                      |

**QuecReadMsgModel属性定义**

| 参数          | 	类型                           | 说明   |
|-------------|-------------------------------|------|
| successList | List&lt;QuecReadMsgListModel> | 成功列表 |
| failureList | List&lt;QuecReadMsgListModel> | 失败列表 |

**QuecReadMsgListModel属性定义**

| 参数         | 类型     | 说明                |
|------------|--------|-------------------|
| msgID      | String | 消息ID              |
| failureMsg | String | 阅读失败原因(仅失败列表有此字段) |

**示例代码**

```kotlin
QuecUserMessageService.readMessageByMsgIdList("msgIdList", "1", "1") {
    if (it.isSuccess) {
        val value = it.data //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 获取未读消息数

**接口说明**

获取未读消息数，可用于标记用户消息阅读状态

```kotlin
fun getMsgStats(callback: QuecCallback<QuecUserMessageStatsModel>)
```

**参数说明**

| 参数       | 	是否必传 | 说明   |
|----------|-------|------|
| callback | 是     | 请求回调 |

**QuecUserMessageStatsModel属性定义**

| 参数     | 	类型                        | 说明     |
|--------|----------------------------|--------|
| smart  | QuecUserMessageUnReadModel | 智能相关消息 |
| system | QuecUserMessageUnReadModel | 系统相关消息 |
| device | QuecUserMessageUnReadModel | 设备相关消息 |

**QuecUserMessageUnReadModel属性定义**

| 参数     | 	类型 | 说明    |
|--------|-----|-------|
| read   | int | 已读消息数 |
| unRead | int | 未读消息数 |

**示例代码**

```kotlin
QuecUserMessageService.getMsgStats {
    if (it.isSuccess) {
        val value = it.data //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 用户接收推送的消息类型

**接口说明**

获取用户接收推送的消息类型

```kotlin
fun getMsgPushSetting(callback: QuecCallback<QuecPushSettingModel>)
```

**参数说明**

| 参数       | 	是否必传 | 说明   |
|----------|-------|------|
| callback | 是     | 请求回调 |

**QuecPushSettingModel属性定义**

| 参数           | 	类型                      | 说明          |
|--------------|--------------------------|-------------|
| deviceNotify | boolean                  | 设备推送        |
| smartNotify  | boolean                  | 智能推送        |
| systemNotify | boolean                  | 系统推送        |
| device       | QuecPushSettingItemModel | 设备推送子属性设置情况 |
| smart        | QuecPushSettingItemModel | 智能推送子属性设置情况 |
| system       | QuecPushSettingItemModel | 系统推送子属性设置情况 |

**QuecPushSettingItemModel属性定义**

| 参数    | 	类型     | 说明       |
|-------|---------|----------|
| app   | boolean | 系统推送是否开启 |
| sms   | boolean | 短信推送是否开启 |
| voice | boolean | 语音推送是否开启 |

**示例代码**

```kotlin
QuecUserMessageService.getMsgPushSetting {
    if (it.isSuccess) {
        val value = it.data //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 设置用户接收推送的消息类型

**接口说明**

用户自定义设置接收推送的消息类型

```kotlin
fun setMsgPushSetting(config: QuecPushSettingModel, callback: QuecCallback<Unit>)
```

**参数说明**

| 参数       | 	是否必传 | 说明                                      |
|----------|-------|-----------------------------------------|
| config   | 是     | QuecPushSettingModel属性定义同上【用户接收推送的消息类型】 |
| callback | 是     | 请求回调                                    |

**示例代码**

```kotlin
val config = getConfig() //从云端获取配置参数
QuecUserMessageService.setMsgPushSetting(config) {
    if (it.isSuccess) {
        //设置成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```


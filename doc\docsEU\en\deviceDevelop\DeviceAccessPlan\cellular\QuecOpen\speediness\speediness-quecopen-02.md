# SDK Description

## __1. Introduction to QuecThing SDK__

QuecThing SDK provides a unified API definition for the module adaptation layer, and carries out secondary abstraction on chip basic SDK, shielding complex module technical details, which is convenient for you to get started quickly. Developers only need to implement specific business features according to module capabilities. After compilation and download, you can run QuecThing SDK on the module and connect the device to Developer Center to realize the intellectualization of the device. At the same time, in order to speed up the development efficiency, QuecThing SDK provides built-in and unified communication protocol and APIs for the module application layer to call, so developers can directly use and develop specific features without paying too much attention to the module model.


## __2. Logic Structure Diagram__

<a data-fancybox title="img" href="/en/deviceDevelop/develop/speediness/resource/QuecOpen/Speediness-QuecOpen-04.png">![img](/en/deviceDevelop/develop/speediness/resource/QuecOpen/Speediness-QuecOpen-04.png)</a>


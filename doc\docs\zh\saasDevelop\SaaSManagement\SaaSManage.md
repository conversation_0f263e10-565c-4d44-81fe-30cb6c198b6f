# SaaS管理
本文介绍SaaS开发第一步，SaaS管理。

## **前提条件**
● 用户有SaaS应用模块的操作权限

## **操作步骤**

### **1.SaaS管理**
1.登录开发者中心后，点击“SaaS开发”→"SaaS管理"，进入SaaS管理列表页。

<a data-fancybox title="img" href="/zh/guide/image2022-0706-01.png?version=1&modificationDate=1646655516000&api=v2">![img](/zh/guide/image2022-0706-01.png?version=1&modificationDate=1646655516000&api=v2)</a>

2.点击"创建SaaS"
输入一个SaaS名称，如果有必要可以输入SaaS的描述，用于更加清晰的表达该SaaS的作用或意义。

3.在"SaaS管理"页面找到创建的SaaS应用，点击该应用面板右上角的图标可编辑该应用名称和描述；也可直接点击该SaaS应用进行服务包、产品和App的授权操作。

### **2.授权服务包**
1.点击SaaS应用，进入"服务"TAB页面，点击"新增服务授权"，通过给SaaS授权服务包，可以指定SaaS只有权限访问已授权的服务包中的api。

2.创建完成后，服务列表会多出一条服务信息，可以点击操作栏对其进行查看和取消授权操作。

<a data-fancybox title="img" href="/zh/guide/image2022-0706-08.png?version=1&modificationDate=1646655516000&api=v2">![img](/zh/guide/image2022-0706-08.png?version=1&modificationDate=1646655516000&api=v2)</a>

### **3.授权产品**
1.点击SaaS应用，进入"产品"TAB页面，点击"新增产品授权"，通过给SaaS授权产品，可以指定SaaS只能操作授权后的产品。

2.创建完成后，产品列表会多出一条产品信息，可以点击操作栏对其进行取消授权操作。

<a data-fancybox title="img" href="/zh/guide/image2022-0706-09.png?version=1&modificationDate=1646655516000&api=v2">![img](/zh/guide/image2022-0706-09.png?version=1&modificationDate=1646655516000&api=v2)</a>


### **4.授权App**
1.点击SaaS应用，进入"App"TAB页面，点击"新增App授权"，通过给SaaS授权App，可以指定SaaS只能操作授权后的App。

2.创建完成后，App列表会多出一条App信息，可以点击操作栏对其进行取消授权操作。

<a data-fancybox title="img" href="/zh/guide/image2022-0706-10.png?version=1&modificationDate=1646655516000&api=v2">![img](/zh/guide/image2022-0706-10.png?version=1&modificationDate=1646655516000&api=v2)</a>

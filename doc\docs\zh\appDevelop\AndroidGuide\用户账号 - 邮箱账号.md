# 邮箱账号

本文介绍了如何使用邮箱注册账号, 登录等功能

## 功能概述

```kotlin
QuecUserService
```

### 邮箱密码登录

**接口说明**

用于用户邮箱+密码登录

```kotlin
fun loginByEmail(email: String, password: String, callback: Quec<PERSON>allback<Unit>)
```

**参数说明**

| 参数       | 	是否必传 | 说明   |	
|----------|-------|------| 
| email    | 	是    | 邮箱   | 
| password | 	是    | 密码   | 
| callback | 是     | 请求回调 |

**示例代码**

```kotlin
QuecUserService.loginByEmail("email", "password") {
    if (it.isSuccess) {
        //登录成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 邮箱密码注册

**接口说明**

用于用户邮箱+密码注册，需要获取优先验证码

```kotlin
fun registerByEmail(
    email: String,
    code: String,
    password: String,
    nationality: Int?,
    lang: Int?,
    timezone: Int?,
    callback: QuecCallback<Unit>
)
```

**参数说明**

| 参数          | 	是否必传 | 说明   |	
|-------------|-------|------| 
| email       | 	是    | 邮箱   | 
| code        | 	是    | 验证码  | 
| password    | 	是    | 密码   | 
| nationality | 	否    | 国家   | 
| lang        | 	否    | 语言   | 
| timezone    | 	否    | 时区   | 
| callback    | 是     | 请求回调 |

**示例代码**

```kotlin
QuecUserService.registerByEmail("email", "code", "password", null, null, null) {
    if (it.isSuccess) {
        //注册成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 发送邮件

**接口说明**

用于获取账号注册、密码重置、注销 验证码

```kotlin
fun sendEmail(type: Int, email: String, callback: QuecCallback<Unit>)
```

**参数说明**

| 参数       | 	是否必传 | 说明                                                    |
|----------|-------|-------------------------------------------------------|
| type     | 	是    | 类型, 1:注册验证码 2:密码重置验证码 3:关联邮箱验证码 4:删除邮箱关联验证码 5:用户注销验证码 |
| email    | 是     | 收件人邮箱                                                 |
| callback | 是     | 请求回调                                                  |

**示例代码**

```kotlin
QuecUserService.sendEmail(1, "<EMAIL>") {
    if (it.isSuccess) {
        //发送成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 验证用户发送的邮件验证码

**接口说明**

用于邮箱验证码正确性验证，需先获取验证码

```kotlin
fun validateEmailCodeByUserEmail(
    email: String, code: String, isDisabled: Int?, callback: QuecCallback<Unit>
)
```

**参数说明**

| 参数         | 	是否必传 | 说明                         |
|------------|-------|----------------------------|
| email      | 是     | 收件人邮箱                      |
| code       | 是     | 验证码                        |
| isDisabled | 否     | 验证码验证后是否失效，1：失效 2：不失效，默认 1 |
| callback   | 是     | 请求回调                       |

**示例代码**

```kotlin
QuecUserService.validateEmailCodeByUserEmail("<EMAIL>", "code", 1) {
    if (it.isSuccess) {
        //验证成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 邮箱重置密码

**接口说明**

用户通过邮箱+验证码重置密码

```kotlin
fun resetPasswordByEmail(
    code: String,
    email: String,
    internationalCode: String?,
    password: String?,
    callback: QuecCallback<Unit>
)
```

**参数说明**

| 参数                | 	是否必传 | 说明                         |
|-------------------|-------|----------------------------|
| code              | 是     | 验证码	                       |
| email             | 	是    | 邮箱	                        |
| internationalCode | 	否    | 国际代码，配合手机号码使用，默认为国内	       |
| password          | 	否    | 用户重置的密码，如果不输入默认为 12345678	 |
| callback          | 是     | 请求回调                       |

**示例代码**

```kotlin
QuecUserService.resetPasswordByEmail("code", "<EMAIL>", "86", "newPwd") {
    if (it.isSuccess) {
        //重置成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 邮箱是否已注册

**接口说明**

查询邮箱是否已注册

```kotlin
fun checkEmailRegister(email: String, callback: QuecCallback<Boolean>)
```

**参数说明**

| 参数       | 	是否必传 | 说明   |
|----------|-------|------|
| email    | 是     | 邮箱   |
| callback | 是     | 请求回调 |

**示例代码**

```kotlin
QuecUserService.checkEmailRegister("<EMAIL>") {
    if (it.isSuccess) {
        val value = it.data //查询成功, true: 已注册, false: 未注册
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

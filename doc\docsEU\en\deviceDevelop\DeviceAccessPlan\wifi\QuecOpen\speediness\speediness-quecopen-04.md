# __Network Provisioning on App__

## **Precondition**
Download WonderFree and register an account.

## **Procedure**

1. Log in to QuecOne with your account. On the home page, click "**⊕**" in the upper right corner and select "**Detect Nearby Devices**" in the pop-up.

<a data-fancybox title="img" href="/en/deviceDevelop/develop/app/Example-app-1.png">
<img src="/en/deviceDevelop/develop/app/Example-app-1.png" style="width: 30%" /></a>

2. After you enter the "Detect Nearby Devices" page, the app automatically searches for the Bluetooth devices nearby, and the detected Bluetooth devices are displayed on the page. Click "**⊕**" behind the device to start network provisioning.

<a data-fancybox title="img" href="/en/deviceDevelop/develop/app/Example-app-2.png">
<img src="/en/deviceDevelop/develop/app/Example-app-2.png" style="width: 30%" /></a>

3. Enter the Wi-Fi name and password of the current network environment (only 2.4 GHz Wi-Fi is supported now).

<a data-fancybox title="img" href="/en/deviceDevelop/develop/app/Example-app-3.png">
<img src="/en/deviceDevelop/develop/app/Example-app-3.png" style="width: 30%" /></a>

4. Please wait patiently during network provisioning.

<a data-fancybox title="img" href="/en/deviceDevelop/develop/app/Example-app-4.png">
<img src="/en/deviceDevelop/develop/app/Example-app-4.png" style="width: 30%" /></a>

5. After the network provisioning, the successfully added device will be displayed in the device list on the home page, as shown below.

<a data-fancybox title="img" href="/en/deviceDevelop/develop/app/Example-app-5.png">
<img src="/en/deviceDevelop/develop/app/Example-app-5.png" style="width: 30%" /></a>

6. At the same time, the device status changes to "Online" on Acceleronix Developer Center.

<a data-fancybox title="img" href="/en/deviceDevelop/develop/app/Example-app-7.png">
<img src="/en/deviceDevelop/develop/app/Example-app-7.png" /></a>


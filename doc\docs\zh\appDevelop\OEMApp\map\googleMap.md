# 配置Google地图

### 申请Google的API key，设置您的 Google Cloud 项

##### 使用谷歌账号登录谷歌地图开发者平台（登录网站：[https://console.cloud.google.com/welcome?hl=zh-hans](https://console.cloud.google.com/welcome?hl=zh-hans)）

##### 开通免费试用

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/map/googlemap102.png">![image](/zh/appDevelop/oemapp/map/googlemap102.png)</a>

##### 创建一个新的项目

点击【选择项目】 -> 【新建项目】 -> 输入项目名称

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/map/googlemap103.png">![image](/zh/appDevelop/oemapp/map/googlemap103.png)</a>

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/map/googlemap104.png">![image](/zh/appDevelop/oemapp/map/googlemap104.png)</a>

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/map/googlemap105.png">![image](/zh/appDevelop/oemapp/map/googlemap105.png)</a>

##### 选择创建的项目，进入 API 库

点击 Google Cloud 左侧的菜单栏，点击 【API 和服务】，点击【库】，进入 API 库

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/map/googlemap106.png">![image](/zh/appDevelop/oemapp/map/googlemap106.png)</a>

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/map/googlemap107.png">![image](/zh/appDevelop/oemapp/map/googlemap107.png)</a>

##### 将所需地图 API 库全部启用

例如，我们开启库 **Maps SDK for Android**，操作如下

1. 点击 **Maps SDK for Android**进入详情页；
2. 点击 **启用**按钮，开启完成之后会显示 **API 已启用**标签；

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/map/googlemap108.png">![image](/zh/appDevelop/oemapp/map/googlemap108.png)</a>

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/map/googlemap109.png">![image](/zh/appDevelop/oemapp/map/googlemap109.png)</a>

在**Google Maps Platform** 查看已开启的 API，总共需要开启的 API 如下

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/map/googlemap110.png">![image](/zh/appDevelop/oemapp/map/googlemap110.png)</a>

##### API 全部开启后，我们需要生成 API 密钥

1. 点击 Google Cloud 左侧的菜单栏，点击 【API 和服务】，点击【凭据】，进入凭据详情页；
2. 点击【+创建凭据】按钮，选择 【API 密钥】创建；

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/map/googlemap111.png">![image](/zh/appDevelop/oemapp/map/googlemap111.png)</a>

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/map/googlemap112.png">![image](/zh/appDevelop/oemapp/map/googlemap112.png)</a>

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/map/googlemap113.png">![image](/zh/appDevelop/oemapp/map/googlemap113.png)</a>

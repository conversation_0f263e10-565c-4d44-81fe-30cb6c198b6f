# 获取iOS应用在App Store内的下载链接

## 获取iOS应用在App Store内的下载链接有两种方式：
方式一：如果您可以登录App Store Connect，登录后点击对应的App，页面显示“**App 信息**” - “**额外信息**”，点击“**在App Store中查看**”，点击后会跳转出新页面，新页面的网址即为App Store下载链接。  
<a data-fancybox title="img" href="/zh/appDevelop/oemapp/appstore/store37.png">![image](/zh/appDevelop/oemapp/appstore/store37.png)</a>

方式二：如果您没有办法登录App Store Connect，可在手机App Store内搜索对应App，进入App介绍详情页后,点击右侧分享图标，在弹窗中选择“**拷贝链接**”，此链接即为该App在App Store内的下载链接。  
<a data-fancybox title="img" href="/zh/appDevelop/oemapp/appstore/store38.png">![image](/zh/appDevelop/oemapp/appstore/store38.png)</a>

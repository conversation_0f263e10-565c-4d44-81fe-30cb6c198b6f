---
title: "房间信息"
description: "房间信息相关文档"
---

# 房间信息

## 功能概述

本文介绍了如何管理家庭下房间信息, 包括创建房间, 修改房间名称, 删除房间, 移入设备到房间, 设置房间排序, 房间中设备列表等。

## 房间管理

### 创建房间

**接口说明**

创建家庭下房间

```objc
- (void)addFamilyRoomWithFid:(NSString *)fid
                    roomName:(NSString *)roomName
                     success:(QuecVoidBlock)success
                     failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- | 
| fid |	是|家庭id| 
| roomName |	是|房间名称| 
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**示例代码**

```objc
[QuecSmartHomeService.sharedInstance addFamilyRoomWithFid:@"your fid" roomName:@"room name" success:^{
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 修改房间名称

**接口说明**

修改房间名称

```objc
- (void)setFamilyRoomWithFrid:(NSString *)frid
                     roomName:(NSString *)roomName
                      success:(QuecVoidBlock)success
                      failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- | 
| frid |	是|家庭房间id| 
| roomName |	是|房间名称| 
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**示例代码**

```objc
[QuecSmartHomeService.sharedInstance setFamilyRoomWithFrid:@"your frid" roomName:@"room name" success:^{
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 删除房间

**接口说明**

删除房间, 支持批量删除

```objc
- (void)deleteFamilyRoomsWithIds:(NSArray<NSString *> *)fridList
                         success:(QuecVoidBlock)success
                         failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- | 
| fridList |	是|房间id列表| 
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**示例代码**

```objc
[QuecSmartHomeService.sharedInstance deleteFamilyRoomsWithIds:@[@"room id1", @"room id2"] success:^{
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```


### 移入设备到房间

**接口说明**

移入设备到房间, 支持批量添加

```objc
- (void)addDeviceInFamilyRoomWithDeviceList:(NSArray<QuecAddDeviceEnterModel *> *)deviceList
                                    success:(void(^)(QuecFamilyAddDeviceModel *model))success
                                    failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- | 
| deviceList |	是|设备列表| 
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**QuecAddDeviceEnterModel属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| pk       | NSString             | 产品pk |
| dk       | NSString             | 设备dk |
| oldFrid       | NSString             | 原房间号 |
| nFrid       | NSString             | 新房间号 |

**QuecFamilyAddDeviceModel属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| successList       | NSArray<QuecFamilyAddDeviceListModel *> *             | 执行成功列表 |
| failureList       | NSArray<QuecFamilyAddDeviceListModel *> *             | 执行失败列表 |

**QuecFamilyAddDeviceListModel属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| oldFrid       | NSString             | 原房间号 |
| nFrid       | NSString             | 新房间号 |
| pk       | NSString             | 产品pk |
| dk       | NSString             | 设备dk |
| NSInteger       | NSInteger             | 错误码（仅failureList有此属性） |
| NSString       | NSString             | 错误提示文案 （仅failureList有此属性） |

**示例代码**

```objc
QuecAddDeviceEnterModel *addEnterModel = QuecAddDeviceEnterModel.new;
addEnterModel.pk = @"pk";
addEnterModel.dk = @"dk";
addEnterModel.oldFrid = @"old frid";
addEnterModel.nFrid = @"new frid";
[QuecSmartHomeService.sharedInstance addDeviceInFamilyRoomWithDeviceList:@[addEnterModel] success:^(QuecFamilyAddDeviceModel *model) {
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 设置房间排序

**接口说明**

设置房间排序, 设置房间显示优先级

```objc
- (void)setFamilyRoomSortWithRoomRortList:(NSArray<QuecSortDeviceEnterModel *> *)roomSortList
                                  success:(QuecVoidBlock)success
                                  failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- | 
| roomSortList |	是|待排序房间数组| 
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**QuecSortDeviceEnterModel属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| frid       | NSString             | 家庭房间ID |
| roomSort       | NSString             | 房间顺序，从 0 开始累加，数字小的排列在列表前面，可以不连续，不允许相等 |

**示例代码**

```objc
QuecSortDeviceEnterModel *sort0 = QuecSortDeviceEnterModel.new;
sort0.roomSort = @"0";
sort0.frid = @"first frid";
QuecSortDeviceEnterModel *sort1 = QuecSortDeviceEnterModel.new;
sort1.roomSort = @"1";
sort1.frid = @"second frid";
[QuecSmartHomeService.sharedInstance setFamilyRoomSortWithRoomRortList:@[sort0, sort1]
                                                                success:^{
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 房间中设备列表

**接口说明**

查询房间中设备列表

```objc
- (void)getFamilyRoomDeviceListWithFrid:(NSString *)frid 
                             pageNumber:(NSInteger)pageNumber
                               pageSize:(NSInteger)pageSize
                      isGroupDeviceShow:(BOOL)isGroupDeviceShow
                                success:(void(^)(NSArray <QuecDeviceModel *> *list, NSInteger total))success 
                                failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- | 
| frid |	是|家庭房间id| 
| pageNumber |	否|页码，非必填，默认1| 
| pageSize |	否|页大小，非必填，默认10| 
| isGroupDeviceShow |	否|是否显示群组设备，默认缺省| 
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

>QuecDeviceModel属性定义同设备SDK说明

**示例代码**

```objc
[QuecSmartHomeService.sharedInstance getFamilyRoomDeviceListWithFrid:@"your frid"
                                                              pageNumber:1
                                                                pageSize:10
                                                       isGroupDeviceShow:YES
                                                                 success:^(NSArray<QuecDeviceModel *> *list, NSInteger total) {
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 家庭下的房间列表

**接口说明**

查询家庭中的房间列表

```objc
- (void)getFamilyRoomListWithFid:(NSString *)fid
                      pageNumber:(NSInteger)pageNumber
                        pageSize:(NSInteger)pageSize
                         success:(void(^)(NSArray <QuecFamilyRoomItemModel *> *list, NSInteger total))success
                         failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- | 
| fid |	是|家庭id| 
| pageNumber |	否|页码，非必填，默认1| 
| pageSize |	否|页大小，非必填，默认10| 
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

>QuecFamilyRoomItemModel属性定义同上

**示例代码**

```objc
[QuecSmartHomeService.sharedInstance getFamilyRoomListWithFid:@"your fid"
                                                       pageNumber:1
                                                         pageSize:10
                                                          success:^(NSArray<QuecFamilyRoomItemModel *> *list, NSInteger total) {
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```


### 编辑设备信息

**接口说明**

编辑设备信息, 设置房间信息 是否常用等

```objc
- (void)setDeviceInfoWithModelArray:(NSArray<QuecSetDeviceInfoModel *> *)modelArray
                            success:(void(^)(QuecOperateDeviceRespModel *model))success
                            failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- | 
| modelArray |	否|待设置房间信息设备列表| 
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**QuecSetDeviceInfoModel属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| fid       | NSString             | 家庭Id |
| dk       | NSString             | 设备dk |
| pk       | NSString             | 产品pk |
| deviceName       | NSString             | 设备名称 |
| isCommonUsed       | BOOL             | 是否常用：true-常用，false-不是常用 |
| type       | int             | 设备类型：1-家庭中的设备，2-用户接收分享的设备，3-用户多绑模式的设备 |
| oldFrid       | NSString             | 移出房间ID |
| selectFrid       | NSString             | 移入房间ID |
| shareCode       | NSString             | 分享码 |

>QuecOperateDeviceRespModel属性定义同上

**示例代码**

```objc
QuecSetDeviceInfoModel *setInfoModel = QuecSetDeviceInfoModel.new;
setInfoModel.fid = @"your fid";
setInfoModel.dk = @"dk";
setInfoModel.pk = @"pk";
setInfoModel.deviceName = @"device name";
setInfoModel.isCommonUsed = YES;
setInfoModel.type = 3;
setInfoModel.oldFrid = @"old frid"; /// can be null
setInfoModel.selectFrid = @"select frid";
[QuecSmartHomeService.sharedInstance setDeviceInfoWithModelArray:@[setInfoModel]
                                                            success:^(QuecOperateDeviceRespModel *model) {
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

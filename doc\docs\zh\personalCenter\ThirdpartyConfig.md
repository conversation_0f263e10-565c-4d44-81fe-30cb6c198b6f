# 第三方配置

本文介绍第三方配置功能，包含短信配置、邮件配置与公众号配置。

## **短信配置**

短信配置主要用于配置App用户在进行手机号注册、手机验证码登录、手机忘记密码等操作时收到的验证码短信签名与模板。

### **操作步骤**

1. 点击短信账号配置“新增”按钮，填入阿里云的AccessKey与AccessSecret，并为短信账号输入一个账号名称，完成账号添加。
2. 点击“设为生效”按钮，把需要使用的短信账号置为生效状态，仅有一个账号可设为生效状态。
3. 设置完成后，可在下方短信模板配置中对各个使用场景配置短信签名与短信模板。
4. 除了默认签名与默认模板外，您还可以对每个App进行个性化签名与模板配置。
5. 在配置短信签名与短信模板前，请确保已经在阿里云短信平台完成内容审核操作，否则将无法添加或使用。



## **电话配置**

电话配置主要用于配置App用户在接收电话告警时收到的语音模板。

### **操作步骤**

1. 点击电话配置“新增”按钮，填入阿里云的AccessKey与AccessSecret，并为电话账号输入一个账号名称，完成账号添加。
2. 点击“设为生效”按钮，把需要使用的电话账号置为生效状态，仅有一个账号可设为生效状态。
3. 设置完成后，可在下方语音模板配置中对各个使用场景配置语音模板。
5. 在配置语音模板前，请确保已经在阿里云电话平台完成内容审核操作，否则将无法添加或使用。




## **邮件配置**

邮件配置主要用于配置App用户在进行邮箱账号注册、邮箱验证码登录、邮箱忘记密码等操作时收到的验证码邮件模板。

### **操作步骤**

1. 点击邮件账号配置“新增”按钮，填入自己的邮箱账户名称、smtp服务器地址/端口、smtp账户/密码、是否启用加密。
2. 点击“设为生效”按钮，把需要使用的邮件账号置为生效状态，仅有一个账号可设为生效状态。
3. 设置完成后，可在下方邮件模板配置中对各个使用场景配置邮件模板。
4. 除了默认模板外，您还可以对每个App进行个性化模板配置。
5. 模板内容需使用html格式编辑，您可通过参考示例查看模板模板，其中验证码模板需包含验证码变量${code}。



## **公众号配置**

公众号配置主要用于配置小程序用户需要通过公众号接收设备告警消息时所使用的推送账号。

### **操作步骤**

1. 点击“新增”按钮，输入公众号应用ID与应用密钥，仅当ID与密钥校验通过方可添加成功；
2. 添加成功后，在添加公众号推送规则时，可选择该账号下已审核通过的消息模板，并对模板下的变量参数进行赋值配置；
3. 已添加成功的公众号信息不可修改，若删除公众号，已配置的公众号推送规则将同步失效。

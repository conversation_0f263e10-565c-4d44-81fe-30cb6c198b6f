<!--
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-11-15 15:09:29
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2024-12-25 13:52:45
 * @FilePath: \quec-doc-web\docsEU\en\massProduct\process.md
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
# Mass Production Process

Once the product has been successfully developed and debugged, it can proceed to the trial and mass production stages. Please follow the steps outlined below.

## **Mass Production Process**

<a data-fancybox title="img" href="/en/massProduct/image2022-3-14_14-34-56.png?version=1&modificationDate=*************&api=v2">![img](/en/massProduct/image2022-3-14_14-34-56.png?version=1&modificationDate=*************&api=v2)</a>

## **Preparations**

**1.Release the product:**<br />
● After the device is developed successfully, it is recommended to release the product to prevent the produced devices from being unusable due to the modification of TSL model.  

**2.Purchase activation codes:**<br />
● We provide 10 free activation codes for each user under enterprise accounts for product development purposes. Once the device activation codes are used up, you will need to purchase new activation codes if your newly produced devices need to access Developer Center.  <br />
● You can contact our sales staff to consult and purchase activation codes. For details, please see [**【Mass Production】 - 【Product Activation Code】**](/license/LicenseIntroduction).

**3.Purchase a module:**<br />
● Offline Purchase (**Recommended**): You can contact our sales staff or contact us at <<EMAIL>> to purchase the module.<br />
● Online Purchase: You can purchase the module from [Quectel's Tmall Flagship Store](https://yiyuanznsb.tmall.com/shop/view_shop.htm).

**4.Download the firmware:**<br />
● It is recommended that you confirm the required firmware information when purchasing the module, and Quectel will download the firmware into the module before shipping, so that you will not need to download the firmware after receiving the module.

## **Trial Production Process**

In this stage, a small batch of products is produced to test the manufacturing process and ensure that the product meets the required quality standards. The trial production stage allows for any issues to be identified and corrected before proceeding to mass production. We suggest you verify the software and hardware functions and stability of the products with the mobile terminal and the back-end terminal.

## **Mass Production**

After the function verification is passed in trial production, mass production can be carried out.

If cellular products are provided to App users, the QR codes of the devices must be generated for device binding. See If cellular products are provided to App users, the QR codes of the devices needs to be generated for device binding. See [Device QR Code](/massProduct/QRcode).

## **Activate Device**

After the devices are produced, it is recommended to activate the devices on the production line to avoid device failures caused by license insufficiency or other reasons after delivery. 

# 家庭信息

## 功能概述

本文介绍了如何对家庭进行管理, 包括创建家庭, 修改家庭信息, 删除家庭, 获取家庭列表等

## 通用信息

### QuecSmartHomeService类初始化

```objc
+ (instancetype)sharedInstance;
```

**QuecSmartHomeService类属性说明**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| enable       | BOOL             | 家庭模式是否开启    |
| autoSwitch  | BOOL            | 自动切换开关状态 |
| currentFamily | QuecFamilyItemModel | 用户当前家庭信息    |
| familys | NSArray <QuecFamilyItemModel *> * | 用户所有家庭数据列表    |
| familyInviteList | NSArray <QuecInviteItemModel *> * | 用户被邀请家庭列表    |

例如您想获取当前家庭信息, 可通过以下示例代码获取
```objc
QuecFamilyItemModel *currentFamily = QuecSmartHomeService.sharedInstance.currentFamily;
```

**QuecFamilyItemModel属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| fid       | NSString             | 家庭Id    |
| familyName  | NSString            | 名称 |
| familyLocation | NSString | 家庭位置    |
| familyCoordinates | NSString | 家庭经纬度    |
| addTime | NSString | 添加时间    |
| addTimeTs | NSInteger | 添加时间戳    |
| memberRole       | NSInteger             | 角色,1-创建者  2-管理员  3-普通成员    |
| currentRoom  | QuecFamilyRoomItemModel            | 当前家庭下选中的房间信息 |
| rooms | NSArray<QuecFamilyRoomItemModel *> * | 当前家庭下所有房间列表   |
| groupDeviceDeviceNum | NSInteger | 群组包含设备数量    |
| deviceList | NSArray<QuecGroupDeviceBean *> * | 设备列表    |

**QuecFamilyRoomItemModel属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| frid       | NSString             | 房间ID    |
| roomName  | NSString            | 房间名称 |
| roomSort | NSString | 房间排序    |

**QuecInviteItemModel属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| fid       | NSString             | 家庭Id    |
| familyName  | NSString            | 名称 |
| familyLocation | NSString | 家庭位置    |
| familyCoordinates | NSString | 家庭经纬度    |
| addTime | NSString | 添加时间    |
| addTimeTs | NSInteger | 添加时间戳    |
| invalidTime | NSString | 邀请时间    |

清除当前家庭缓存数据

```objc
- (void)clearFamilyInfos;
```

## 家庭模式

### 家居模式开关

**接口说明**

启用停用家居模式, 开启后用户将处于家居模式

```objc
- (void)enabledFamilyMode:(BOOL)familyMode success:(QuecVoidBlock)success failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- | 
| familyMode |	是|启用停用家居模式| 
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**示例代码**

```objc
[QuecSmartHomeService.sharedInstance enabledFamilyMode:YES success:^{
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 自动切换开关

**接口说明**

启用自动切换, 开启家居模式后设置. 根据位置自动切换连接家庭

```objc
- (void)enabledAutoSwitch:(BOOL)autoSwitch success:(QuecVoidBlock)success failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- | 
| autoSwitch |	是|启用停用自动切换| 
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**示例代码**

```objc
[QuecSmartHomeService.sharedInstance enabledAutoSwitch:YES success:^{
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```


### 获取家居模式配置

**接口说明**

查询用户的家居模式配置, 可用于判断当前家居模式是否开启

```objc
- (void)getFamilyModeConfigWithSuccess:(void(^)(QuecFamilyModeConfigModel *model))success failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- | 
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**QuecFamilyModeConfigModel属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| enabledAutoSwitch       | BOOL             | 自动切换是否开启    |
| enabledFamilyMode  | BOOL            | 家庭模式是否开启 |

**示例代码**

```objc
[QuecSmartHomeService.sharedInstance getFamilyModeConfigWithSuccess:^(QuecFamilyModeConfigModel *model) {
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

## 家庭管理

### 创建家庭

**接口说明**

创建家庭

```objc
- (void)addFamilyWithFamilyParamModel:(QuecFamilyParamModel *)familyParamModel success:(QuecVoidBlock)success failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- | 
| familyParamModel |	是|家庭信息| 
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**QuecFamilyParamModel属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| fid       | NSString             | 家庭Id,更改家庭信息和删除家庭时必传，创建家庭不用传    |
| familyName  | NSString            | 名称，创建家庭时该参数必传，更改家庭非必传 |
| familyLocation  | NSString            | 家庭位置，非必传 |
| familyCoordinates  | NSString            | 家庭经纬度，WGS84坐标系，格式：40.759186,-73.928204, 非必传 |
| familyRoomList  | NSArray<NSString *> *       | 房间列表,非必传 |


**示例代码**

```objc
QuecFamilyParamModel *paramModel = QuecFamilyParamModel.new;
paramModel.familyName = @"My Smart Family";
[QuecSmartHomeService.sharedInstance addFamilyWithFamilyParamModel:paramModel success:^{
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 修改家庭信息

**接口说明**

修改家庭信息

```objc
- (void)setFamilyWithFamilyParamModel:(QuecFamilyParamModel *)familyParamModel success:(QuecVoidBlock)success failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- | 
| familyParamModel |	是|家庭信息| 
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

> QuecFamilyParamModel属性定义同上

**示例代码**

```objc
QuecFamilyParamModel *paramModel = QuecFamilyParamModel.new;
paramModel.fid = @"my fid";
paramModel.familyName = @"My Smart Family";
[QuecSmartHomeService.sharedInstance setFamilyWithFamilyParamModel:paramModel success:^{
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 删除家庭

**接口说明**

删除家庭

```objc
- (void)deleteFamilyWithFid:(NSString *)fid success:(QuecVoidBlock)success failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- | 
| fid |	是|家庭id| 
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**示例代码**

```objc
[QuecSmartHomeService.sharedInstance deleteFamilyWithFid:@"your fid" success:^{
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 查询当前家庭

**接口说明**

查询当前家庭

```objc
- (void)getCurrentFamilyWithFid:(NSString *)fid
             currentCoordinates:(NSString *)currentCoordinates
                        success:(void(^)(QuecFamilyItemModel *))success
                        failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- | 
| fid |	是|家庭id|  
| currentCoordinates |	否|当前GPS定位坐标，WGS84坐标系，格式：40.759186,-73.928204，非必传| 
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

>QuecFamilyItemModel属性定义同上

**示例代码**

```objc
[QuecSmartHomeService.sharedInstance getCurrentFamilyWithFid:@"your fid"
                                              currentCoordinates:@""
                                                         success:^(QuecFamilyItemModel *itemModel) {
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 家庭列表

**接口说明**

查询家庭列表

```objc
- (void)getFamilyListWithRole:(NSString *)role
                   pageNumber:(NSInteger)pageNumber
                     pageSize:(NSInteger)pageSize
                      success:(void(^)(NSArray <QuecFamilyItemModel *> *list, NSInteger total))success
                      failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- | 
| role |	否|角色，成员角色：1-创建者  2-管理员  3-普通成员，多个使用英文逗号分隔，非必填| 
| pageNumber |	是|页码，非必填，默认1| 
| pageSize |	是|页大小，非必填，默认10| 
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

>QuecFamilyItemModel属性定义同上

**示例代码**

```objc
[QuecSmartHomeService.sharedInstance getFamilyListWithRole:@"1"
                                                    pageNumber:1
                                                      pageSize:10
                                                       success:^(NSArray<QuecFamilyItemModel *> *list, NSInteger total) {
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 家庭中设备列表

**接口说明**

查询家庭中设备列表

```objc
- (void)getFamilyDeviceListWithModel:(QuecFamilyDeviceListParamsModel *)params
                             success:(void(^)(NSArray <QuecDeviceModel *> *list, NSInteger total))success
                             failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- | 
| params |	是|参数模型| 
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**QuecFamilyDeviceListParamsModel属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| fid       | NSString             | 家庭Id |
| isAddOwnerDevice  | BOOL            | 是否加上自己的所有设备，非必填 |
| deviceName  | NSString            | 设备名称搜索, 非必须 |
| pageNumber  | NSInteger            | 页码，非必填，默认1 |
| pageSize  | NSInteger            | 页大小，非必填，默认10 |
| isGroupDeviceShow  | BOOL            | 是否显示群组设备，默认缺省 |
| isAssociation  | BOOL            | 查询未被关联的设备, 默认 false |
| secondItemCode  | NSString            | 二级品类过滤, 默认为空 |
| pkList  | NSString            | 增加pklist, 多pk用英文逗号隔开 |


**示例代码**

```objc
QuecFamilyDeviceListParamsModel *paramsModel = QuecFamilyDeviceListParamsModel.new;
paramsModel.fid = @"your fid";
paramsModel.isAddOwnerDevice = YES;
paramsModel.isGroupDeviceShow = YES;
paramsModel.pageSize = 10;
paramsModel.pageNumber = 1;
[QuecSmartHomeService.sharedInstance getFamilyDeviceListWithModel:paramsModel
                                                            success:^(NSArray<QuecDeviceModel *> *list, NSInteger total) {
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

## 常用设备

### 添加常用设备

**接口说明**

添加常用设备, 支持批量添加

```objc
- (void)addCommonUsedDeviceWithFid:(NSString *)fid
                        deviceList:(NSArray<QuecDeviceEnterModel *> *)deviceList
                           success:(void(^)(QuecOperateDeviceRespModel *respModel))success
                           failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- | 
| fid |	是|家庭id| 
| deviceList |	是|设备列表| 
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**QuecDeviceEnterModel属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| pk       | NSString             | 产品pk |
| dk       | NSString             | 设备dk |
| type       | NSUInteger             | 添加或移除常用设备类型 <br />1-家庭中的设备 <br />2-用户自己的设备，来自接收分享的设备 <br />3-用户自己的设备，来自自己多绑模式的设备 |

>QuecOperateDeviceRespModel为批量操作通用返回类, 可根据实际返回参取需要用得到的属性

**QuecOperateDeviceRespModel属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| successList       | QuecOperateDeviceRespItem             | 执行成功列表 |
| failureList       | QuecOperateDeviceRespItem             | 执行失败列表 |

**QuecOperateDeviceRespItem属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| fid       | NSString             | 家庭id |
| oldFrid       | NSString             | 原房间号 |
| nFrid       | NSString             | 新房间号 |
| pk       | NSString             | 产品pk |
| dk       | NSString             | 设备dk |
| deviceName       | NSString             | 设备名 |
| shareCode       | NSString             | 分享码 |
| isCommonUsed       | BOOL             | 是否常用：true-常用，false-不是常用 |
| msg       | NSString             | 错误提示文案 （仅failureList有此属性） |

**示例代码**

```objc
QuecDeviceEnterModel *enterModel = QuecDeviceEnterModel.new;
enterModel.pk = @"pk";
enterModel.dk = @"dk";
enterModel.type = 3;
[QuecSmartHomeService.sharedInstance addCommonUsedDeviceWithFid:@"your fid" deviceList:@[enterModel] success:^(QuecOperateDeviceRespModel *respModel) {
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 移除常用设备

**接口说明**

移除常用设备, 支持批量移除

```objc
- (void)deleteCommonUsedDeviceWithFid:(NSString *)fid
                        deviceList:(NSArray<QuecDeviceEnterModel *> *)deviceList
                           success:(void(^)(QuecOperateDeviceRespModel *respModel))success
                           failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- | 
| fid |	是|家庭id| 
| deviceList |	是|设备列表| 
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

>QuecDeviceEnterModel QuecOperateDeviceRespModel属性定义同上

**示例代码**

```objc
QuecDeviceEnterModel *enterModel = QuecDeviceEnterModel.new;
enterModel.pk = @"pk";
enterModel.dk = @"dk";
enterModel.type = 3;
[QuecSmartHomeService.sharedInstance deleteCommonUsedDeviceWithFid:@"your fid" deviceList:@[enterModel] success:^(QuecOperateDeviceRespModel *respModel) {
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 常用设备列表

**接口说明**

查询常用设备列表

```objc
- (void)getCommonUsedDeviceListWithFid:(NSString *)fid 
                            pageNumber:(NSInteger)pageNumber
                              pageSize:(NSInteger)pageSize
                     isGroupDeviceShow:(BOOL)isGroupDeviceShow
                               success:(void(^)(NSArray <QuecDeviceModel *> *list, NSInteger total))success
                               failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- | 
| fid |	是|家庭id| 
| pageNumber |	否|页码，非必填，默认1| 
| pageSize |	否|页大小，非必填，默认10| 
| isGroupDeviceShow |	否|是否显示群组设备，默认缺省| 
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

>QuecDeviceModel属性定义同设备SDK说明

**示例代码**

```objc
[QuecSmartHomeService.sharedInstance getCommonUsedDeviceListWithFid:@"your fid" pageNumber:1 pageSize:10 isGroupDeviceShow:YES success:^(NSArray<QuecDeviceModel *> *list, NSInteger total) {
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

## 设备组

### 家庭设备组列表

**接口说明**

查询家庭设备组列表

```objc
- (void)getFamilyGroupListWithFid:(NSString *)fid
                       pageNumber:(NSInteger)pageNumber
                         pageSize:(NSInteger)pageSize
                          success:(void(^)(NSArray <QuecFamilyDeviceGroupInfoModel *> *list, NSInteger total))success
                          failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- | 
| fid |	是|家庭ID| 
| pageNumber |	否|页码，非必填，默认1| 
| pageSize |	否|页大小，非必填，默认10| 
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**QuecFamilyDeviceGroupInfoModel属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| name       | NSString             | 名称 |
| fid       | NSString             | 家庭ID，创建分组时该字段必传 |
| address       | NSString             | 地址 |
| contactPhoneList       | NSString             | 联系人 |
| coordinate       | NSString             | 经纬度 |
| coordinateSystem       | NSString             | 坐标系 |
| descrip       | NSString             | 说明 |
| manager       | NSString             | 管理员 |
| managerType       | NSString             | 管理员类型 |
| parentId       | NSString             | 父设备组ID |
| extend       | NSString             | 拓展字段 |
| dgid       | NSString             | 分组ID |
| owner       | NSString             | 拥有者 |
| addTime       | NSString             | 添加时间 |
| addTimeTs       | NSInteger             | 添加时间戳 |

**示例代码**

```objc
[QuecSmartHomeService.sharedInstance getFamilyGroupListWithFid:@"your fid"
                                                         pageNumber:1
                                                           pageSize:10
                                                            success:^(NSArray<QuecFamilyDeviceGroupInfoModel *> *list, NSInteger total) {
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 添加家庭设备分组

**接口说明**

添加家庭设备分组

```objc
- (void)addFamilyDeviceGroupWithInfo:(QuecFamilyDeviceGroupModel *)groupInfoModel
                             success:(QuecVoidBlock)success
                             failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- | 
| groupInfoModel |	是|分组信息| 
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**QuecFamilyDeviceGroupModel属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| name       | NSString             | 名称 |
| fid       | NSString             | 家庭ID，创建分组时该字段必传 |
| address       | NSString             | 地址 |
| contactPhoneList       | NSString             | 联系人 |
| coordinate       | NSString             | 经纬度 |
| coordinateSystem       | NSString             | 坐标系 |
| descrip       | NSString             | 说明 |
| manager       | NSString             | 管理员 |
| managerType       | NSString             | 管理员类型 |
| parentId       | NSString             | 父设备组ID |
| extend       | NSString             | 拓展字段 |

**示例代码**

```objc
QuecFamilyDeviceGroupModel *groupModel = QuecFamilyDeviceGroupModel.new;
groupModel.name = @"group name";
groupModel.fid = @"your fid";
[QuecSmartHomeService.sharedInstance addFamilyDeviceGroupWithInfo:groupModel
                                                              success:^{
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 查询不在家庭设备组内的设备列表

**接口说明**

查询不在家庭设备组内的设备列表

```objc
- (void)getDeviceListByNotInDeviceGroupWithFid:(NSString *)fid
                                    PageNumber:(NSInteger)pageNumber
                                      pageSize:(NSInteger)pageSize
                                       groupId:(NSString *)groupId
                                       success:(void(^)(NSArray <QuecDeviceModel *> *list, NSInteger total))success
                                       failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- | 
| fid |	否|家庭ID| 
| pageNumber |	否|页码，非必填，默认1| 
| pageSize |	否|页大小，非必填，默认10| 
| groupId |	是|页设备组id| 
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

>QuecDeviceModel属性定义同设备SDK说明

**示例代码**

```objc
[QuecSmartHomeService.sharedInstance getDeviceListByNotInDeviceGroupWithFid:@"your fid"
                                                                     PageNumber:1
                                                                       pageSize:10
                                                                        groupId:@"your groupid"
                                                                        success:^(NSArray<QuecDeviceModel *> *list, NSInteger total) {
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```


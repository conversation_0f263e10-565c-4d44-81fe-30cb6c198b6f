# 设备分组

## 功能概述

设备分组是指将多个设备归为一组，便于管理，比如将多个设备归为一组，便于进行批量控制、批量查询等操作。

> QuecDeviceModel属性定义参考[设备管理](设备管理.md)

```objc
#import <QuecDeviceKit/QuecDeviceKit.h>
/// 初始化
[QuecDeviceGroupService sharedInstance]
```

## 设备分组

### 分组列表

**接口说明**

获取分组列表, 可先调用添加分组接口后获取分组列表数据

```objc
- (void)getDeviceGroupListWithPageNumber:(NSInteger)pageNumber
                                  pageSize:(NSInteger)pageSize
                                     extra:(QuecDeviceGroupParamModel * _Nullable)infoModel
                                   success:(void(^)(NSArray<QuecDeviceGroupInfoModel *> *list, NSInteger total))success
                                   failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- |  
| pageNumber | 是 | 页码 |
| pageSize | 是 | 页数据数量 |
| infoModel | 否 | 分组拓展信息 |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**QuecDeviceGroupParamModel属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| name  | NSString | 名称，创建分组时该字段必传 |
| address  | NSString  | 地址 |
| contactPhoneList  | NSString  | 联系人 |
| coordinate  | NSString  | 经纬度 |
| coordinateSystem  | NSString  | 坐标系 |
| descrip  | NSString  | 说明 |
| manager  | NSString  | 管理员 |
| managerType  | NSString  | 管理员类型 |
| parentId  | NSString  | 父设备组ID |
| extend  | NSString  | 拓展字段 |

**QuecDeviceGroupInfoModel属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| name  | NSString | 名称，创建分组时该字段必传 |
| address  | NSString  | 地址 |
| contactPhoneList  | NSString  | 联系人 |
| coordinate  | NSString  | 经纬度 |
| coordinateSystem  | NSString  | 坐标系 |
| descrip  | NSString  | 说明 |
| manager  | NSString  | 管理员 |
| managerType  | NSString  | 管理员类型 |
| parentId  | NSString  | 父设备组ID |
| extend  | NSString  | 拓展字段 |
| dgid  | NSString  | 分组ID |
| owner  | NSString  | 拥有者 |
| addTime  | NSString  | 添加时间 |
| addTimeTs  | NSString  | 添加时间戳 |


**示例代码**

```objc
[QuecDeviceGroupService.sharedInstance getDeviceGroupListWithPageNumber:1
                                                                   pageSize:10
                                                                      extra:nil
                                                                    success:^(NSArray<QuecDeviceGroupInfoModel *> *list, NSInteger total) {
        /// Next Action
    } failure:^(NSError *error) {
        NSLog(@"check error: %@", error);
    }];
```

### 添加设备分组

**接口说明**

添加设备分组

```objc
- (void)addDeviceGroupWithInfo:(QuecDeviceGroupParamModel *)groupInfoModel
                        success:(QuecVoidBlock)success
                        failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- |  
| groupInfoModel | 是 | 分组信息 |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

>QuecDeviceGroupParamModel属性定义同上

**示例代码**

```objc
QuecDeviceGroupParamModel *paramModel = [[QuecDeviceGroupParamModel alloc] init];
paramModel.name = @"group name";
[QuecDeviceGroupService.sharedInstance addDeviceGroupWithInfo:paramModel success:^{
        /// Next Action
    } failure:^(NSError *error) {
        NSLog(@"check error: %@", error);
    }];
```

### 修改设备组

**接口说明**

修改设备组信息

```objc
- (void)updateDeviceGroupInfoWithDeviceGroupId:(NSString *)deviceGroupId
                                       infoModel:(QuecDeviceGroupParamModel * _Nullable)infoModel
                                         success:(QuecVoidBlock)success
                                         failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- |  
| deviceGroupId | 是 | 分组ID |
| infoModel | 是 | 分组信息 |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

>QuecDeviceGroupParamModel属性定义同上

**示例代码**

```objc
QuecDeviceGroupParamModel *paramModel = [[QuecDeviceGroupParamModel alloc] init];
paramModel.name = @"group name";
[QuecDeviceGroupService.sharedInstance updateDeviceGroupInfoWithDeviceGroupId:@"dgid" infoModel:paramModel success:^{
        /// Next Action
    } failure:^(NSError *error) {
        NSLog(@"check error: %@", error);
    }];
```

### 删除设备组

**接口说明**

根据分组Id删除设备组

```objc
- (void)deleteDeviceGroupWithDeviceGroupId:(NSString *)deviceGroupId
                                     success:(QuecVoidBlock)success
                                     failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- |  
| deviceGroupId | 是 | 分组id |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**示例代码**

```objc
[QuecDeviceGroupService.sharedInstance deleteDeviceGroupWithDeviceGroupId:@"dgid" success:^{
        /// Next Action
    } failure:^(NSError *error) {
        NSLog(@"check error: %@", error);
    }];
```

### 获取分组详情

**接口说明**

获取分组详情信息

```objc
- (void)getDeviceGroupInfoWithDeviceGroupId:(NSString *)deviceGroupId
                                      success:(void(^)(QuecDeviceGroupInfoModel *model))success
                                      failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- |  
| deviceGroupId | 是 | 分组id |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

>QuecDeviceGroupInfoModel属性定义同上

**示例代码**

```objc
[QuecDeviceGroupService.sharedInstance getDeviceGroupInfoWithDeviceGroupId:@"gpid" success:^(QuecDeviceGroupInfoModel * _Nonnull model) {
        /// Next Action
    } failure:^(NSError *error) {
        NSLog(@"check error: %@", error);
    }];
```

### 分组设备列表

**接口说明**

获取分组设备列表, 列表数据由[添加设备到设备组中]接口加入

```objc
- (void)getDeviceListWithDeviceGroupId:(NSString *)deviceGroupId
                        deviceGroupName:(NSString * _Nullable)deviceGroupName
                          deviceKeyList:(NSString * _Nullable)deviceKeyList
                             productKey:(NSString * _Nullable)productKey
                             pageNumber:(NSInteger)pageNumber
                               pageSize:(NSInteger)pageSize
                                success:(void (^)(NSArray<QuecDeviceModel *> *list, NSInteger total))success
                                failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- |  
| deviceGroupId | 是 | 分组id |
| deviceGroupName | 否 | 分组名称 |
| deviceKeyList | 否 | device key列表，多个device key，使用英文逗号分隔 |
| productKey | 否 | 产品key |
| pageNumber | 是 | 当前页，默认为第 1 页 |
| pageSize | 是 | 页大小，默认为 10 条 |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**示例代码**

```objc
[QuecDeviceGroupService.sharedInstance getDeviceListWithDeviceGroupId:@"gpid"
                                                          deviceGroupName:nil
                                                            deviceKeyList:nil
                                                               productKey:nil
                                                               pageNumber:1
                                                                 pageSize:1000
                                                                  success:^(NSArray<QuecDeviceModel *> *list, NSInteger total) {
        /// Next Action
    } failure:^(NSError *error) {
        NSLog(@"check error: %@", error);
    }];
```

### 添加设备到设备组中

**接口说明**

将设备添加到设备组中

```objc
- (void)addDeviceToGroupWithDeviceGroupId:(NSString *)deviceGroupId
                                 deviceList:(NSArray *)deviceList
                                    success:(void(^)(QuecOperateDeviceToGroupModel *model))success
                                    failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- |  
| deviceGroupId | 是 | 分组id |
| deviceList | 是 | 设备列表，示例：@[{@"dk": @"value", @"pk": "value"}] |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**QuecOperateDeviceToGroupModel属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| successList  | NSArray<QuecOperateDeviceToGroupRstModel *> * | 成功列表 |
| failureList  | NSArray<QuecOperateDeviceToGroupRstModel *> *  | 失败列表 |

**QuecOperateDeviceToGroupFailRstModel属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| data  | QuecOperateDeviceToGroupRstModel | 设备信息 |
| msg  | NSString | 错误提示 |

**QuecOperateDeviceToGroupRstModel属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| pk  | NSString | 产品pk |
| dk  | NSString | 设备dk |

**示例代码**

```objc
[QuecDeviceGroupService.sharedInstance addDeviceToGroupWithDeviceGroupId:@"gpid"
                                                            deviceList:@[{@"dk": @"value", @"pk": "value"}]
                                                                success:^(QuecOperateDeviceToGroupModel * _Nonnull model) {
        /// Next Action
    } failure:^(NSError *error) {
        NSLog(@"check error: %@", error);
    }];
```

### 删除分组中的设备

**接口说明**

将分组中的设备删除

```objc
- (void)deleteDeviceFromGroupWithDeviceGroupId:(NSString *)deviceGroupId
                                      deviceList:(NSArray *)deviceList
                                         success:(void(^)(QuecOperateDeviceToGroupModel *model))success
                                         failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- |  
| deviceGroupId | 是 | 分组id |
| deviceList | 是 | 设备列表，示例：@[{@"dk": @"value", @"pk": "value"}] |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

>QuecOperateDeviceToGroupModel属性定义同上

**示例代码**

```objc
[QuecDeviceGroupService.sharedInstance deleteDeviceFromGroupWithDeviceGroupId:@"gpid"
deviceList:@[{@"dk": @"value", @"pk": "value"}] success:^(QuecOperateDeviceToGroupModel * _Nonnull model) {
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 网关设备下子设备列表

**接口说明**

 查询网关设备下子设备列表

```objc
- (void)getGatewayDeviceChildListWithDeviceKey:(NSString *)deviceKey
                                      productKey:(NSString *)productKey
                                      pageNumber:(NSInteger)pageNumber
                                        pageSize:(NSInteger)pageSize
                                         success:(void(^)(NSArray<QuecDeviceModel *> *list, NSInteger total))success
                                         failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- |  
| deviceKey | 是 | 设备dk |
| productKey | 是 | 产品pk |
| pageNumber | 是 | 页码 |
| pageSize | 是 | 页大小 |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**示例代码**

```objc
[QuecDeviceGroupService.sharedInstance getGatewayDeviceChildListWithDeviceKey:@"deviceKey" productKey:@"productKey" pageNumber:1 pageSize:10 success:^(NSArray<QuecDeviceModel *> * _Nonnull list, NSInteger total) {
         /// Next Action
    } failure:^(NSError *error) {
        NSLog(@"check error: %@", error);
    }];
```

### 不在设备组内的设备列表

**接口说明**

查询不在设备组内的设备列表, 用于设备过滤添加进设备组

```objc
- (void)getDeviceListByNotInDeviceGroupWithPageNumber:(NSInteger)pageNumber
                                                pageSize:(NSInteger)pageSize
                                                 groupId:(NSString *)groupId
                                                 success:(void(^)(NSArray<QuecDeviceModel *> *list, NSInteger total))success
                                                 failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- |  
| deviceKey | 是 | 设备dk |
| productKey | 是 | 产品pk |
| groupId | 是 | 设备组ID |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**示例代码**

```objc
[QuecDeviceGroupService.sharedInstance getDeviceListByNotInDeviceGroupWithPageNumber:1
                                                                                pageSize:10
                                                                                 groupId:@"gpId"
                                                                                 success:^(NSArray<QuecDeviceModel *> *list, NSInteger total) {
        /// Next Action
    } failure:^(NSError *error) {
        NSLog(@"check error: %@", error);
    }];
```

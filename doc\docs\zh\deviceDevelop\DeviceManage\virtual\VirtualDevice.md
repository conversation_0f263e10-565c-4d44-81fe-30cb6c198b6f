# 虚拟设备

在硬件设备未完成开发前，您可通过虚拟设备进行应用端的真实通信数据模拟，用于数据交互调试以及预览应用端效果。
移动端应用可通过扫描设备二维码进行绑定。

## **虚拟设备上线/下线**

每个账号下的每款产品，可创建一个虚拟设备。

创建虚拟设备时需输入DeviceKey和SN，您可以使用默认值完成创建。

完成虚拟设备创建后，可通过“虚拟设备上线”、“虚拟设备下线”按钮，改变在线状态。

虚拟设备单次在线时长最大为最后活跃时间+24小时，超过后将自动离线。

当虚拟设备为子设备时，上线需选择绑定网关虚拟设备。

## **模拟数据上报**

您可通过左侧的属性、事件、服务回调页面，模拟真实设备上报物模型数据：

1. 勾选需要上报的物模型；
2. 设置期望上报的具体数值；
3. 点击推送按钮

推送成功后应用端（App或AMQP消费客户端）可收到设备上行数据。（仅当虚拟设备为在线状态时，可推送上行消息）

您可设置延迟响应时间来模拟应用端查询设备状态、服务回调响应等非实时响应情况。

## **通信日志**

当虚拟设备发生上线/下线、接收到下行物模型数据、模拟上报物模型数据时，均会在通信日志中显示相关内容。

备注：当前暂不支持物模型电信NB产品与蓝牙产品使用虚拟设备功能。
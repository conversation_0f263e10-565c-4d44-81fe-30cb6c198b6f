# 常见问题

## <span style="color:#A52A2A">__快速接入开发者中心常见问题__</span>

> __1.设备初始化需要做什么？__ <br>
> 答：QuecThing的初始化配置必须完成以下几步的操作。
>
> ```c
> /* 初始化QuecThing SDK */
> Ql_iotInit();
> /* 注册事件回调函数 */
> Ql_iotConfigSetEventCB(Ql_iotEventCB);
> /* 配置产品信息*/
> Ql_iotConfigSetProductinfo("pxxxxt", "VzZUxxxxxxxxxx9E");
> ```

> __2.ProductKey和ProductSecret是什么？从哪里获取ProductKey和ProductSecret？__ <br>
> 答：ProductKey(简称PK)和ProductSecret(简称PS)分别是产品号和产品密钥，是产品的唯一标识，产品下所有设备都使用同一个PK和PS。在开发者中心创建产品时，PK和PS会自动下发到平台注册时所使用的邮箱。

> __3.事件回调函数是什么？__ <br>
> 答：事件回调函数简单的说就是为事件源(组件)添加一个监听任务 __Ql_iotEventCB()__ ，当用户触发了某件事件时，交给监听器去处理，开发者即可根据不同的事件来执行不同的操作。

> __4.如何连接开发者中心？__ <br>
> 答：在完成初始化后，调用 __Ql_iotConfigSetConnmode(1)__ 即可连接开发者中心。

> __5.如何确定当前设备已连接开发者中心？__ <br>
> 答：模组调用 __Ql_iotGetWorkState()__ 函数可查询当前设备与平台之间的连接状态，若返回值为 __8__， 则确保模组已订阅上开发者中心并且可进行数据业务交互。

> __6.设备如何在开发者中心认证？如何激活？__ <br>
> 答：设备在初次连接上开发者中心时，若有激活码的情况下，设备会自动认证并且激活。<br>

> __7.为什么设备连接平台时失败，事件回调码打印 "1,10422" ？__ <br>
> 答：一般是切换产品导致模组保存的DS与产品DS对应失败，或者是烧录固件时覆盖了配置文件导致模组保存的DS被清除了。
> 您只需要在平台重置一下**DeviceSecret**，并将设备重新上线即可。<br>

> __8.设备连接开发者中心之前，我需要配置服务器信息吗？__ <br>
> 答：模组已有默认服务器信息，若无特殊需求则无需配置。<br>

> __9.设备生命周期(lifetime)是什么？设备连接开发者中心之前我需要设置吗？__ <br>
> 答：设备生命周期(lifetime)，指的是协议内部的心跳时间，一般没有特殊要求不需要设置，使用默认值(120 s)即可。<br>

> __10.设备的“MCU编号及其对应的版本号”是什么？设备连接开发者中心之前我需要设置吗？__ <br>
> 答：“MCU编号及其对应的版本号”是当您需要通过模组给设备上的MCU进行SOTA升级时，才需要设置。当设备没有MCU，或者不需要给MCU升级时，则不需要设置。

> __11. PDP上下文ID(contextID)是什么？设备连接开发者中心之前我需要设置吗？__ <br>
> 答：contextID是当模组除了使用平台功能以外，还需要使用模组进行其他传输业务时才需要设置的，若无特殊需求则不需要进行配置，默认关闭。<br>

> __12.设备与平台之间的连接加密模式是什么？设备连接开发者中心之前我需要设置吗？__ <br>
> 答：数据链路的一种加密模式，可根据实际需求进行选择，默认关闭。<br>

> __13.设备连接平台之前，我需要配置设备标识(DeviceKey) 和设备密钥(DeviceSecret) 吗？__ <br>
> 答：__DeviceKey__ 一般不需要特别设置，模组默认使用IMEI（蜂窝模组）或者MAC地址（Wi-Fi模组）作为 __DeviceKey__。 __DeviceSecret__ 也不需要配置，模组第一次连接平台时，平台会生成 __DeviceSecret__ 并下发给模组（此步骤用户无感知）。

## <span style="color:#A52A2A">__数据业务常见问题__</span>
> __1.数据格式有几种？__ <br>
> 答：数据格式有两种，分别为物模型与透传。

> __2.物模型是什么？__ <br>
> 答：物模型是指设备在开发者中心的数字化表示，并在开发者中心构建的实体数据模型，并分为属性、服务和事件三个维度。

> __3.透传数据格式是什么？__ <br>
> 答：透传数据格式是指设备上报透传数据到开发者中心，平台不做任何数据解析。

> __4.QoS是什么？__ <br>答：QoS（Quality of Service）是服务质量的简称，提高服务质量就是保证传输的带宽，降低数据的丢包率以及延时抖动等，用户可根据实际的网络状态选择相对应的QoS。
> 
> * QoS 当前有三个等级：0，1，2。<br>
> * 若设备与平台间需要高频且不可靠交互时，出于流量节省和高效性，可选择QoS为0。<br>
> * 若设备与平台间需要高频且需可靠交互时，出于流量节省和高效性，可选择QoS为1。<br>
> * 若设备与平台间需要高频且需可靠交互时，且需要严格遵循一发一收原则，可选择QoS为2。<br>

> __5.PkgID是什么？__ <br>
> 答：PkgID是上下行数据包的ID。例如，开发者在连续多次发送上行数据时，可能无法判别哪个数据发送成功或失败了，所以在发送数据时会先生成一个PkgID，在数据成功发送到平台后，平台再将该数据的PkgID通知给开发者，开发者即可以根据两个PkgID的一致性来判别数据是否发送成功。

> __6.如何发送物模型数据到开发者中心？__ <br>
> 答：调用 __Ql_iotCmdBusPhymodelReport_ex()__ 函数即可向开发者中心发送物模型数据。

> __7.如何接收物模型数据？__ <br>
> 答：当开发者中心下发物模型数据时，设备接收到数据会自动调用回调函数并上报事件{5,10210}，您可调用 __Ql_iotTtlvCountGet()__ 与 __Ql_iotTtlvNodeGet()__ 函数获取及解析TTLV数据。详情请参考物模型数据业务交互示例。

> __8.如何应答开发者中心请求的物模型数据？__ <br>
> 答：调用 __Ql_iotCmdBusPhymodelAck()__ 函数即可应答开发者中心，并发送物模型数据。

> __9.设备上报事件时，一定要携带属性参数吗？__ <br>
> 答：设备上报事件时，可以不携带参数。  <br>

> __10.设备上报物模型数据时，可以同时上报属性和事件吗？__ <br>
> 答：可以，设备上报物模型数据时可以一次性同时上报多条属性和事件。<br>  


## <span style="color:#A52A2A">__OTA升级常见问题__</span>
> __1.OTA是什么意思？__ <br>
> 答：OTA是指在线网络升级。可将升级固件包上传到开发者中心并创建好升级计划，当设备环境满足升级策略后，模块会主动触发平台计划以确认升级。

> __2.OTA分为几种，分别是什么？__ <br>
> 答：OTA分为两种，分别是FOTA（模组固件升级）、SOTA（MCU固件升级）。

> __3.我该如何选择OTA？__ <br>
> 答：根据项目需求来定，若是给MCU升级程序则选择SOTA，若是给模组升级则选择FOTA。
>
> * 若用户需要在模组固件基础上进行二次开发，则需使用FOTA方式进行升级。<br>
> * 若用户使用移远模组与自身开发的设备进行开发，则需使用SOTA方式进行升级。<br>

> __4.整包和差分包的区别？__ <br>
> 答：<br>
>  1、制作方式 <br>
>   整包和差分包均可以实现固件升级，整包是用户项目通过编译器编译完整代码生成的固件；
> 而差分包是通过对比原始版本的代码差异，生成可以插入到原始固件中的固件（可称为补丁），并且只有使用特定的工具才能生成。<br>
>  2、流量 <br>
>   整包是属于完整项目编译的固件，故一般情况下，文件远大于差分包的大小，因此对于高延时、低带宽或者低功耗设备而言，差分包具备更好的优势。<br>
>  3、兼容性 <br>
>   差分包升级是基于上一个版本的固件进行修整，所以差分包升级无法兼容更低版本升级到最新版本，若需要全部升级，则需要创建多个组件或计划，所以对于大批量多版本设备而言，维护成本偏高。  

> __5.FOTA操作步骤是什么？__ <br>
> 答：<br>
> 1、用户需在开发者中心新增版本包并创建升级计划。 <br>
> 2、调用 __Ql_iotCmdOtaRequest()__ 函数请求 OTA 升级计划，若设备升级环境满足升级策略即可确认升级。 <br>
> 3、调用 __Ql_iotCmdOtaAction()__ 函数确认升级。 <br>
> 4、模组自动接收升级包后自动进入升级状态。 <br>

> __6.SOTA操作步骤是什么？__ <br>
> 答：<br>
> 1、调用 __Ql_iotConfigSetMcuVersion()__ 函数配置 MCU 版本号。 <br>
> 2、用户需在开发者中心新增版本包并创建升级计划。 <br>
> 3、调用 __Ql_iotCmdOtaRequest()__ 函数请求 OTA 升级计划，若设备升级环境满足升级策略即可确认升级。 <br>
> 4、调用 __Ql_iotCmdOtaAction()__ 函数确认升级。 <br>
> 5、调用 __Ql_iotCmdOtaMcuFWDataRead()__ 函数分片读取SOTA 数据。 <br>
> 6、MCU接收完整升级包数据后则进入更新状态。 <br>
> 7、MCU升级完毕后设置新的MCU版本号并上传至开发者中心。 <br>

> __7.OTA为什么触发不了？__ <br>
> 答：<br>
> 原因一：当前设备未连接开发者中心。
> 原因二：当前设备的属性不满足计划的要求，如用户剩余空间不足、当前信号弱、版本不匹配。
> 原因三：用户所创建的计划不包含该设备。

> __8.OTA为什么会失败？__ <br>
> 答：<br>
> 原因一：请确保当前SDK版本是否符合相关的AT指令，若2.9.2版本前的SDK使用2.9.2版本及之后的AT指令将可能会发生未知错误。<br>
> 原因二：设备进行OTA升级时，若设备断电、断网或与开发者中心断开连接直至超过了计划时间则都被认定为升级失败。<br>
> 原因三：设备进行OTA升级时，若出现网络波动并持续了一段时间，设备将认为本次升级失败，并自动重新触发升级，若连续5次均失败，则认为该设备升级计划失败。<br>
> 原因四：若设备OTA下载完成却更新失败，有可能是更新过程中损坏了升级文件，您可以关注与该升级文件储存区域相关的操作。<br>

> __9.向平台请求 OTA 升级计划时，在调用 Ql_iotCmdOtaRequest(quint32_t mode) 函数时，入口参数 mode 选择0和1有什么区别？__ <br>
> 答：
>
> * 入口参数 __mode__ 为**0**时，平台下发升级计划时不会携带固件的SHA256校验信息；
> * 入口参数 __mode__ 为**1**时，平台下发升级计划时会携带固件包的SHA256校验信息；<br>
> 您可以根据自己的需求来决定是否需要使用固件的SHA256校验信息。<br>

> __10.为什么SOTA升级时，设备发送OTA请求指令后，无法收到升级计划？__ <br>
> 答：<br>
>    原因一：平台未创建升级计划或者计划未激活。<br>
>    原因二：模组未设置MCU版本号，需要先调用 __Ql_iotConfigSetMcuVersion()__ 函数设置MCU版本号。 <br>

> __11.为什么SOTA升级时，模组不能一次性下载所有升级包？__ <br>
> 答：模组空闲内存不够，无法一次性存放所有升级包，所以会采取分包下载，模组空闲内存大小因模组型号而异。 <br>    

> __12.为什么SOTA升级时，模组不会自动下载下一块固件数据？__ <br>
> 答：SOTA升级时，当设备从开发者中心分块下载固件时，在下载完第一块后，需要模块调用 __Ql_iotCmdOtaAction(2)__ 函数才会下载下一块数据。

> __13.设备端如何判断设备OTA是否成功？__ <br>
> 答：当设备OTA升级成功时，设备端会打印事件响应码：__7,10705__ ，或在开发者中心升级计划界面查看计划升级成功。<br>

## <span style="color:#A52A2A">__设备定位常见问题__</span>
> __1.设备定位有几种方式？__ <br>
> 答：QuecThing 设备定位有三种，分别是LBS基站定位、GNSS定位、Wi-Fi定位。
>
> __2. LBS基站定位特点？__ <br>
> 答：LBS基站定位覆盖率高，但要求室内室外都有信号，定位精度较低，无需外置模组。
>
> __3. GNSS定位特点？__ <br>
> 答：GNSS定位速度快，全天候作业，不需要考虑目视通视条件，可以远距离大范围测量。但受天顶方向遮盖影响极大，在室内、隧道内精度下降。
>
> __4.Wi-Fi定位特点？__ <br>
> 答：Wi-Fi 定位速度快，在密集人流多的地方相当精确。但依赖于W-iFi，没有打开Wi-Fi就无法定位，并且Wi-Fi必须处于联网状态。<br>
>
> __5. LBS基站定位该如何使用？__ <br>
> 答：QuecThing已包含LBS基站定位组件，只需调用 __Ql_iotLocGetData()__ 函数即可获取设备当前内置定位功能的定位数据。调用 __Ql_iotCmdBusLocReportInside_ex()__ 函数即可上报设备内置定位功能的定位数据。
>
> __6. GNSS定位该如何使用？__ <br>
> 答：GNSS定位需要外置GNSS模组，需将GNSS模组读取的坐标系读取到模组内，再调用 __Ql_iotCmdBusLocReportOutside_ex()__ 函数上报设备外置定位功能的定位数据。
>
> __7. Wi-Fi定位该如何使用？__ <br>
> 答：QuecThing已包含Wi-Fi定位功能，只需调用 __Ql_iotLocGetData()__ 函数即可获取设备当前内置定位功能的定位数据。调用 __Ql_iotCmdBusLocReportInside_ex()__ 函数即可上报设备内置定位功能的定位数据。
>
> __8.我该选择哪种定位方式？__ <br>
> 答：<br>
> 若对设备的定位精度要求不高，可选择使用LBS定位；<br>
> 若对设备的定位精度要求较高，可选择使用GNSS定位；<br>
> 若对设备的定位精度要求不高，并且模组支持Wi-Fi定位，可选择Wi-Fi定位。<br>

> __9.设备端如何判断设备定位数据上报成功？__ <br>
> 答：当设备上报定位数据成功时，模组会调用事件回调函数**(*eventCB)(4, 10200)**。 <br>

> __10.为什么发送定位数据失败，返回ERROR？__ <br>
> 答：当前设备还未连接上平台，请在设备连接平台之后再上报定位数据。 <br>

## <span style="color:#A52A2A">__网关与子设备常见问题__</span>
> __1.网关是什么？__ <br>
> 答：网关的功能是转换不同设备间的通讯协议，可以让不具有联网能力的子设备通过网关连接上平台。

> __2.子设备是什么？__ <br>
> 答：子设备是不具备联网能力的设备，需通过与网关的连接才能与平台通讯。

> __3.子设备心跳是什么？__ <br>
> 答：子设备的心跳指的是 网关需要定期检测子设备是否已断开的周期。<br>
> * 若在指定的心跳周期内，子设备发送心跳信息给网关，网关将刷新子设备的检测周期；<br>
> * 若在指定的心跳周期内，子设备没有发送心跳信息给网关，网关将认为子设备已断开，则网关关闭该接口。<br>

> __4.直连设备、网关和子设备的区别？__ <br>
> 答：
>
> * 直连设备指的是不具有网关或子设备的关联关系的产品。<br>
> * 网关和子设备指的是具备成为网关或子设备的产品。<br>

> __5.为什么子设备初次连接平台成功后，后续连接平台时设备会打印回调事件码 **1,10422** ？__ <br>
> 答：子设备初次连接平台时，不需要携带子设备DeviceSecret，但后续连接平台时需要携带DeviceSecret。 <br>

## <span style="color:#A52A2A">__DTU 接入方案常见问题__</span>

> __1. 什么样的设备可以使用ModBus DTU接入开发者中心？__
> <br>
> 答：只要Modbus从机设备满足Modbus标准通信协议的设备均可使用ModBus DTU接入开发者中心。

> __2.ModBus DTU支持哪些功能码？__ <br>
> 答：目前ModBus DTU支持0X01\0X03\0X04\0X05\0X06\0X10功能码，能对ModBus从机设备的线圈、保持寄存器、输入寄存器进行读写操作。

> __3. ModBus DTU能支持多少个从机设备连接？__ <br>
> 答：根据ModBus协议，每个串口最大支持247个从机设备，根据不同的通信模块的串口数量限制，不同的DTU会有差异。

> __4.上报方式中全部上报和变化上报有什么区别？__ <br>
> 答：当选择 __全部上报__ 时，DTU查询到的从机设备数据会全部上报到平台；当选择 __变化上报__ 时，DTU会检查每次查询到的从机设备数据，只把变化的寄存器数据上报到平台，从而节省通信流量。

> __5.设备类型中直连版和网关版有什么区别？__ <br>
> 答：当选择 __直连版__ 时，所有子设备的功能信息和数据都会被汇总到一个设备上，平台上只会显示一个网关设备，不过的子设备信息通过树状结构进行展示；当选择 __网关版__ 时，需要为每一款子设备在平台上创建对应的产品信息，网关联网成功后会为每一次子设备在平台注册上线，后续子设备的上下行数据均可以在对应的子设备界面展示。

> __6.配置文件怎么烧写到通信模块？__ <br>
> 答：可以通过模块的烧写工具，把配置文件烧写到对应的文件系统位置上；也可以通过配置的下载串口把文件下载到文件系统中。

 ## <span style="color:#A52A2A">__其他常见问题__</span>
> __1.为什么使用Qflash烧录固件时，显示烧录失败？__ <br>
> 答：<br>
> 原因一：没有正确安装模组驱动。<br>
> 原因二：固件的存放路径下有中文或者空格。<br>

> __2.为什么设备上报告警事件，移联万物APP却没有弹窗提醒？__ <br>
> 答： 开发者中心没有配置消息通知规则，用户只需在开发者中心“产品开发”->“产品”->“消息通知”配置一下消息规则即可。

> __3.为什么开发者中心下行数据时，MCU仿真工具提示无法匹配数据？__ <br>
> 答：MCU仿真工具没有导入物模型，导致工具无法解析平台下发的物模型，需要在工具中导入物模型文件。 <br> 






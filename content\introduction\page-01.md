---
title: "平台概述"
description: "平台概述相关文档"
---

# 平台概述

开发者中心是一个可以快速连接设备和企业系统的物联网平台，提供安全可靠的设备连接通信能力，帮助用户将海量设备连接平台。可为客户提供 “终端+连接+平台+应用” 的端到端解决方案，提供方便快捷的设备管理能力。支持功能定义、消息订阅、OTA 升级和远程调试、监控、运维等服务，助力企业降本、增效、提质和快速构建上层业务应用，轻松管理设备。


<a data-fancybox title="img" href="/zh/productIntroduce/image10001.png">![img](/zh/productIntroduce/image10001.png)</a>

## **平台功能**概述

### 产品管理

产品是设备的集合，通常是将具有相同功能定义（物模型）的设备归属到一个产品下。平台为每个产品颁发全局唯一的ProductKey。该产品下所有设备将会烧录同一个ProductKey进行设备连接IoT平台的认证与通信。

### 物模型

在开发者中心上，可以对产品进行功能定义，包括设备的属性、事件和服务，通过功能定义可简化上层应用的开发工作。

### 设备接入

提供 2G/3G/4G/5G、NB-IoT、WiFi、蓝牙等不同设备连网接入方案，解决客户不同设备接入管理的痛点，同时支持基于 MQTT、LwM2M 等多协议接入、实现海量设备连接平台。

### 设备管理

提供丰富的设备全生命周期管理能力，支持设备注册，激活，认证，删除等。同时提供数据存储能力，支持对设备上报的历史属性、服务、事件等数据查询，简化海量设备管理复杂性，提升管理效率。

### 通信安全

通信链路层面，平台与设备间支持MQTTS与CoAPs加密通信，保障数据安全。业务链路层面，平台与设备间支持一型一密、一机一密、一机一证的认证方式。

### 远程固件升级

通过开发者中心可进行固件包与升级计划管理。平台提供整包与差分包升级方式，并支持设置升级时间段，从而实现对硬件的模组程序以及MCU程序进行远程升级。

### 数据缓存

支持消息离线存储，支持设备消息（包括上报及下发）的离线存储能力，同时支持离线存储时间可根据需求做配置调整。

### 消息订阅

开发者中心提供基于高性能、高可用的消息队列数据流转服务，支持企业客户端通过AMQP方式接收设备上报的业务数据，客户端可自定义订阅的数据类型，按需转发数据到企业客户端。

### 开放 API

提供符合 REST 接口规范的产品、设备、服务等IoT平台 API 接口，帮助开发者快速开发应用，满足场景业务需求。




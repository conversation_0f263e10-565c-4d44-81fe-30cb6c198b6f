# 设备分享

## 功能概述

设备分享相关功能，您可以通过设备分享相关功能，实现设备分享到其他用户，实现设备分享到其他用户后，其他用户可以控制您的设备。

> QuecDeviceModel属性定义参考[设备管理](设备管理.md)

```objc
#import <QuecDeviceKit/QuecDeviceKit.h>
/// 初始化
[QuecDeviceShareService sharedInstance]
```

## 设备分享

### 分享码查询设备信息

**接口说明**

通过分享码查询设备信息

```objc
- (void)getDeviceInfoByShareCode:(NSString *)shareCode
                          success:(void(^)(QuecDeviceModel *deviceModel))success
                          failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- |  
| shareCode | 是 | 分享码 |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**示例代码**

```objc
[QuecDeviceShareService.sharedInstance getDeviceInfoByShareCode:@"shareCode" success:^(QuecDeviceModel *deviceModel) {
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 更改分享设备名称

**接口说明**

更改分享设备名称

```objc
- (void)updateDeviceNameByShareUserWithDeviceName:(NSString *)deviceName
                                          shareCode:(NSString *)shareCode
                                            success:(QuecVoidBlock)success
                                            failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- |  
| deviceName | 是 | 设备名称 |
| shareCode | 是 | 分享码 |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**示例代码**

```objc
[QuecDeviceShareService.sharedInstance updateDeviceNameByShareUserWithDeviceName:@"deviceName" shareCode:@"shareCode" success:^{
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 设备分享人列表

**接口说明**

获取设备分享人列表

```objc
- (void)getDeviceShareUserListWithDeviceKey:(NSString *)deviceKey
                                   productKey:(NSString *)productKey
                                      success:(void(^)(NSArray<QuecShareUserModel *> *list))success
                                      failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- |  
| deviceKey | 是 | 设备dk |
| productKey | 是 | 产品pk |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**QuecShareUserModel属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| shareInfo  | QuecShareUserShareInfoModel | 分享信息 |
| userInfo  | QuecShareUserInfoModel | 用户信息 |

**QuecShareUserShareInfoModel属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| acceptTime  | NSString | 分享时间 |
| acceptingExpireAt  | NSString | 分享失效时间 |
| coverMark  | NSInteger | 标记 |
| deleteTime  | NSString | 删除时间 |
| deviceName  | NSString | 设备名称 |
| dk  | NSString | 设备key |
| ownerUid  | NSString | 所有者ID |
| pk  | NSString | 产品key |
| shareCode  | NSString | 分享码 |
| shareId  | NSString | 分享ID |
| shareStatus  | NSInteger | 分享状态 |
| shareTime  | NSString | 分享时间 |
| shareUid  | NSString | 分享用户ID |
| sharingExpireAt  | NSString | 分享失效时间 |
| updateTime  | NSString | 更新时间 |

**QuecShareUserInfoModel属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| address  | NSString | 地址 |
| email  | NSString | 邮箱 |
| headimg  | NSString | 头像 |
| lastLoginTime  | NSString | 最后登录时间 |
| nikeName  | NSString | 昵称 |
| phone  | NSString | 手机号码 |
| registerTime  | NSString | 授权时间 |
| sex  | NSString | 性别 |
| uid  | NSString | 用户ID |
| userDomain  | NSString | 用户域 |
| userType  | NSInteger | 用户来源 |
| wchartId  | NSString | 微信ID |
| wchartName  | NSString | 微信名称 |

**示例代码**

```objc
[QuecDeviceShareService.sharedInstance getDeviceShareUserListWithDeviceKey:@"deviceKey" productKey:@"productKey" success:^(NSArray<QuecShareUserModel *> * _Nonnull list) {
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 取消分享

**接口说明**

分享人取消分享

```objc
- (void)unShareDeviceByOwnerWithShareCode:(NSString *)shareCode
                                    success:(QuecVoidBlock)success
                                    failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- |  
| shareCode | 是 | 分享码 |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**示例代码**

```objc
[QuecDeviceShareService.sharedInstance unShareDeviceByOwnerWithShareCode:@"shareCode" success:^{
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 被分享人取消分享

**接口说明**

被分享人移除已接收分享的设备

```objc
- (void)unShareDeviceByShareUserWithShareCode:(NSString *)shareCode
                                        success:(QuecVoidBlock)success
                                        failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- |  
| shareCode | 是 | 分享码 |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	|

**示例代码**

```objc
[QuecDeviceShareService.sharedInstance unShareDeviceByShareUserWithShareCode:@"shareCode" success:^{
    /// Next Action
} failure::^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 接受分享

**接口说明**

被分享人接受分享

```objc
- (void)acceptShareByShareUserWithShareCode:(NSString *)shareCode
                                   deviceName:(NSString *)deviceName
                                      success:(QuecVoidBlock)success
                                      failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- |  
| shareCode | 是 | 分享码 |
| deviceName | 是 | 设备名称 |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**示例代码**

```objc
[QuecDeviceShareService.sharedInstance acceptShareByShareUserWithShareCode:@"shareCode" deviceName:@"deviceName" success:^{
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 分享人设置分享信息

**接口说明**

分享人设置分享信息

```objc
- (void)setShareInfoByOwnerWithDeviceKey:(NSString *)deviceKey
                                productKey:(NSString *)productKey
                      acceptingExpireTime:(NSInteger)acceptingExpireTime
                                 coverMark:(NSInteger)coverMark
                     isSharingAlwaysValid:(BOOL)isSharingAlwaysValid
                        sharingExpireTime:(NSInteger)sharingExpireTime
                                   success:(void(^)(NSString *shareCode))success
                                   failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- |  
| deviceKey | 是 | 设备dk |
| productKey | 是 | 产品pk |
| acceptingExpireTime | 是 | 分享二维码种子失效时间 时间戳（毫秒），表示该分享在此时间戳时间内没有使用，会失效 |
| coverMark | 是 | 覆盖标志:1 直接覆盖上条有效分享（默认）（覆盖原有的分享码）；2 直接添加，允许多条并存；3 只有分享时间延长了，才允许覆盖上条分享 |
| isSharingAlwaysValid | 否 | 设备是否永久有效 |
| sharingExpireTime | 否 | 设备使用到期时间 时间戳（毫秒），表示该分享的设备，被分享人可以使用的时间，isSharingAlwaysValid为YES时该参数无效 |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**示例代码**

```objc
[QuecDeviceShareService.sharedInstance setShareInfoByOwnerWithDeviceKey:@"deviceKey"
                                                                         productKey:@"productKey"
                                                                acceptingExpireTime:expireTimeTS
                                                                          coverMark:1
                                                               isSharingAlwaysValid:YES
                                                                  sharingExpireTime:0 success:^(NSString * _Nonnull shareCode) {
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

# One-Click Login with Local Phone Number


**API Address**:`/v2/enduser/enduserapi/phoneAuthLogin`


**Request Method**:`POST`


**Request Data Type**:`application/json`


**Response Data Type**:`*/*`


**API Description**:`One-Click Login with local phone number. If the phone number is not registered, it will automatically register and log in, returning a token`


**Request Parameters**:


| Parameter Name      | Parameter Description                                        | Request Type | Required | Data Type       | Schema |
| ------------------- | ------------------------------------------------------------ | ------------ | -------- | -------------- | ------ |
| appid               | Application Id                                               | query        | true     | string         |        |
| msgid               | Unique identifier generated by the business (a random number to identify the request (1-36 digits)) | query        | true     | string         |        |
| sign                | Encrypted sign<br/>Sign generation rule: MD5(appId +version + msgId + systemtime + strictcheck + token + appsecret) | query        | true     | string         |        |
| strictcheck         | Temporarily fill in "0". When filled with "1", the server IP whitelist will be strictly verified (IP strict verification will be mandatory in the future) | query        | true     | string         |        |
| systemtime          | System time when the request message was sent, accurate to milliseconds, 17 digits in total, format: 20121227180001165 | query        | true     | string         |        |
| token               | Business credential                                          | query        | true     | string         |        |
| userDomain          | User domain                                                  | query        | true     | string         |        |
| version             | Interface version number                                     | query        | true     | string         |        |
| encryptionalgorithm | Encryption algorithm (MD5)                                   | query        | false    | string         |        |
| expandparams        | Extended parameters                                          | query        | false    | string         |        |
| mcc                 | MCC                                                          | query        | false    | integer(int32) |        |


**Response Status**:


| Status Code | Description                               | Schema                                         |
| ----------- | ----------------------------------------- | ---------------------------------------------- |
| 200         | Login successful, token returned in data  | Return registration code response data«Login returned token» |
| 5000        | Service exception                         |                                                |
| 5013        | User domain format incorrect              |                                                |
| 5015        | User domain does not exist                |                                                |
| 5023        | Please enter user domain                  |                                                |
| 5104        | User domain has been disabled             |                                                |
| 5220        | China Mobile service response exception   |                                                |
| 5221        | China Mobile service parameter validation failed |                                         |
| 6084        | MCC does not belong to current data center |                                               |
| 6085        | Failed to query data center for MCC       |                                                |


**Response Parameters**:


| Parameter Name                         | Parameter Description                                         | Type           | Schema         |
| ------------------------------------- | ------------------------------------------------------------ | -------------- | -------------- |
| code                                  | Response status code                                         | integer(int32) | integer(int32) |
| data                                  | Response data                                                | Login returned token | Login returned token |
| &emsp;&emsp;accessToken               | Interface access token                                       | token          | token          |
| &emsp;&emsp;&emsp;&emsp;expirationTime | Expiration timestamp in seconds. Token becomes invalid after expiration time. | integer        |                |
| &emsp;&emsp;&emsp;&emsp;token         | token                                                        | string         |                |
| &emsp;&emsp;refreshToken              | Refresh token                                                | token          | token          |
| &emsp;&emsp;&emsp;&emsp;expirationTime | Expiration timestamp in seconds. Token becomes invalid after expiration time. | integer        |                |
| &emsp;&emsp;&emsp;&emsp;token         | token                                                        | string         |                |
| extMsg                                | Extended message                                             | string         |                |
| msg                                   | Response message                                             | string         |                |


**Response Example**:
```javascript
{
	"code": 0,
	"data": {
		"accessToken": {
			"expirationTime": 0,
			"token": ""
		},
		"refreshToken": {
			"expirationTime": 0,
			"token": ""
		}
	},
	"extMsg": "",
	"msg": ""
}
```
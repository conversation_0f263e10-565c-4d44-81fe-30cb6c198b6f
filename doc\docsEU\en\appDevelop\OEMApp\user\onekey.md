# Enable Login via Local Phone Number

OEM apps that have enabled the "`Login via Local Phone Number`" service support app users who have enabled SIM data services to quickly log in to the app without entering their phone numbers and SMS verification codes. This service not only enhances the users' experience but also greatly improves account security.

Preview of "Login via Local Phone Number"

<a data-fancybox title="img" href="/en/appDevelop/oemapp/onekey/onekey.png"><img style="width: 320px;" src="/en/appDevelop/oemapp/onekey/onekey.png"></a>



## Prerequisites

You have purchased a data plan for the "Login via Local Phone Number", and the remaining data is more than 0 M.

Acceleronix has configured the service for your app (The service only needs to be set once).

```
Note:
Only operators in Chinese Mainland support this service, including China Mobile, China Unicom, and China Telecom.
Due to the limitations of mobile operators' capabilities, Android phones with China Unicom SIM inserted does not support this service.
```


## FAQ

### Can the "China Mobile Authentication Service Term", "China Telecom Authentication Service Term", and "China Unicom Authentication Service Term" on the login page be customized?

No. These terms are standard and managed by the operators.


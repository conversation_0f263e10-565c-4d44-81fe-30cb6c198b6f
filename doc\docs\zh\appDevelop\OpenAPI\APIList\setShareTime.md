# 分享人设置分享可使用时间


**接口地址**:`/v2/binding/enduserapi/setShareTime`


**请求方式**:`PUT`


**请求数据类型**:`application/json`


**响应数据类型**:`*/*`


**接口描述**:<p>分享人设置分享可使用时间</p>


**请求参数**:


| 参数名称        | 参数说明                                                     | 请求类型 | 是否必须 | 数据类型       | schema |
| --------------- | ------------------------------------------------------------ | -------- | -------- | -------------- | ------ |
| shareCode       | 分享码                                                       | query    | true     | string         |        |
| sharingExpireAt | 设备使用到期时间的毫秒时间戳，表示该分享的设备被分享人可以使用的时间 | query    | true     | integer(int64) |        |


**响应状态**:


| 状态码 | 说明                         | schema             |
| ------ | ---------------------------- | ------------------ |
| 200    | 分享人设置分享可使用时间成功 | 返回注册码响应数据 |
| 5032   | token 验证失败               |                    |
| 5041   | 没有权限                     |                    |
| 5106   | 请输入token                  |                    |
| 5136   | 请输入分享码                 |                    |


**响应参数**:


| 参数名称 | 参数说明   | 类型           | schema         |
| -------- | ---------- | -------------- | -------------- |
| code     | 响应状态码 | integer(int32) | integer(int32) |
| data     | 响应数据   | object         |                |
| extMsg   | 扩展消息   | string         |                |
| msg      | 响应消息   | string         |                |


**响应示例**:
```javascript
{
	"code": 0,
	"data": {},
	"extMsg": "",
	"msg": ""
}
```

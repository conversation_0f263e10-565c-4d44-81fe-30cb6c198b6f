# 增值服务 相关指令
## **相关AT指令列表**

| 指令名称                                | 功能描述     |
| --------------------------------------- | ------------ |
| [AT+QIOTNTPREQ](#AT+QIOTNTPREQ)         | 获取网络时间 |
| [AT+QIOTDATACOLLTX](#AT+QIOTDATACOLLTX) | 数采信息上报 |

## __相关AT指令详情__

<span id="AT+QIOTNTPREQ"> </span>

## <span style="color:#A52A2A">__AT+QIOTNTPREQ (获取网络时间)__</span>

* __说明__：该命令用于获取网络时间。
* __最大响应时间__ ：30 秒。

### **测试命令**

* __发送：__

  ```c
  AT+QIOTNTPREQ=?
  ```

* __响应：__

  响应支持的设置参数格式

  ```c
  OK
  ```

### **执行命令**

* __发送：__

  ```c
  AT+QIOTNTPREQ
  ```

* __响应：__

  若请求成功则响应以下内容

  ```c
  OK

  +QIOTEVT:4,10250,<time>,<zone>,<timestamp> 
  ```

  若请求失败

  ```c
  +QIOTEVT:4,10350
  ```

  若出现任何错误

  ```c
  ERROR
  ```

---

* __参数：__
  * __`<time>`__： 字符串类型。可绑定时间，格式为 YYYY-MM-DD hh:mm:ss week（年-月-日 时:分:秒 星期）。
  * __`<zone>`__： 字符串类型。时区。
  * __`<timestamp>`__： 符串类型。时间戳。

* __备注：__
    * __2.11.0及以上版本支持__

---

#### **示例**

__示例1 (设备端获取NTP网络时间)__

```c
[TX]AT+QIOTNTPREQ

[RX]OK

[RX]+QIOTEVT: 4,10250,"2023-02-07 10:29:54 2","+08:00","1675736994488"
```

---

<span id="AT+QIOTDATACOLLTX"> </span>

## <span style="color:#A52A2A">__AT+QIOTDATACOLLTX (数采信息上报)__</span>

* __说明__：该命令用于设备端上报数采信息。
* __最大响应时间__ ：300毫秒。

### **测试命令**

* __发送：__

```c
AT+QIOTDATACOLLTX=?
```

* __响应：__

```c
+QIOTDATACOLLTX: (0-1),(0-1048576),(1-1024)

OK
```

### **设置命令**

* __发送：__

  ```c
  AT+QIOTDATACOLLTX=<endFlag>,<pkgSortId>,<pkgLen>
  ```

* __响应：__

  若设置成功

  ```c
  >
  输入数据

  OK
  +QIOTEVT: 30,0,<pkgSortId>
  ```

  若出现任何错误

  ```c
  ERROR
  ```

  或

  ```c
  OK

  +QIOTEVT: 30,<errnoType>[,<pkgSortId>]
  ```

---

* __参数：__
  * __`<endFlag>`__： 整型，指示当前次发送的数据是否为最后一包。范围为：0 ~ 1。
    * __`0`__ : 不是最后一个包
    * __`1`__ : 最后一个包
  * __`<pkgSortId>`__： 整型，当前大数据分包发送的分包序号。范围为：0 ~ 1048576。
  * __`<pkgLen>`__： 整型，当前分包的包大小。范围为：1 ~ 1024。
  * __`<errnoType>`__： 整型，错误码，指示当前发送的数据的结果，详细请看如下表。范围为：0 ~ 20。

    | `<errnoType>`错误码值 |        功能描述        |
    | :-------------------: | :--------------------: |
    |           0           |      数据发送成功      |
    |           1           |      数据发送失败      |
    |           2           | 收到暂停发送，单位：秒 |
    |           3           |  数据发送失败，并重试  |
    |          10           |   连接数采服务器失败   |
    |          11           |    本次传输异常结束    |
    |          12           |     数采通道未开启     |
    |          13           |  连接服务失败，并重试  |
    |          20           |     数采通道已关闭     |

* __备注：__
    *  __2.18.0及以上版本支持__

---

#### **示例**

__示例1 (设备端上报数采信息到平台)__  

```c
[TX]AT+QIOTDATACOLLTX=1,0,5

[RX]>

[TX]hello

[RX]OK

[RX]+QIOTEVT: 30,0,0
```

---

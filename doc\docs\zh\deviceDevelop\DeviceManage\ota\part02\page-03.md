# 创建升级计划

对批量设备进行OTA升级，您需要在平台创建一个升级计划。 

本文主要介绍如何创建升级计划。

## 前提条件

1、 已添加用于升级的固件升级包。具体操作步骤，详见[**添加固件升级包**](/deviceDevelop/DeviceManage/ota/part02/page-01) 。

2、 固件升级包发起验证（可选）。具体操作步骤，详见[**发起固件验证**](/deviceDevelop/DeviceManage/ota/part02/page-02) 。

 

## 操作步骤

1、 登录开发者中心，在左侧导航栏，选择 **设备管理** ，点击**OTA升级**。

<a data-fancybox title="img" href="/zh/guide/ota/ota09.png">![img](/zh/guide/ota/ota09.png)</a>

2、 在 **升级计划** 面板页，点击 **新增升级计划**，配置计划基本参数。  

<a data-fancybox title="img" href="/zh/guide/ota/ota10.png">![img](/zh/guide/ota/ota10.png)</a>

<a data-fancybox title="img" href="/zh/guide/ota/ota101.png">![img](/zh/guide/ota/ota101.png)</a>


**参数说明：**



| **参数**              | **描述**                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  |
| --------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 计划名称              | 设置升级计划的名称，名称在用户账号下唯一，计划激活后不可修改。长度限制100个字符。<br />  **注：** 如果是企业用户，名称需在企业用户账号及其子账号下唯一。                                                                                                                                                                                                                                                                                                                                                                                  |
| 升级产品              | 选择升级设备的所属产品。<br />  **注：** 计划一旦保存成功，升级产品不可变更。                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
| 设置黑白名单          | ● 黑名单：在黑名单中的设备不允许被升级。 <br /> ● 白名单：只允许在白名单中的设备被升级。 <br /> ● 不设置黑、白名单，该产品下所有设备升级。 <br />  ● 同时设置黑、白名单，属于白名单且不在黑名单内设备可升级，黑名单优先级大于白名单优先级。<br />  **注：** 计划保存成功，黑白名单的修改需在计划详情中完成，已激活计划，不能修改。                                                                                                                                                                                                        |
| 时区                  | 此项必须配置。 <br />1）	计划时间以此时区为准。 <br />2）	平台只会在此时区的推送时段内主动推送升级计划。                                                                                                                                                                                                                                                                                                                                                                                                                                  |
| 计划时间              | 设置升级计划周期。 <br />  超过计划结束时间，计划自动结束，计划内未升级设备不可再升级。                                                                                                                                                                                                                                                                                                                                                                                                                                                   |
| 推送时段              | 配置平台主动推送升级计划的时间段，可选择性配置。  只能配置整点到整点，至少间隔1个小时。                                                                                                                                                                                                                                                                                                                                                                                                                                                   |
| 升级方式              | ●  用户确认升级：设备无法直接获取OTA升级计划，需App通过接口查询并上报确认升级后，平台才会推送升级任务给到设备进行OTA升级。  <br />  ●  静默升级：平台直接推送升级任务给到设备进行OTA升级。 <br />  **注：** 蓝牙设备暂只支持静默升级。                                                                                                                                                                                                                                                                                                    |
| 自动升级时段/推送时段 | 配置平台主动推送升级计划的时间段，可选择性配置，如未配置时段为24小时。只能配置整点到整点，至少间隔1个小时。<br />●	自动升级时段：设备如打开了自动更新开关，平台会在对应时区此时段内推送升级计划给用户自动确认升级的设备。<br />●	推送时段：平台会在对应时区此时间段内推送静默升级的计划给到设备；仅针对平台主动推送设置为是的计划。<br />例如：北京时间1月1日10:00创建了静默升级计划A，推送时段配置00:00~02:00，在1月2日00:00主动推送计划A给到设备进行升级，如设备已在升级中且已确认升级，推送将停止。 注：推送时段内，约10分钟推送一次。 |
| 平台主动推送          | ●	是：默认值，计划激活后，平台会主动向在线设备推送静默升级计划。<br />●	否：平台不会主动推送升级计划，需要设备主动向平台发起升级请求来获取升级计划。                                                                                                                                                                                                                                                                                                                                                                                      |
| 拒绝推送的有效时间    | 升级方式选择静默升级且需要平台主动推送，此项才需配置。<br />   ●	针对设备拒绝升级场景使用，设备因当前情况不适合升级并上报拒绝后，平台在拒绝时间之后再次推送升级任务，直到设备确认升级或计划过期/结束。<br />   ●	  配置时间范围为 10~1440 分钟（ 24 小时），默认120分钟； <br />   ●	   例如，将该参数设置为50分钟，设备10:00收到升级计划，若设备10:10拒绝升级，下一次平台主动推送计划给设备的时间11:00，具体以实际平台下发计划的时间为准；                                                                                               |
| 升级策略              | ● 重试次数：允许设备重试升级的次数。  <br />  ●   最低信号强度:设备上报的信号强度低于此值，则设备不会进行OTA升级。  <br />  ●   最低电池电量：设备上报电池电量低于此值，则设备不会进行OTA升级。  **注：** 最低信号强度和最低电池电量如需生效，需要设备具备此策略的校验逻辑。                                                                                                                                                                                                                                                              |

 

3、 升级组件及版本信息配置，一是可以选择现有计划的升级组件，复用。


<a data-fancybox title="img" href="/zh/guide/ota/ota11.png">![img](/zh/guide/ota/ota11.png)</a>


4、 二是添加新的升级组件，配置参数后，点击 **确定**，完成添加。

 

<a data-fancybox title="img" href="/zh/guide/ota/ota12.png">![img](/zh/guide/ota/ota12.png)</a>

<a data-fancybox title="img" href="/zh/guide/ota/ota13.png">![img](/zh/guide/ota/ota13.png)</a>

**参数说明：**

| **参数** | **描述**                                                                                                                                                                                                                                                                             |
| -------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| 组件类型 | 选择升级组件的类型。<br />  **注：** 子设备产品暂只支持模组固件升级。                                                                                                                                                                                                                |
| 组件标识 | 输入用于验证的设备模组固件或者MCU固件标识。<br />  **注：** 请确保此信息的正确性。如何获取设备此信息，AT指令接入方案，详见[**此处**](/deviceDevelop/DeviceAccessPlan/cellular/AT/API/cellular-at-03) ；Open接入方式，详见[**此处**](/deviceDevelop/DeviceAccessPlan/cellular/QuecOpen/api/cellular-quecopen-api-02) 。 |
| 升级方式 | 选择升级组件的升级方式。                                                                                                                                                                                                                                                             |
| 目标版本 | 选择此升级组件升级后的版本。 <br />  **注：** 升级方式选择整包升级，根据选择的目标版本匹配固件升级包。                                                                                                                                                                               |
| 指定版本 | 升级方式选择整包升级，此项可选择性配置。  支持输入英文、数字、下划线"_"、连接符"-"、英文句号"."、英文分号“;”。  最多可指定8个版本号，使用“;”分隔。  配置后设备版本号必须与指定版本号匹配才可升级。                                                                                   |
| 源版本   | 选择此升级组件升级前的版本。  升级方式选择差分包升级，此参数需配置。<br /> **注：** 升级方式选择差分包升级，根据选择的目标版本和源版本匹配固件升级包。                                                                                                                               |

 

5、 在升级组件列表可查看已添加的组件，组件升级顺序以组件添加顺序为准。

<a data-fancybox title="img" href="/zh/guide/ota/ota14.png">![img](/zh/guide/ota/ota14.png)</a>

 

6、 升级方式选择用户确认升级

a)  中国区的升级计划，中文升级文案必须配置；非中国区的升级计划，英文升级文案必须配置。

b)  其他语言文案可根据实际需求进行配置。

7、 升级方式选择静默升级，升级文案无需配置。

<a data-fancybox title="img" href="/zh/guide/ota/ota15.png">![img](/zh/guide/ota/ota15.png)</a>

8、 升级文案配置页，点击 **新增，** 选择对应语言以及输入升级文案，确定提交即可。

**注：** 如果当前支持的文案语言不能满足您的业务需求，可[**提交工单**](/personalCenter/WorkOrder) 反馈。

<a data-fancybox title="img" href="/zh/guide/ota/ota16.png">![img](/zh/guide/ota/ota16.png)</a>

9、 计划信息配置完成后，点击 **保存，** 计划进入保存状态，此时计划未激活，可以对计划进行编辑修改。

**注：** 升级产品除外。

10、     点击 **激活计划**，计划进入激活状态。此时计划不可编辑修改，计划中设备上线则会进入OTA升级流程。

**注：** 升级计划一旦激活，计划内所有信息均不可变更。

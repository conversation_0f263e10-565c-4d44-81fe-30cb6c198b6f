# 定时管理 - 云定时

## 功能概述

设备定时管理, 支持创建、修改、删除、查询定时任务列表、查询定时任务详情等操作。

```objc
#import <QuecDeviceKit/QuecDeviceKit.h>
/// 初始化
[QuecDeviceService sharedInstance]
```

## 定时管理

### 创建定时任务

**接口说明**

创建定时任务

```objc
- (void)addCornJobWithCornJobModel:(QuecCornJobModel *)cornJobModel 
                            success:(void(^)(NSString *ruleId))success
                            failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- |  
| cornJobModel | 是 | QuecCornJobModel类 |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**QuecCornJobModel属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| createTime  | NSInteger   | 创建时间 |
| productKey  | NSString   | 产品key |
| deviceKey  | NSString   | 设备key |
| ruleId  | NSString   | 规则唯一标识，修改规则实例信息时必填 |
| type  | NSString   | 定时任务类型，once: 执行一次，day-repeat: 每天重复，custom-repeat: 自定义重复，multi-section: 多段执行，random: 随机执行，delay: 延迟执行（倒计时） |
| enabled  | BOOL   | 定时任务状态：false-停止（默认） true-启动 |
| dayOfWeek  | NSString   | 周几执行：1-周一 2-周二 3-周三 4-周四 5-周五 6-周六 7-周日, 可以多选，传多个值时使用英文逗号分隔, 当 type = custom-repeat || multi-section || random 时必填 |
| timers  | NSArray<QuecCornTimersModel *> *   | 定时任务详细 |

**QuecCornTimersModel属性定义**

| 字段        | 类型                 | 描述                                                                                |
|-----------|--------------------|-----------------------------------------------------------------------------------|
| action  | NSString   | 定时任务执行的命令，格式：物模型的 json 字符串                                                        |
| delay  | NSInteger   | 延迟执行时间，单位为秒, 当 type = delay 时必填，单位为 s                                             |
| endTime  | NSString   | 当 type 为 random 时必填，格式为 "HH:mm:ss"，如 "12:00:00"                                   |
| startTime  | NSString   | 当 type 为 random 时必填，格式为 "HH:mm:ss"，如 "12:00:00"                                   |
| time  | NSString   | 执行时间，格式为 HH:mm:ss, 当 type = once / day-repeat / custom-repeat / multi-section 时必填 |
| timerId  | NSString   | 定时任务ID                                                                            |

**示例代码**

```objc
QuecCornJobModel *jobModel = QuecCornJobModel.new;
jobModel.productKey = @"";
jobModel.type = .....;
[QuecDeviceService.sharedInstance addCornJobWithCornJobModel:jobModel success:^(NSString *ruleId) {
        /// Next Action
    } failure:^(NSError *error) {
        NSLog(@"check error: %@", error);
    }];
```
### 修改定时任务

**接口说明**

修改定时任务

```objc
- (void)setCronJobWithCornJobModel:(QuecCornJobModel *)cornJobModel 
                           success:(void(^)(NSDictionary *data))success
                           failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- |  
| cornJobModel | 是 | QuecCornJobModel类 |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

>QuecCornJobModel属性说明同上【创建定时任务】

**示例代码**

```objc
QuecCornJobModel *jobModel = QuecCornJobModel.new;
jobModel.productKey = @"";
jobModel.type = .....;
[[QuecDeviceService sharedInstance] setCronJobWithCornJobModel:jobModel success:^(NSDictionary *data){
        /// Next Action
    } failure:^(NSError *error) {
        NSLog(@"check error: %@", error);
    }];
```


### 获取定时任务列表

**接口说明**

查询设备下定时任务列表

```objc
- (void)getCronJobListWithDeviceKey:(NSString *)deviceKey 
                          productKey:(NSString *)productKey
                                 type:(NSString *)type
                          pageNumber:(NSInteger)pageNumber
                            pageSize:(NSInteger)pageSize
                             success:(void(^)(NSArray<QuecCornJobModel *> *list, NSInteger total))success
                             failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- |  
| productKey | 是 | 产品key |
| deviceKey | 是 | 设备key |
| type | 是 | 定时任务类型，once: 执行一次，day-repeat: 每天重复，custom-repeat: 自定义重复，multi-section: 多段执行，random: 随机执行，delay: 延迟执行（倒计时） |
| pageNumber | 否 | 分页页码，默认: 1 |
| pageSize | 否 | 分页大小，默认: 10 |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

>回调QuecCornJobModel属性说明同上【创建定时任务】

**示例代码**

```objc
[[QuecDeviceService sharedInstance] getCronJobListWithDeviceKey:@"deviceKey" productKey:@"productKey type:@"type" pageNumber:page pageSize:pageSize success:^(NSArray<QuecCornJobModel *> *list, NSInteger total) {
        /// Next Action
    } failure:^(NSError *error) {
        NSLog(@"check error: %@", error);
    }];
```

### 定时任务详情

**接口说明**

查询定时任务详情

```objc
- (void)getCronJobInfoWithRuleId:(NSString *)ruleId 
                          success:(void(^)(QuecCornJobModel *cornJobModel))success
                          failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- |  
| ruleId | 是 | 定时任务ID |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

>回调QuecCornJobModel属性说明同上【创建定时任务】

**示例代码**

```objc
[[QuecDeviceService sharedInstance] getCronJobInfoWithRuleId:@"ruleId" success:^(QuecCornJobModel *model) {
        /// Next Action
    } failure:^(NSError *error) {
        NSLog(@"check error: %@", error);
    }];
```

### 批量删除定时任务

**接口说明**

批量删除定时任务

```objc
- (void)batchDeleteCronJobWithParams:(NSDictionary *)params 
                               success:(void(^)(QuecCornJobDeleteListModel *model))success
                               failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- | 
| params | 是 | ruleIdList:[String 定时任务ID数组] |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	|  

**QuecCornJobDeleteListModel属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| successList  | NSArray<QuecCornJobDeleteModel *> *   | 成功列表 |
| failureList  | NSArray<QuecCornJobDeleteModel *> *   | 失败列表 |

**QuecCornJobDeleteModel属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| code  | NSString  | code码 |
| msg  | NSString  | 提示 |
| ruleId  | NSString   | 定时任务ID |

**示例代码**

```objc
[QuecDeviceService.sharedInstance batchDeleteCronJobWithParams:@{ruleIdList:@[@"1"， @"2"]} success:^(QuecCornJobDeleteListModel *model) {
        /// Next Action
    } failure:^(NSError *error) {
        NSLog(@"check error: %@", error);
    }];
```

### 查询定时任务限制数

**接口说明**

查询产品下定时任务限制数

```objc
- (void)getProductCornJobLimitWithProductKey:(NSString *)productKey 
                                       success:(void(^)(NSInteger limit))success
                                       failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- |  
| productKey | 是 | 产品pk |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	|  

**示例代码**

```objc
[[QuecDeviceService sharedInstance] getProductCornJobLimitWithProductKey:@"productKey" success:^(NSInteger limit) {
        /// Next Action
    } failure:^(NSError *error) {
        NSLog(@"check error: %@", error);
    }];
```

# 设备分享

## 功能概述

设备分享相关功能，您可以通过设备分享相关功能，实现设备分享到其他用户，实现设备分享到其他用户后，其他用户可以控制您的设备。

> QuecDeviceModel属性定义参考[设备管理](设备管理.md)

```kotlin
QuecDeviceShareService
```

## 设备分享

### 分享码查询设备信息

**接口说明**

通过分享码查询设备信息

```kotlin
fun getDeviceInfoByShareCode(
    shareCode: String,
    callback: QuecCallback<QuecDeviceModel>
)
```

**参数说明**

| 参数        | 	是否必传 | 说明   |	
|-----------|-------|------|  
| shareCode | 是     | 分享码  |
| callback  | 是     | 请求回调 |

**示例代码**

```kotlin
QuecDeviceShareService.getDeviceInfoByShareCode("code") {
    if (it.isSuccess) {
        val data = it.data //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 更改分享设备名称

**接口说明**

更改分享设备名称

```kotlin
fun updateDeviceNameByShareUser(
    deviceName: String,
    shareCode: String,
    callback: QuecCallback<Unit>
)
```

**参数说明**

| 参数         | 	是否必传 | 说明   |	
|------------|-------|------|  
| deviceName | 是     | 设备名称 |
| shareCode  | 是     | 分享码  |
| callback   | 是     | 请求回调 |

**示例代码**

```kotlin
QuecDeviceShareService.updateDeviceNameByShareUser("name", "code") {
    if (it.isSuccess) {
        //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 设备分享人列表

**接口说明**

获取设备分享人列表

```kotlin
fun getDeviceShareUserList(
    deviceKey: String,
    productKey: String,
    callback: QuecCallback<List<QuecShareUserModel>>
)
```

**参数说明**

| 参数         | 	是否必传 | 说明   |	
|------------|-------|------|  
| deviceKey  | 是     | 设备dk |
| productKey | 是     | 产品pk |
| callback   | 是     | 请求回调 |

**QuecShareUserModel属性定义**

| 字段        | 类型                          | 描述   |
|-----------|-----------------------------|------|
| shareInfo | QuecShareUserShareInfoModel | 分享信息 |
| userInfo  | QuecShareUserInfoModel      | 用户信息 |

**QuecShareUserShareInfoModel属性定义**

| 字段                | 类型     | 描述     |
|-------------------|--------|--------|
| acceptTime        | String | 分享时间   |
| acceptingExpireAt | String | 分享失效时间 |
| coverMark         | int    | 标记     |
| deleteTime        | String | 删除时间   |
| deviceName        | String | 设备名称   |
| dk                | String | 设备key  |
| ownerUid          | String | 所有者ID  |
| pk                | String | 产品key  |
| shareCode         | String | 分享码    |
| shareId           | String | 分享ID   |
| shareStatus       | int    | 分享状态   |
| shareTime         | String | 分享时间   |
| shareUid          | String | 分享用户ID |
| sharingExpireAt   | String | 分享失效时间 |
| updateTime        | String | 更新时间   |

**QuecShareUserInfoModel属性定义**

| 字段            | 类型     | 描述     |
|---------------|--------|--------|
| address       | String | 地址     |
| email         | String | 邮箱     |
| headimg       | String | 头像     |
| lastLoginTime | String | 最后登录时间 |
| nikeName      | String | 昵称     |
| phone         | String | 手机号码   |
| registerTime  | String | 授权时间   |
| sex           | String | 性别     |
| uid           | String | 用户ID   |
| userDomain    | String | 用户域    |
| userType      | int    | 用户来源   |
| wchartId      | String | 微信ID   |
| wchartName    | String | 微信名称   |

**示例代码**

```kotlin
QuecDeviceShareService.getDeviceShareUserList("deviceKey", "productKey") {
    if (it.isSuccess) {
        val data = it.data //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 取消分享

**接口说明**

分享人取消分享

```kotlin
fun unShareDeviceByOwner(
    shareCode: String,
    callback: QuecCallback<Unit>
)
```

**参数说明**

| 参数        | 	是否必传 | 说明   |	
|-----------|-------|------|  
| shareCode | 是     | 分享码  |
| callback  | 是     | 请求回调 |

**示例代码**

```kotlin
QuecDeviceShareService.unShareDeviceByOwner("code") {
    if (it.isSuccess) {
        //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 被分享人取消分享

**接口说明**

被分享人移除已接收分享的设备

```kotlin
fun unShareDeviceByShareUser(
    shareCode: String,
    callback: QuecCallback<Unit>
)
```

**参数说明**

| 参数        | 	是否必传 | 说明   |	
|-----------|-------|------|  
| shareCode | 是     | 分享码  |
| callback  | 是     | 请求回调 |

**示例代码**

```kotlin
QuecDeviceShareService.unShareDeviceByShareUser("code") {
    if (it.isSuccess) {
        //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 接受分享

**接口说明**

被分享人接受分享

```kotlin
fun acceptShareByShareUser(
    shareCode: String,
    deviceName: String?,
    callback: QuecCallback<Unit>
)
```

**参数说明**

| 参数         | 	是否必传 | 说明   |	
|------------|-------|------|  
| shareCode  | 是     | 分享码  |
| deviceName | 是     | 设备名称 |
| callback   | 是     | 请求回调 |

**示例代码**

```kotlin
QuecDeviceShareService.acceptShareByShareUser("code", "name") {
    if (it.isSuccess) {
        //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 分享人设置分享信息

**接口说明**

分享人设置分享信息

```kotlin
fun setShareInfoByOwner(
    deviceKey: String,
    productKey: String,
    acceptingExpireTime: Long,
    coverMark: Int,
    isSharingAlwaysValid: Boolean,
    sharingExpireTime: Long,
    callback: QuecCallback<String>
)
```

**参数说明**

| 参数                   | 	是否必传 | 说明                                                                   |	
|----------------------|-------|----------------------------------------------------------------------|  
| deviceKey            | 是     | 设备dk                                                                 |
| productKey           | 是     | 产品pk                                                                 |
| acceptingExpireTime  | 是     | 分享二维码种子失效时间 时间戳（毫秒），表示该分享在此时间戳时间内没有使用，会失效                            |
| coverMark            | 是     | 覆盖标志:1 直接覆盖上条有效分享（默认）（覆盖原有的分享码）；2 直接添加，允许多条并存；3 只有分享时间延长了，才允许覆盖上条分享  |
| isSharingAlwaysValid | 否     | 设备是否永久有效                                                             |
| sharingExpireTime    | 否     | 设备使用到期时间 时间戳（毫秒），表示该分享的设备，被分享人可以使用的时间，isSharingAlwaysValid为YES时该参数无效 |
| callback             | 是     | 请求回调                                                                 |

**示例代码**

```kotlin
QuecDeviceShareService.setShareInfoByOwner(
    "deviceKey",
    "productKey",
    0,
    1,
    true,
    System.currentTimeMillis() + 60000
) {
    if (it.isSuccess) {
        val data = it.data //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

# Hardware Development

This section provides tailored development guides based on your product's connection methods.

## Development Resources

Available development methods for each product connection method are as follows:

| **Network Connection Method** | **Development Method**                                   |
| ----------------------------- | -------------------------------------------------------- |
| 2G/3G/4G/5G                   | QuecOpen <br />QuecPython <br />AT Commands <br />Modbus |
| WiFi                          | QuecOpen <br />Turnkey (supported by specific solutions) |
| NB-IoT                        | QuecOpen <br />QuecPython <br />AT Commands              |
| Bluetooth                     | QuecOpen                                                 |

<a data-fancybox title="img" href="/en/deviceDevelop/creatproduct/devicedevelop02.png">![img](/en/deviceDevelop/creatproduct/devicedevelop02.png)</a>

After selecting the development method, the corresponding development guide and resource download links will be displayed on the page.

Development Guide: includes guidance documents, API documents, examples, and help information. You can connect the device to Developer Center quickly according to guidance documents.

Resources: include the materials required for developing the selected module. Please select your specific module model to download the required content by clicking on the download link.

## MCU SDK

When an AT command solution is selected, Developer Center supports generating MCU SDK based on the released TSL model of the current product.

<a data-fancybox title="img" href="/en/deviceDevelop/creatproduct/devicedevelop01.png">![img](/en/deviceDevelop/creatproduct/devicedevelop01.png)</a>

After selecting the hardware platform, click "**Next**" and wait for the code package to be generated.

After the code package is generated, you can download it to a local server and add the SDK to your project files for immediate use.

For detailed instructions on how to use the SDK, please refer to the MCU SDK application note documents in the *doc* directory of the code package.


# 发布Android应用（海外版）

## 开通一个开发者账号

  **1. 注册Google账号（如已有Google账号可跳过此步骤直接登录）**：[https://accounts.google.com/SignUp](https://accounts.google.com/SignUp)

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/googlestore/googlestore1.png">![image](/zh/appDevelop/oemapp/googlestore/googlestore1.png)</a>

推荐使用Gmail，如果想用自己的邮箱点击**改用我的当前电子邮件地址**。填写完成之后点击下一步，同意协议，验证手机号、邮箱之后，即可登录。账号首页地址为[https://myaccount.google.com/](https://myaccount.google.com/)

  **2.开通开发者权限** ：[https://play.google.com/apps/publish/signup/](https://play.google.com/apps/publish/signup/)

  官方指引 [https://support.google.com/accounts/answer/27441?hl=zh-Hans&amp;sjid=9464741943980018757-AP](https://support.google.com/accounts/answer/27441?hl=zh-Hans&sjid=9464741943980018757-AP)

## 应用创建

 官方指导文档 [https://support.google.com/googleplay/android-developer/answer/9859152?hl=zh-Hans&amp;ref_topic=7072031&amp;sjid=2374854195900752047-AP](https://support.google.com/googleplay/android-developer/answer/9859152?hl=zh-Hans&ref_topic=7072031&sjid=2374854195900752047-AP)

 访问[https://play.google.com/apps/publish/](https://play.google.com/apps/publish/) ，应该能看见如下的界面，点击**创建应用**

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/googlestore/googlestore102.png">![image](/zh/appDevelop/oemapp/googlestore/googlestore102.png)</a>

 选择App的 **默认语言** ，填写App名称，点击**创建应用**即可进入详情页

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/googlestore/googlestore103.png">![image](/zh/appDevelop/oemapp/googlestore/googlestore103.png)</a>

 会列出一些必填的选项，全部完成才能发布版本

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/googlestore/googlestore104.png">![image](/zh/appDevelop/oemapp/googlestore/googlestore104.png)</a>

 **1. 隐私政策**

填入App的隐私政策URL地址

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/googlestore/googlestore105.png">![image](/zh/appDevelop/oemapp/googlestore/googlestore105.png)</a>

 **2. 广告**

根据App是否有广告据实选择

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/googlestore/googlestore106.png">![image](/zh/appDevelop/oemapp/googlestore/googlestore106.png)</a>

 **3. 应用访问权限**

根据App是否需要登录选择

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/googlestore/googlestore107.png">![image](/zh/appDevelop/oemapp/googlestore/googlestore107.png)</a>

如果需要登录，那么要提供一个测试账号

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/googlestore/googlestore109.png">![image](/zh/appDevelop/oemapp/googlestore/googlestore109.png)</a>

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/googlestore/googlestore110.png">![image](/zh/appDevelop/oemapp/googlestore/googlestore110.png)</a>

 **4. 内容分级**

 填写电子邮箱，选择应用的类别

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/googlestore/googlestore108.png">![image](/zh/appDevelop/oemapp/googlestore/googlestore108.png)</a>

 开始填写应用内有无暴力、令人恐惧的内容等，选否就对了

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/googlestore/googlestore117.png">![image](/zh/appDevelop/oemapp/googlestore/googlestore117.png)</a>

 **5. 目标受众群体和内容**

 根据App内容选择年龄段，多选

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/googlestore/googlestore111.png">![image](/zh/appDevelop/oemapp/googlestore/googlestore111.png)</a>

 **6. 新闻应用**

 根据App是否是新闻应用选择

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/googlestore/googlestore112.png">![image](/zh/appDevelop/oemapp/googlestore/googlestore112.png)</a>

 **7. 新冠 (COVID-19) 接触者追踪应用和感染状况应用**

 根据App是否是新冠应用勾选

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/googlestore/googlestore113.png">![image](/zh/appDevelop/oemapp/googlestore/googlestore113.png)</a>

 **8. 数据安全**

 填写App数据安全，参考文档[《Google Play：APP数据安全信息填写合规指引》](/appDevelop/OEMApp/app/google-data-inof)

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/googlestore/googlestore114.png">![image](/zh/appDevelop/oemapp/googlestore/googlestore114.png)</a>

 **9. 广告 ID**

 根据App使用广告情况据实选择

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/googlestore/googlestore115.png">![image](/zh/appDevelop/oemapp/googlestore/googlestore115.png)</a>

 **10. 政府应用**

 根据App是否是政府应用据实选择

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/googlestore/googlestore116.png">![image](/zh/appDevelop/oemapp/googlestore/googlestore116.png)</a>

 **11. 商品详情**

 填写App名称、说明、应用图标、置顶大图等信息

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/googlestore/googlestore118.png">![image](/zh/appDevelop/oemapp/googlestore/googlestore118.png)</a>

 **12. 上传App版本**

 上传App的aab包，设置版本名称和版本说明等

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/googlestore/googlestore119.png">![image](/zh/appDevelop/oemapp/googlestore/googlestore119.png)</a>

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/googlestore/googlestore120.png">![image](/zh/appDevelop/oemapp/googlestore/googlestore120.png)</a>

 签名密匙选择默认模式

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/googlestore/googlestore121.png">![image](/zh/appDevelop/oemapp/googlestore/googlestore121.png)</a>

  发布国家根据App范围选择，可以全选

## 应用发布

 当上面的都填完，就可以发布了。第一次上架新的应用审核会比较慢，一般48小时之内会收到邮件通知。

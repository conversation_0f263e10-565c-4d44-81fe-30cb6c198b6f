# 产品交互

当您使用移联万物App服务或OEM App服务时，可通过产品交互模块对App中的设备详情页进行配置。

开发者中心默认提供通用面板，会根据产品已定义的物模型功能动态生成控制页面，可用于设备调试与简单的使用。

除了通用面板外，开发者中心对部分品类提供了品类面板。您可选择使用已有的品类面板或者联系我们开发企业定制面板。定制面板是您独有的控制页面，可为您的客户提供更好的产品体验。定制面板更加匹配您的产品功能、更贴合品牌风格，同时可通过差异个性化提高竞争力。

## 切换面板

点击切换按钮，可选择其他不同类型的控制面板。

<a data-fancybox title="img" href="/zh/deviceDevelop/creatproduct/interaction01.png">![img](/zh/deviceDevelop/creatproduct/interaction01.png)</a>

面板切换完成后可开启授权移联万物App与虚拟设备功能，使用App扫描虚拟二维码绑定后可进行体验。

<a data-fancybox title="img" href="/zh/deviceDevelop/creatproduct/interaction02.png">![img](/zh/deviceDevelop/creatproduct/interaction02.png)</a>

需注意面板功能通常与物模型定义相关联，若两者不一致时可能会导致无法通过面板控制设备的情况出现。

## 面板多语言

若产品有出海需求，开发者中心支持对所选面板配置不同语种的文案内容，可点击配置按钮进入配置页面。

<a data-fancybox title="img" href="/zh/deviceDevelop/creatproduct/interaction03.png">![img](/zh/deviceDevelop/creatproduct/interaction03.png)</a>

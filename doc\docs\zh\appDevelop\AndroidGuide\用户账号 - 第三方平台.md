# 第三方平台

本文介绍了如何使用第三方方式进行登录的功能

## 功能概述

```kotlin
QuecUserService
```

### 一键登录

**接口说明**

一键登录入参QuecOneKeyLoginModel数据模型, 所有参数都必填

```kotlin
fun oneKeyLogin(model: QuecOneKeyLoginModel, callback: QuecCallback<Unit>)
```

**QuecOneKeyLoginModel属性定义**

| 参数          | 	类型    | 说明                                 |
|-------------|--------|------------------------------------|
| appid       | String | 中国移动开放平台申请appid                    |
| msgid       | String | uuid                               |
| strictcheck | String | 0：不对服务器ip白名单进行强校验,1：对服务器ip白名单进行强校验 |
| systemtime  | String | 系统时间 yyyyMMddHHmmssSSS             |
| appSecret   | String | 中国移动开放平台申请appSecret                |
| loginToken  | String | 获取权限后移动SDK返回token                  |
| version     | String | 版本                                 |

**示例代码**

```kotlin
QuecUserService.oneKeyLogin(
    QuecOneKeyLoginModel(
        "xxx",
        "xxx",
        "xxx",
        "xxx",
        "0",
        "20250421154210030",
        "2.0"
    )
) {
    if (it.isSuccess) {
        //一键登录成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 第三方用户登录

**接口说明**

用于第三方用户登录

```kotlin
fun loginByAuthCode(authCode: String, callback: QuecCallback<Unit>)
```

**参数说明**

| 参数       | 	是否必传 | 说明     |
|----------|-------|--------|
| authCode | 是     | 授权code |
| callback | 是     | 请求回调   |

**示例代码**

```kotlin
QuecUserService.loginByAuthCode("authCode") {
    if (it.isSuccess) {
        //登录成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

---
title: "设备关联"
description: "设备关联相关文档"
---

# 设备关联

设备关联可以将多个设备关联在一起，如：主设备是智能插座，从设备是智能开关。

## 功能概述

> QuecDeviceModel属性定义参考[设备管理](设备管理.md)

```kotlin
QuecDeviceAssociateService
```

## 设备关联管理

### 绑定设备

**接口说明**

设备关联管理 - 绑定设备

```kotlin
fun deviceAssociation(
    list: List<QuecDeviceModel>,
    masterDevice: QuecDeviceModel,
    callback: QuecCallback<Unit>
)
```

**参数说明**

| 参数           | 	是否必传 | 说明                              |	
|--------------|-------|---------------------------------|  
| list         | 是     | 需要关联的从设备列表, QuecDeviceModel元素数组 |
| masterDevice | 是     | 主设备                             |
| callback     | 是     | 请求回调                            |

**示例代码**

```kotlin
val subDeviceList = listOf(
    QuecDeviceModel("productKey", "deviceKey1"),
    QuecDeviceModel("productKey", "deviceKey2")
)
val masterDevice = QuecDeviceModel("productKey", "deviceKey3")

QuecDeviceAssociateService.deviceAssociation(subDeviceList, masterDevice) {
    if (it.isSuccess) {
        //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 关联关系查询

**接口说明**

设备关联管理 - 关联关系查询

```kotlin
fun getDeviceAssociationInfo(
    masterDevice: QuecDeviceModel,
    code: String?,
    callback: QuecCallback<QuecDeviceAssociationModel>
)
```

**参数说明**

| 参数           | 	是否必传 | 说明                          |	
|--------------|-------|-----------------------------|  
| masterDevice | 是     | 主设备 QuecDeviceModel类型       |
| code         | 是     | 要查询的属性标识符，如果查询多个属性，使用英文逗号分隔 |
| callback     | 是     | 请求回调                        |

> QuecDeviceModel属性定义同上

**QuecDeviceAssociationModel属性定义**

| 字段              | 类型                                       | 描述      |
|-----------------|------------------------------------------|---------|
| slaveDeviceList | List&lt;QuecDeviceAssociationSlaveModel> | 从设备数据列表 |
| masterDevice    | QuecDeviceAssociationMasterModel         | 主设备数据   |

**QuecDeviceAssociationSlaveModel属性定义**

| 字段                        | 类型                      | 描述    |
|---------------------------|-------------------------|-------|
| deviceAssociationMasterId | int                     | 主设备id |
| deviceAssociationSlaveId  | int                     | 从设备id |
| slaveDeviceKey            | String                  | 从设备dk |
| slaveDeviceName           | String                  | 从设备名  |
| slaveProductKey           | String                  | 从设备pk |
| tslDeviceDetail           | QuecProductTSLInfoModel | 物模型   |

> QuecProductTSLInfoModel属性定义同上

**QuecDeviceAssociationMasterModel属性定义**

| 字段                        | 类型     | 描述                               |
|---------------------------|--------|----------------------------------|
| deviceAssociationMasterId | int    | 主设备id                            |
| masterProductKey          | String | 主设备pk                            |
| masterDeviceKey           | String | 主设备dk                            |
| masterDeviceName          | String | 主设备名                             |
| siddhiName                | String | 规则名称。全局唯一，由大小写英文字母和阿拉伯数字组成，长度32位 |

**示例代码**

```kotlin
val masterDevice = QuecDeviceModel("productKey", "deviceKey3")
QuecDeviceAssociateService.getDeviceAssociationInfo(masterDevice, "code") {
    if (it.isSuccess) {
        val data = it.data //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 关联关系解除

**接口说明**

设备关联管理 - 关联关系解除

```kotlin
fun deviceDisassociation(
    model: QuecDeviceModel,
    callback: QuecCallback<Unit>
)
```

**参数说明**

| 参数       | 	是否必传 | 说明                          |	
|----------|-------|-----------------------------|  
| model    | 是     | 需要解除绑定设备, QuecDeviceModel类型 |
| callback | 是     | 请求回调                        |

> QuecDeviceModel属性定义同上

**示例代码**

```kotlin
val device = QuecDeviceModel("productKey", "deviceKey3")
QuecDeviceAssociateService.deviceDisassociation(device) {
    if (it.isSuccess) {
        //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 关联关系配置

**接口说明**

设备关联管理 - 关联关系配置

```kotlin
fun deviceAssociationConfig(
    productKey: String,
    callback: QuecCallback<QuecDeviceAssociationConfig>
)
```

**参数说明**

| 参数         | 	是否必传 | 说明   |	
|------------|-------|------|  
| productKey | 是     | 产品pk |
| callback   | 是     | 请求回调 |

**QuecDeviceAssociationConfig属性定义**

| 字段               | 类型              | 描述       |
|------------------|-----------------|----------|
| productKey       | String          | 主设备产品Key |
| masterItemCode   | String          | 主设备品类编码  |
| slaveItemCode    | String          | 从设备品类编码  |
| slaveDeviceLimit | int             | 从设备数量上限  |
| slaveProductKey  | List&lt;String> | 从设备pk数组  |

**示例代码**

```kotlin
QuecDeviceAssociateService.deviceAssociationConfig("pk") {
    if (it.isSuccess) {
        val data = it.data //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

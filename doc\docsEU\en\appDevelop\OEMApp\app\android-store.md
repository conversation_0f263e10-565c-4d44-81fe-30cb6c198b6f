# Publish Android App (Chinese Mainland Version)

Since the Tencent MyApp only has a Chinese version, this section describes the page information in a "Chinese(English translation)" format, so that even if you are not fluent in Chinese, you can still launch your app on Tencent MyApp.

#### Go to [Tencent Open Platform](https://open.tencent.com) > "管理中心(Management Center)" and log in to Tencent MyApp by clicking "登录 (Log in)" in the upper right corner.

<a data-fancybox title="img" href="/en/appDevelop/oemapp/androidstore/store1.png">![image](/en/appDevelop/oemapp/androidstore/store1.png)</a>

#### Click "创建应用(Create App)" to create an Android app.

<a data-fancybox title="img" href="/en/appDevelop/oemapp/androidstore/store2.png">![image](/en/appDevelop/oemapp/androidstore/store2.png)</a>

#### Verify the App ID and App Key and Click "完善基础信息(Complete Basic Information)" to complete the app information.

<a data-fancybox title="img" href="/en/appDevelop/oemapp/androidstore/store3.png">![image](/en/appDevelop/oemapp/androidstore/store3.png)</a>
<a data-fancybox title="img" href="/en/appDevelop/oemapp/androidstore/store4.png">![image](/en/appDevelop/oemapp/androidstore/store4.png)</a>

#### Complete "基础信息(Basic Information)" 

* 应用名称(App Name): Your app name.
* 应用类型(App Category): "软件(Software)".
* 应用子分类(Subcategory): Select a specific industry category.
* 应用提供方(App Provider): Enterprise name, which must be consistent with the enterprise name when registering the enterprise account.
* 应用研发方(App Developer): Developer name, which must be consistent with the subject of the app privacy policy.
* 应用简介(App Description): Describe your app in 60 to 500 Chinese characters.
* 一句话简介(App Introduction): Briefly introduce your app in 5 to 15 Chinese characters.
* 年龄分级 (Target User): Select the target user scope of your app. "18周岁+(Over 18 years old)" is recommended.

<a data-fancybox title="img" href="/en/appDevelop/oemapp/androidstore/store5.png">![image](/en/appDevelop/oemapp/androidstore/store5.png)</a>

#### Upload "图标素材(Icon)" 

* 应用图标(App Icon): Upload your app icon based on the requirements.
* 应用截图(App Screenshot): Upload screenshots of your app or upload screenshots made for the app store according to the size.
* 介绍视频(Introduction Video): Optional.

<a data-fancybox title="img" href="/en/appDevelop/oemapp/androidstore/store6.png">![image](/en/appDevelop/oemapp/androidstore/store6.png)</a>

#### Select "适配信息(Adaption Information)"

* 支持屏幕大小(Supported Screen Size): "全部(All)" is recommended.
* 支持语言(Supported Language): "中文(Chinese)" is recommended.
* 支持IPv6(Support IPv6): "不支持(No)" is recommended.
* 设备信息(Supported Device Type): "Phone" is recommended.

<a data-fancybox title="img" href="/en/appDevelop/oemapp/androidstore/store7.png">![image](/en/appDevelop/oemapp/androidstore/store7.png)</a>

#### Upload "资质证明(Qualification Certificate)"

<a data-fancybox title="img" href="/en/appDevelop/oemapp/androidstore/store8.png">![image](/en/appDevelop/oemapp/androidstore/store8.png)</a>

**Copyright Certificate:**

1. Upload the "Computer Software Copyright Registration Certificate" **[Note]** You must upload a Computer Software Copyright Registration Certificate to Tencent MyApp to pass the review. If there are any changes to the copyright, please re-upload it on the Tencent MyApp as soon as possible to avoid the app being taken down or the account being banned due to the old copyright becoming invalid.

<a data-fancybox title="img" href="/en/appDevelop/oemapp/androidstore/store9.png">![image](/en/appDevelop/oemapp/androidstore/store9.png)</a>

2. Upload the screenshot of the security audit approval.

<a data-fancybox title="img" href="/en/appDevelop/oemapp/androidstore/store10.png">![image](/en/appDevelop/oemapp/androidstore/store10.png)</a>

3. Upload the screenshot of the ICP registration and recording.

<a data-fancybox title="img" href="/en/appDevelop/oemapp/androidstore/store11.png">![image](/en/appDevelop/oemapp/androidstore/store11.png)</a>

4. Upload the "Human Resources Service License". If you have plans to operate recruitment-related content in the app in the future, you need to apply for this license or have the qualification authorization. Otherwise, your app will be restricted from being displayed on Tencent MyApp.

#### Upload "隐私限制(Privacy Restriction)”

1. 隐私权限(Privacy Permission): Upload screenshots of privacy permissions (including the entry page and pop-up). The image should be within 2 MB and in JPG or PNG formats. You can install the official package and take screenshots of the corresponding pages.

<a data-fancybox title="img" href="/en/appDevelop/oemapp/androidstore/store12.png">![image](/en/appDevelop/oemapp/androidstore/store12.png)</a>

Example of privacy permission screenshots:

<a data-fancybox title="img" href="/en/appDevelop/oemapp/androidstore/store13.png">![image](/en/appDevelop/oemapp/androidstore/store13.png)</a>

2. 隐私政策(Privacy Policy): Open the privacy policy URL, copy and paste the privacy policy content.

<a data-fancybox title="img" href="/en/appDevelop/oemapp/androidstore/store14.png">![image](/en/appDevelop/oemapp/androidstore/store14.png)</a>

Note: 1) Do not paste the privacy policy URL. 2)  If your app has been rejected due to privacy reasons, you must upgrade the version number. If the version number remains the same, Tencent MyApp will not recheck the privacy policy and the app will still be rejected for the previous reasons.

<a data-fancybox title="img" href="/en/appDevelop/oemapp/androidstore/store15.png">![image](/en/appDevelop/oemapp/androidstore/store15.png)</a>

#### Upload "安全评估报告(Security Assessment Report)"

1. Upload the "Internet Information Service Security Assessment" and screenshots of the security assessment approval. Since there are multiple pages, you can combine multiple screenshots into one image and then upload it.
2. Upload the "Security Commitment Letter". After filling in the commitment letter, stamp it and then take a photo to upload. Since there are multiple pages, you can combine multiple photos into one image and then upload it.

<a data-fancybox title="img" href="/en/appDevelop/oemapp/androidstore/store16.png">![image](/en/appDevelop/oemapp/androidstore/store16.png)</a>

#### After completing the above information, click "保存(Save)" in the upper right corner.

<a data-fancybox title="img" href="/en/appDevelop/oemapp/androidstore/store17.png">![image](/en/appDevelop/oemapp/androidstore/store17.png)</a>

#### Apply for Launch

<a data-fancybox title="img" href="/en/appDevelop/oemapp/androidstore/store18.png">![image](/en/appDevelop/oemapp/androidstore/store18.png)</a>

1. 版本详情说明(Version Description): You can refer to the Android release note to edit the version description. Note: The version description is for ordinary users, so the description should be easy to understand.
2. 正式包(Official Package): Upload the installation package. Upload the 32-bit package and 64-bit package separately.
3. 是否登录(Login Required): The login capability of app accounts. "是(Yes)" is recommended. Please provide an account and password that can normally log in to the app. Note: Do not submit an administrator account.
4. 是否涉及支付(Payment Involved): Select the corresponding option according to the app type. "含支付（Yes）" is recommended.
5. 发布类型(Release Type): You can select "审核通过后立即发布(Release Immediately After Approval)" or "定时发布(Release at a Specified Time)".
6. Check "允许平台为我的应用提供最终协议(Allow Tencent MyApp to provide the final agreement for my app)"

#### After submitting for review, you can check the status in the "管理中心(Management Center)" after 1–3 days.

If the "状态(App Status)" is "已上线(Online)", it means the app has been successfully launched on the Tencent MyApp.

<a data-fancybox title="img" href="/en/appDevelop/oemapp/androidstore/store19.png">![image](/en/appDevelop/oemapp/androidstore/store19.png)</a>

Note: If the review is not passed, please modify the submitted information according to the feedback or contact the customer service of Tencent MyApp for help.

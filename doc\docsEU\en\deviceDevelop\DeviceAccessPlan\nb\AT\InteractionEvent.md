# Events Related to Interaction Between the Device and Dev<PERSON>per Center

## **Event Callback Format：**

> __+QIOTEVT: \<event_type\>,\<event_code\> [,\<data\>]__

## **Callback Event Description**

- __`<event_type>`__ : Integer type. Event identifier.
  - __`1`__ : Bootstrap Authentication Event
  - __`2`__ : Access Event
  - __`3`__ : Subscription Event
  - __`4`__ : Data Sending Event
  - __`5`__ : Data Receiving Event
  - __`6`__ : Deregistration Event
  - __`7`__ : OTA Event
  - __`8`__ : Developer Center Event
  - __`9`__: Wake-up Event
- **`<event_code>`**: Integer type. Event return code.

## __Event Return Code: <event_type><event_code>：__

## __1：Bootstrap Authentication Event__

| Event ID, Event Code |                                                  Description                                                  |                                                                                                                   Reason and Treatment                                                                                                                   |
| :---------: | :------------------------------------------------------------------------------------------------------------: | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------: |
|   1,10200   |                Successful device authentication.<br>Sub-device：URC is followed by\<subDS>                |                                                                                                                                                                                                                                                          |
|   1,10300   |                                                 Other errors.                                                 |                                                                                                                                                                                                                                                          |
|   1,10404   |                              An error occurred when Developer Center called the API.                              |                                                                            Device internal error. Please contact IoT IoT platform and software solution Product Dept FAE to capture logs for analysis.                                                                            |
|   1,10421   | Static devices cannot re-register. |                                                                              Execute the correct command to configure DeviceSecret of the device with static authentication.                                                                              |
|   1,10422   |                                  The device is authenticated (Connection failed).                                  |                                                                                        Incorrect DeviceSecret. Please reset the DeviceSecret on Developer Center.                                                                                        |
|   1,10423   |                          Failed to query the product information (Connection failed).                          |                                                                                                    Incorrect ProductKey. Please check the ProductKey.                                                                                                    |
|   1,10424   |                                 Failed to decode PAYLOAD (Connection failed).                                 |    Possible reason 1: Incorrect ProductKey. Please check the ProductKey.<br> Possible reason 2: Failed to decode the DeviceSecret. Please check the consistency between the module DeviceSecret and the DeviceSecret assigned by Developer Center.    |
|   1,10425   |                               Signature verification failed (Connection failed).                               |                                                                  Illegal DeviceKey. DeviceKey must be 4 to 32 characters long and contain numbers, capital letters or lowercase letters.                                                                  |
|   1,10427   |                                Hash information is illegal (Connection failed).                                |                                                                            Device internal error. Please contact Acceleronix Product Dept FAE to capture logs for analysis.                                                                            |
|   1,10431   |                                       DK is illegal (Connection failed).                                       |                                                                  Illegal DeviceKey. DeviceKey must be 4 to 32 characters long and contain numbers, capital letters or lowercase letters.                                                                  |
|   1,10433   |                                                Flag is illegal.                                                |                                                                            Device internal error. Please contact Acceleronix Product Dept FAE to capture logs for analysis.                                                                            |
|   1,10434   |            ClientID is not matched with the password (The password contains ClientID-related information).            |                                                                            Device internal error. Please contact Acceleronix Product Dept FAE to capture logs for analysis.                                                                            |
|   1,10440   |                               The gateway is not associated with the sub-device.                               |                                                                                                                                                                                                                                                          |
|   1,10450   |                                   Device internal error (Connection failed).                                   | Possible reason 1: No available network. Please check whether the device is connected to the antenna and registered on the network. <br>Possible reason 2: Device internal error. Please contact Acceleronix Product Dept FAE to capture logs for analysis. |
|   1,10500   |                Device authentication to Developer Center failed (Unknown error in the system).                |                                                                            Device internal error. Please contact Acceleronix Product Dept FAE to capture logs for analysis.                                                                            |

## __2：Access Event__

| Event ID, Event Code |                                       Description                                       |                                                                                                                                          Reason and Treatment                                                                                                                                          |
| :---------: | :-------------------------------------------------------------------------------------: | :-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------: |
|   2,10200   |                                Registered successfully.                                |                                                                                                                                                                                                                                                                                                        |
|   2,10404   |                  An error occurred when Developer Center called the API.                  |                                                                                                   Device internal error. Please contact Acceleronix Product Dept FAE to capture logs for analysis.                                                                                                   |
|   2,10430   |                     DeviceSecret is incorrect (Connection failed).                     |                                                                                                               Incorrect DeviceSecret. Please reset the DeviceSecret on Developer Center.                                                                                                               |
|   2,10431   |                         Device is disabled (Connection failed).                         |                                                                                                   Device internal error. Please contact Acceleronix Product Dept FAE to capture logs for analysis.                                                                                                   |
|   2,10433   |                                    Flag is illegal.                                    |                                                                                                   Device internal error. Please contact Acceleronix Product Dept FAE to capture logs for analysis.                                                                                                   |
|   2,10434   | ClientID is not matched with the password (The password contains ClientID-related information). |                                                                                                   Device internal error. Please contact Acceleronix Product Dept FAE to capture logs for analysis.                                                                                                   |
|   2,10437   |                               DeviceSecret is incorrect.                               |                                                                                  Please initiate a request to connect the device to Developer Center or wait for the device to access Developer Center automatically.                                                                                  |
|   2,10438   |                   Developer Center cannot get the device information.                   |                                                                                  Please initiate a request to connect the device to Developer Center or wait for the device to access Developer Center automatically.                                                                                  |
|   2,10441   |                    The sub-device connects to Developer Center successfully.                    |                                                                                                                                                                                                                                                                                                        |
|   2,10450   |                       Device internal error (Connection failed).                       |                        Possible reason 1: No available network. Please check whether the device is connected to the antenna and registered on the network. <br>Possible reason 2: Device internal error. Please contact Acceleronix Product Dept FAE to capture logs for analysis.                        |
|   2,10471   |              The implementation version is not supported (Connection failed).              |                                                                                                   Device internal error. Please contact Acceleronix Product Dept FAE to capture logs for analysis.                                                                                                   |
|   2,10473   |                      Access heartbeat error (Connection timeout).                      |                                                                                            No available network. Please check whether the device is connected to the antenna and registered to the network.                                                                                            |
|   2,10474   |                           Network error (Connection timeout).                           |                                                                                            No available network. Please check whether the device is connected to the antenna and registered to the network.                                                                                            |
|   2,10475   |                                     Server changed.                                     |                                                                                                          Server or product information changed. Please edit the server or product information.                                                                                                          |
|   2,10476   |                                  AP connection error.                                  |                                                                                            No available network. Please check whether the device is connected to the antenna and registered to the network.                                                                                            |
|   2,10477   |                         The device is disabled (Connection failed).                         |                                                                                                                     Invalid DeviceKey. Please check whether the DeviceKey is valid.                                                                                                                     |
|   2,10478   |                                  Device reset failed.                                  | Perhaps you executed the command of configuring DeviceSecret of the device with static authentication as the authentication to configure DeviceSecret of the device with dynamic authentication as the authentication. Please check whether the device's authentication type is dynamic authentication. |
|   2,10500   |                   Registration failed (unknown error in the system).                   |                                                                                                   Device internal error. Please contact Acceleronix Product Dept FAE to capture logs for analysis.                                                                                                   |

## __3：Subscription Event__

| Event ID, Event Code |      Description      |                                        Reason and Treatment                                        |
| :---------: | :-------------------: | :------------------------------------------------------------------------------------------------: |
|   3,10200   | Subscription success. |                                                                                                    |
|   3,10300   | Subscription failure. | Device internal error. Please contact Acceleronix Product Dept FAE to capture logs for analysis. |

## __4：Data Sending Event__

| Event ID, Event Code |                         Description                         |                          Note                          |                                                                                                                                                                                                   Reason and Treatment                                                                                                                                                                                                   |
| :---------: | :---------------------------------------------------------: | :-----------------------------------------------------: | :----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------: |
|   4,10200   | Sent transparent transmission data [,\<txid>] successfully. | txid is returned after\<txid_mode> is set to "enabled". |                                                                                                                                                                                                                                                                                                                                                                                                                          |
|   4,10210   |           Sent TSL data [,\<txid>] successfully.           | txid is returned after\<txid_mode> is set to "enabled". |                                                                                                                                                                                                                                                                                                                                                                                                                          |
|   4,10220   |         Sent location data [,\<txid>] successfully.         | txid is returned after\<txid_mode> is set to "enabled". |                                                                                                                                                                                                                                                                                                                                                                                                                          |
|   4,10230   |          Sent status data [,\<txid>] successfully.          | txid is returned after\<txid_mode> is set to "enabled". |                                                                                                                                                                                                                                                                                                                                                                                                                          |
|   4,10240   |      Sent device information [,\<txid>] successfully.      | txid is returned after\<txid_mode> is set to "enabled". |                                                                                                                                                                                                                                                                                                                                                                                                                          |
|   4,10250   |  Sent NTP information [,\<txid>] successfully.  | txid is returned after\<txid_mode> is set to "enabled". |                                                                                                                                                                                                                                                                                                                                                                                                                          |
|   4,10300   |  Failed to send transparent transmission data [,\<txid>].  | txid is returned after\<txid_mode> is set to "enabled". | Possible reason 1: The length of data sent by the device is different from that configured with Write Command. Please check whether the data length is correct. <br>Possible reason 2: The length of data sent by the device is greater than the maximum length supported by the module. <br>Possible reason 3: The device is not connected to Developer Center. Please ensure the device is connected to Developer Center. |
|   4,10310   |             Failed to send TSL data [,\<txid>].             | txid is returned after\<txid_mode> is set to "enabled". | Possible reason 1: The length of data sent by the device is different from that configured with Write Command. Please check whether the data length is correct. <br>Possible reason 2: The length of data sent by the device is greater than the maximum length supported by the module. <br>Possible reason 3: The device is not connected to Developer Center. Please ensure the device is connected to Developer Center. |
|   4,10320   |          Failed to send location data [,\<txid>].          | txid is returned after\<txid_mode> is set to "enabled". |                                                                                                                                                                                                                                                                                                                                                                                                                          |
|   4,10330   |           Failed to send status data [,\<txid>].           | txid is returned after\<txid_mode> is set to "enabled". |                                                                                                                                                                                                                                                                                                                                                                                                                          |
|   4,10340   |        Failed to send device information [,\<txid>].        | txid is returned after\<txid_mode> is set to "enabled". |                                                                                                                                                                                                                                                                                                                                                                                                                          |
|   4,10350   |                 Failed to send NTP information [,\<txid>].                 | txid is returned after\<txid_mode> is set to "enabled". |                                                                                                                                                                                                                                                                                                                                                                                                                          |
|  4, 10400   | Sending transparent transmission data is refused. |
|  4, 10410   |           Sending TSL data is refused.            |
|  4, 10420   |       Sending positioning data is refused.        |


## __5：Data Receiving Event__

| Event ID, Event Code |                                                                                            Description                                                                                            |                                        Note                                        | Reason and Treatment                                                           |
| :---------: | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------: | :--------------------------------------------------------------------------------: | ------------------------------------------------------------------------------ |
|   5,10200   |                              URC is followed by [,\<length><\r\n>\<data>] to indicate that the transparent transmission data issued by Developer Center was received.                              | The received downlink data is issued directly if the module is in unbuffered mode. |                                                                                |
|   5,10210   |                                        URC is followed by [,\<length><\r\n>\<data>] to indicate that the TSL data issued by Developer Center was received.                                        | The received downlink data is issued directly if the module is in unbuffered mode. |                                                                                |
|   5,10211   | URC is followed by\<pkgId>,\<ID1>[,\<ID2>...] to indicate that TSL read command issued by Developer Center was received. <br>\<pkgId\>: Request Package ID issued by Developer Center. <br>\<ID\>: TSL ID. |                                                                                    |                                                                                |
|   5,10212   |                                                              URC is followed by \<PkgID>,\<length>,[\<data>] to indicate that the TSL service data issued by Developer Center was received.                                                              |     The received downlink data is issued directly if the module is in unbuffered mode.   |                                      |
|   5,10428   |                                                                         Traffic limiting caused by excessive device cache.                                                                         |                                                                                    | Device cache is too large. Please read the cache data in time.                 |
|   5,10473   |                                                              Failed to receive the data whose length exceeds the device cache limit.                                                              |                                                                                    | The data issued by Developer Center is too long. Please check the data length. |

## __6：Deregistration Event__

| Event ID, Event Code |                      Description                      |
| :---------: | :----------------------------------------------------: |
|   6,10200   | Successful de-registration (successful disconnection). |

## __7：OTA Event__

| Event ID, Event Code |                                                                            Description                                                                            |                                                                                                                                              Parameter                                                                                                                                              |                                                                                                                                                                                               Reason and Treatment                                                                                                                                                                                               |
| :---------: | :---------------------------------------------------------------------------------------------------------------------------------------------------------------: | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------: | :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------: |
|   7,10700   | URC is followed by ,\<componentNo>,\<sourceVersion>,\<targetVersion>,\<batteryLimit>,\<minSignalIntensity>,\<useSpace> to indicate that there is an upgrade task. | \<componentNo> Component identifier. <br>\<sourceVersion> Source version. <br>\<targetVersion> Target version. <br>\<batteryLimit> Minimum power required for the OTA upgrade. <br>\<minSignalIntensity> Minimum signal strength required for the OTA upgrade. <br>\<useSpace> Disk space required for the OTA upgrade. |                                                                                                                                                                                                                                                                                                                                                                                                                  |
|   7,10701   |                URC is followed by [,"\<componentNo>",\<length>,"\<MD5>",\<CRC>] to indicate that the device started downloading firmware package.                |                                                                           \<componentNo> Component identifier. <br>\<length> Size of  OTA upgrade firmware package. <br>\<MD5> md5 value of firmware package in the OTA upgrade.                                                                           |                                                                                                                                                                                                                                                                                                                                                                                                                  |
|   7,10702   |                                                                  Firmware download in progress.                                                                  |                                                                                                                                                                                                                                                                                                    |                                                                                                                                                                                                                                                                                                                                                                                                                  |
|   7,10703   |                URC is followed by [,"\<componentNo>",\<length>,\<startaddr>,\<piece_length>] to indicate that the firmware package was downloaded.                |                                     \<componentNo> Component identifier. <br>\<length> Size of OTA upgrade firmware package. <br>\<startaddr> Start address of the current downloaded block in the OTA upgrade package. <br>\<piece_length> Size of the current file block.                                     |                                                                                                                                                                                                                                                                                                                                                                                                                  |
|   7,10704   |                                                                   Firmware update in progress.                                                                   |                                                                                                                                                                                                                                                                                                    |                                                                                                                                                                                                                                                                                                                                                                                                                  |
|   7,10705   |                                                                    Firmware upgrade successful                                                                    |                                                                                                                                                                                                                                                                                                    |                                                                                                                                                                                                                                                                                                                                                                                                                  |
|   7,10706   |                                                                     Firmware upgrade failed.                                                                     |                                                                                                                                                                                                                                                                                                    | Possible reason 1: The target version is incorrect. Please check whether the target version you entered is correct.<br> Possible reason 2: Network disconnected during the OTA upgrade. Please check whether the network or signal was interrupted during the OTA upgrade.<br>Possible reason 3:  The uploaded upgrade package is incorrect. Please check whether the uploaded upgrade package is valid. |
|   7,10707   |                                                        Advertisement of the first device operation result.                                                        |                                                                                                                                                                                                                                                                                                    |                                                                                                                                                                                                                                                                                                                                                                                                                  |

## __8：Developer Center Event__

| Event ID, Event Code |                                          Description                                          |                                                                                Reason and Treatment                                                                                |
| :---------: | :--------------------------------------------------------------------------------------------: | :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------: |
|   8,10428   |               Traffic limiting caused by high-frequency messages on the device.               |                                                  Device sends messages too frequently. Please reduce the data-sending frequency.                                                  |
|   8,10429   | Traffic limiting caused by an excessive number of single device activations or daily requests. | Device activation code is insufficient. Please contact the local sales for price consultation and our sales will facilitate the purchase process and help to further the progress. |

## __9: Wake-up Event__

| Event ID, Event Code |               Description               |
|   :--------------:   | :-------------------------------------: |
|        9,10200       | The device is woken up from deep sleep. |


# 设备调试

本文介绍设备运维中的设备调试。设备调试指通过网页输入，对设备进行指令下发。验证设备与平台的交互逻辑。设备调试根据产品的数据格式不同，分为透传数据调试和物模型数据调试两种。

## **透传数据调试**

透传数据，通常设备与应用侧已通过自定义协议进行过对接，由设备侧和应用侧自行解析报文数据，平台只负责数据的传输，并不能对透传数据进行解析和数据应用的扩展。

### **前提条件**

● 创建一个数据格式为透传的产品<br />
● 完成设备端开发，并成功连接平台

### **操作步骤**

1.登录开发者中心后，点击左侧菜单“设备管理”→ “设备运维”，出现设备列表页，点击设备操作栏中“查看”，跳转至“设备信息” tab页，点击“设备调试”tab页。

<a data-fancybox title="img" href="/zh/guide/image2022-3-10_13-31-46.png?version=1&modificationDate=1646889716000&api=v2">![img](/zh/guide/image2022-3-10_13-31-46.png?version=1&modificationDate=1646889716000&api=v2)</a>

左侧为数据调试的输入区，右侧为实时数据日志的展示区。

| 参数     | 描述                                                         |
| :------- | :----------------------------------------------------------- |
| 数据格式 | 发送透传数据的格式，Hex:将数据或者byte数组转换为十六进制字符发送。(示例: 二进制内容0x1234AB(3 bytes) 转换成字符串为“1234AB”(6 bytes);字符串"1234AB"(6 bytes) 转换成二进制内容为 0x1234AB(3 bytes);)Text:将数据以UTF-8编码格式发送。 |
| 数据内容 | 数据内容限制为4096字符，数据内容的范围大小以模组能够接收的数据为准。 |
| 缓存时长 | 缓存下发数据内容，若当前设备离线，将在设备下一次上线或者上行数据时，下发缓存中的内容。单位：秒 |
| 是否覆盖 | 是：代表当前设备离线时，若发送的数据指令重复则在下次设备上线或上行数据时，只发送最新一条指令，之前重复指令会被最新指令覆盖。否：代表当前设备离线时，若发送的数据指令重复则在下次设备上线或上行数据时，全部下发所有缓存数据。 |
| 发送指令 | 点击发送下发数据，实时日志会展示当前下发的数据。               |
| 重置数据 | 清空当前所有的选择以及输入的数据内容，回到刚进设备调试页初始状态。 |

## **物模型数据调试**

物模型数据，设备与平台交互按照物模型数据协议进行。平台可对物模型数据解析展示，后续也可对物模型数据进行更多应用扩展。

### **前提条件**

● 创建一个数据格式为物模型的产品<br />
● 完成设备端开发，并成功连接平台

### **操作步骤**

1.登录开发者中心后，点击左侧菜单“设备管理”→ “设备运维”，出现设备列表页，点击设备操作栏中“查看”，跳转至“设备信息” tab页，点击“设备调试”tab页。

<a data-fancybox title="img" href="/zh/guide/image2022-3-10_14-3-55.png?version=1&modificationDate=1646891645000&api=v2">![img](/zh/guide/image2022-3-10_14-3-55.png?version=1&modificationDate=1646891645000&api=v2)</a>



左侧为数据调试的输入区，分为属性调试和服务调用两种不同输入面板。右侧为实时数据日志的展示区。

属性调试 ：通过界面选择属性调试，调试当前产品物模型定义中定义的属性，方法可以选择设置/获取。平台会自动显示输入的内容，您替换’$’部分的内容，就可对设备进行调试。

服务调用 ：通过界面选择服务调试，调试当前产品物模型定义中定义的服务。平台会自动根据这个服务定义的输入参数，给出显示输入的内容，您替换’$’部分的内容，就可对设备进行调试。

# Obtain iOS App Download Link in App Store 

## There are two methods to get the iOS app download link in App Store.
Method 1:  If you are able to log in to App Store Connect, after logging in, click the target app, and you can view the corrsponding "**App Information**" - "**Additional Information**". Click "**View on App Store**", and you will be redirected to a new page. The URL of the new page is the app download link in App Store.  
<a data-fancybox title="img" href="/en/appDevelop/oemapp/appstore/store37-en.png">![image](/en/appDevelop/oemapp/appstore/store37-en.png)</a>

Method 2:  If you are unable to log in to App Store Connect, you can search for the target app on the App Store and enter the detailed page on your mobile phone. Click the "**Share**" icon on the right, and click "**Copy Link**". This link is the app download link in App Store.  
<a data-fancybox title="img" href="/en/appDevelop/oemapp/appstore/store38-en.png">![image](/en/appDevelop/oemapp/appstore/store38-en.png)</a>

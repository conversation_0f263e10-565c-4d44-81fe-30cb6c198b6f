# 设备管理

## 功能概述

设备管理主要提供包含设备列表、设备信息、设备配置相关等操作。

```objc
#import <QuecDeviceKit/QuecDeviceKit.h>
/// 初始化
[QuecDeviceService sharedInstance]
```

在设备管理类中您将频繁使用到QuecDeviceModel类，关于属性的定义如下

**QuecDeviceModel属性定义**

| 字段                     | 类型                   | 描述                                   |
|------------------------|----------------------|--------------------------------------|
| accessType             | String               | 0-直连设备 1-网关设备 2-网关子设备                |
| activeTime             | String               | 激活时间                                 |
| activeTimeTs           | long                 | 激活时间戳                                |
| authKey                | String               | 授权key                                |
| deviceBindTime         | String               | 设备绑定时间                               |
| deviceBindTimeTs       | long                 | 设备绑定时间戳                              |
| deviceCreateTime       | String               | 设备创建时间                               |
| deviceKey              | String               | 设备key                                |
| deviceName             | String               | 设备名称                                 |
| deviceStatus           | String               | 云端设备状态：离线 在线                         |
| onlineStatus           | int                  | 云端设备状态：0-离线 1-在线                     |
| deviceType             | int                  | 设备类型：1 自有设备、 2 分享来的设备                |
| invaildTime            | String               | 失效时间                                 |
| invaildTimeTs          | long                 | 失效时间戳                                |
| lastConnTime           | String               | 最后上线时间                               |
| lastConnTimeTs         | String               | 最后上线时间戳                              |
| locateType             | String               | 支持的定位内容                              |
| ownerUid               | String               | 分享人用户ID，来自谁的分享                       |
| phone                  | String               | 已绑定用户手机号                             |
| productKey             | String               | 产品key                                |
| productName            | String               | 产品名称                                 |
| protocol               | String               | 接入协议                                 |
| uid                    | String               | 已绑定用户ID                              |
| userName               | String               | 已绑定用户昵称                              |
| verified               | String               | 设备绑定是否认证：0 未认证 1 已认证                 |
| signalStrength         | String               | 信号强度                                 |
| status                 | int                  | 绑定状态：1 正常 2 失效                       |
| lastOfflineTime        | String               | 离线时间                                 |
| lastOfflineTimeTs      | long                 | 离线时间戳                                |
| btPwd                  | String               | btPwd                                |
| bindType               | String               | 绑定类型:1 SN绑定 2 wifi绑定 3 PKDK绑定 4 蓝牙绑定 |
| authCode               | String               | 授权authCode                           |
| logoImage              | String               | 产品logo                               |
| sn                     | String               | sn                                   |
| productIcon            | String               | 产品图片                                 |
| upgradeStatus          | int                  | 设备OTA状态：0-未升级，1-升级中，2-升级成功，3-升级失败    |
| userConfirmStatus      | int                  | 设备OTA用户确认状态                          |
| planId                 | long                 | 计划id                                 |
| capabilitiesBitmask    | int                  | 设备通道能力mask                           |
| bindMode               | int                  | 绑定模式: 1 多绑模式 2 单绑模式 3 轮流模式           |
| deviceId               | String               | 设备唯一标识，pk 和 dk共同决定                   |
| onlineChannelState     | int                  | 通道在离线状态，包含近场、ws                      |
| connectingChannelState | int                  | 通道连接中状态                              |
| isNative               | int                  | 是否是移远设备，0-未知 1-移远                    |
| isMatter               | int                  | 是否是matter设备，0-否 1-是                  |
| networkType            | String               | 网络类型：1-wifi   2-蜂窝    3-NB   5-蓝牙    |
| matterInfo             | QuecDeviceMatterMeta | matter 元数据                           |
| firstItemCode          | String               | 一级品类code                             |
| firstItemName          | String               | 一级品类名称                               |
| secondItemCode         | String               | 二级品类code                             |
| secondItemName         | String               | 二级品类名称                               |
| lowPowerProduct        | boolean              | 是否为低功耗产品                             |
| lowPowerStatus         | boolean              | 低功耗开关状态                              |
| lowPowerCache          | int                  | 低功耗缓存时长，单位为秒                         |
| isCommonUsed           | boolean              | 是否常用                                 |
| fid                    | String               | 家庭id                                 |
| frid                   | String               | 房间id                                 |
| roomName               | String               | 房间名称                                 |
| shareCode              | String               | 分享码                                  |
| isShared               | boolean              | 是否来自分享                               |
| gdid                   | String               | 群组id                                 |
| isGroupDevice          | boolean              | 是否群组设备                               |
| groupDeviceDeviceNum   | int                  | 群组包含的设备数量                            |
| dps                    | NSDictionary         | dps数据                                |
| bindingCode            | String               | bindingCode                          |
| btLastUseTime          | long                 | 蓝牙最近使用时间（毫秒时间戳）                      |
| lowPowerAlive          | int                  | 低功耗心跳周期（分）                           |
| aiCapabilityStatus     | boolean              | 是否支持Ai能力                             |

## 设备管理

### 获取设备列表

**接口说明**

用于获取设备列表，需要先调用绑定接口关联设备和用户关系

```objc
- (void)getDeviceListWithParams:(QuecDeviceListParamsModel *)params 
                         success:(void(^)(NSArray<QuecDeviceModel *> *list, NSInteger total))success
                         failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- |  
| params |	是|QuecDeviceListParamsModel类 | 
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**QuecDeviceListParamsModel属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| pageNumber       | NSInteger             | 页码 （必填）    |
| pageSize  | NSInteger            | 页大小 （必填） |
| isAssociation | BOOL | 设备关联管理参数（非必填）    |
| secondItemCode  | NSString            | 二级品类参数（非必填） |
| pkList  | NSString            | pk列表，多个使用英文逗号分隔 （非必填）|
| deviceName  | NSString            | 设备名称-用于设备名称搜索（非必填） |

>返回值类QuecDeviceModel属性定义已在文档开头注释

**示例代码**

```objc
QuecDeviceListParamsModel *paramsModel = QuecDeviceListParamsModel.new;
paramsModel.deviceName = @"searchKey";
paramsModel.pageNumber = 1;
paramsModel.pageSize = pageSize;
[QuecDeviceService.sharedInstance getDeviceListWithParams:paramsModel success:^(NSArray<QuecDeviceModel *> *list, NSInteger total) {
        /// Next Action
    } failure:^(NSError *error) {
        NSLog(@"check error: %@", error);
    }];
```

### 设备是否可以绑定

**接口说明**

查询用户是否可以绑定该设备

```objc
- (void)getDeviceInfoBindingWithProductKey:(NSString *)productKey
                                   deviceKey:(NSString *)deviceKey
                           checkExistInCloud:(BOOL)checkExistInCloud
                                     success:(void(^)(QuecDeviceBindInfoModel *bindInfoModel))success
                                     failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- |  
| productKey | 是 | 产品pk |
| deviceKey | 否 | 设备dk |
| checkExistInCloud | 否 | 验证设备在云端是否存在 |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**QuecDeviceBindInfoModel属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| activeBluetooth       | BOOL             | 优先蓝牙激活开关配置：true - 开启，false - 未开启    |
| productName  | NSString            | 产品名称 |
| productLogo  | NSString            | 产品图片 |
| bindingMode  | int            |  绑定模式 多绑：1 /唯一：2 /轮流：3 |

**示例代码**

```objc
[QuecDeviceService.sharedInstance getDeviceInfoBindingWithProductKey:@"pk"
                                                            deviceKey:@""
                                                    checkExistInCloud:NO
                                                        success:^(QuecDeviceBindInfoModel  *bindInfoModel) {
        /// Next Action
    } failure:^(NSError *error) {
        NSLog(@"check error: %@", error);
    }];
```

### SN绑定设备

**接口说明**

通过设备SN绑定设备

```objc
- (void)bindDeviceWithSerialNumber:(NSString *)serialNumber
                        productKey:(NSString *)productKey
                        deviceName:(NSString *)deviceName
                           success:(void(^)(QuecDeviceBindSNModel *model))success
                           failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- |  
| serialNumber | 是 | 设备SN码 |
| productKey | 是 | 产品pk |
| deviceName | 否 | 设备名称 |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**QuecDeviceBindSNModel属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| authKey       | NSString             | authKey    |
| dk  | NSString            | 设备dk |
| pk  | NSString            | 产品pk |
| moduleType  | NSString            |  设备型号 |

**示例代码**

```objc
[QuecDeviceService.sharedInstance bindDeviceWithSerialNumber:@"sn" productKey:@"pk" deviceName:@"name" success:^(QuecDeviceBindSNModel *model) {
        /// Next Action
    } failure:^(NSError *error) {
        NSLog(@"check error: %@", error);
    }];
```

### 通过authCode绑定设备

**接口说明**

通过authCode绑定设备 (用于wifi/wifi+蓝牙设备绑定)

```objc
- (void)bindWifiDeviceWithAuthCode:(NSString *)authCode
                        productKey:(NSString *)productKey
                         deviceKey:(NSString *)deviceKey
                        deviceName:(NSString *)deviceName
               capabilitiesBitmask:(NSInteger)capabilitiesBitmask
                           success:(void(^)(QuecDeviceBindAuthCodeModel *model))success
                           failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- |  
| authCode | 是 | 设备authCode |
| productKey | 是 | 产品pk |
| deviceKey | 是 | 产品dk |
| deviceName | 否 | 设备名称 |
| capabilitiesBitmask | 否 | 设备连网能力, 参考:QuecIotChannelBitMask |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**QuecDeviceBindAuthCodeModel属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| authKey       | NSString             | authKey    |
| productLogo  | NSString            | 产品图片 |
| productName  | NSString            | 产品名称 |
| bindingMode  | NSString            |  绑定模式:1 多绑，2 唯-，3 轮流 |

**示例代码**

```objc
[QuecDeviceService.sharedInstance bindWifiDeviceWithAuthCode:@"authCode"
                                                        productKey:@"productKey"
                                                        deviceKey:@"deviceKey"
                                                        deviceName:@"deviceName"
                                                capabilitiesBitmask:0
                                                            success:^(QuecDeviceBindAuthCodeModel *model) {
        /// Next Action
    } failure:^(NSError *error) {
        NSLog(@"check error: %@", error);
    }];
```

### 纯蓝牙设备绑定

**接口说明**

通过authCode + password绑定设备 (用于纯蓝牙设备绑定)

```objc
- (void)bindPureBtDeviceWithEncryptedBindingCode:(NSString *)encryptedBindingCode
                                      productKey:(NSString *)productKey
                                       deviceKey:(NSString *)deviceKey
                                        password:(NSString *)password
                                          random:(NSString *)random
                                      deviceName:(NSString *)deviceName
                                  isCommonDevice:(BOOL)isCommonDevice
                             capabilitiesBitmask:(NSInteger)capabilitiesBitmask
                       encryptedNewBindingSecret:(NSString *)encryptedNewBindingSecret
                       encryptedOldBindingSecret:(NSString *)encryptedOldBindingSecret
                                         success:(QuecVoidBlock)success
                                         failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- |  
| encryptedBindingCode | 是 | 加密后的bindingCode |
| productKey | 是 | 产品pk |
| deviceKey | 是 | 产品dk |
| password | 否 | 设备password |
| random | 是 | 设备返回random |
| deviceName | 否 | 设备名称 |
| isCommonDevice | 是 | 是否加入常用 |
| capabilitiesBitmask | 否 | 设备连网能力, 参考:QuecIotChannelBitMask |
| encryptedNewBindingSecret | 是 | 新的加密 bindingSecret |
| encryptedOldBindingSecret | 否 | 旧的加密 bindingSecret(非必传) |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**示例代码**

```objc
[[QuecDeviceService sharedInstance] bindPureBtDeviceWithEncryptedBindingCode:@"encryptedBindingCode"
                                                     productKey:@"pk"
                                                      deviceKey:@"dk"
                                                       password:@""
                                                         random:@"random"
                                                     deviceName:@"deviceName"
                                                 isCommonDevice:YES
                                            capabilitiesBitmask:4
                                      encryptedNewBindingSecret:@"encryptedNewBindingsecret" 
                                      encryptedOldBindingSecret:@"encryptedOldBindingsecret" 
                                                         success:^{
        /// Next Action
    } failure:^(NSError *error) {
        NSLog(@"check error: %@", error);
    }];
```

### 设备重命名

**接口说明**

用于修改设备名称

```objc
- (void)updateDeviceName:(NSString *)deviceName 
               productKey:(NSString *)productKey
                deviceKey:(NSString *)deviceKey
                  success:(QuecVoidBlock)success
                  failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- |  
| deviceName | 是 | 设备名称 |
| productKey | 是 | 产品pk |
| deviceKey | 是 | 产品dk |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**示例代码**

```objc
[[QuecDeviceService sharedInstance] updateDeviceName:@"deviceName" productKey:@"productKey" deviceKey:@"deviceKey" success:^{
        /// Next Action
    } failure:^(NSError *error) {
        NSLog(@"check error: %@", error);
    }];
```

### 设备详情

**接口说明**

获取设备详情

```objc
- (void)getDeviceInfoByDeviceKey:(NSString *)deviceKey 
                       productKey:(NSString *)productKey
                          success:(void(^)(QuecDeviceModel *model))success
                          failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- |  
| productKey | 是 | 产品pk |
| deviceKey | 是 | 产品dk |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

>返回值类QuecDeviceModel属性定义已在文档开头注释

**示例代码**

```objc
[[QuecDeviceService sharedInstance] getDeviceInfoByDeviceKey:dk productKey:pk success:^(QuecDeviceModel *model) {
        /// Next Action
    } failure:^(NSError *error) {
        NSLog(@"check error: %@", error);           
    }];
```
### 设备物模型及业务属性值

**接口说明**

获取设备物模型及业务属性值

```objc
- (void)getProductTslAndDeviceBusinessAttributesWithProductKey:(NSString *)productKey
                                                        deviceKey:(NSString *)deviceKey
                                                        gatewayPk:(NSString *)gatewayPk
                                                        gatewayDk:(NSString *)gatewayDk
                                                         codeList:(NSString *)codeList
                                                             type:(NSString *)type
                                                       success:(void (^)(NSArray<QuecProductTSLPropertyModel *> *list))success
                                                          failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- | 
| productKey | 是 | 产品pk |
| deviceKey | 是 | 设备dk |
| gatewayPk | 否 | 网关设备的 Product Key |
| gatewayDk | 否 | 网关设备的 Device Key |
| codeList | 否 | 查询的属性标识符 和查询类型配合使用，如果查询多个属性，使用英文逗号分隔 |
| type | 否 | 查询类型 (查询类型可以单选和多选，如果需要查询多个类型的属性值，使用英文逗号分隔)  1 查询设备基础属性 2 查询物模型属性 3 查询定位信息 |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**QuecProductTSLPropertyModel属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| specs       | NSArray<QuecProductTSLSpecModel *>  *       | 物模型数据    |
| formatSpecs  | NSArray<QuecProductTSLSpecModel *>  *        | 物模型数据， 同上specs |
| dataType  | NSString            | 数据类型 |
| attributeValue  | NSString            |  属性值 |
| code  | NSString            |  标志符 |
| name  | NSString            |  名称 |
| subType  | NSString            |  读写类型 |
| itemId  | NSInteger            |  id |
| sort  | NSString            |  排序 |
| type  | NSString            |  类型 |
| desc  | NSString            |  描述 |

**QuecProductTSLSpecModel属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| specs       | NSArray<QuecProductTSLSpecModel *>  *       | 嵌套物模型数据    |
| formatSpecs  | NSArray<QuecProductTSLSpecModel *>  *        | 嵌套物模型数据， 同上specs |
| dataType  | NSString            | 数据类型 |
| code  | NSString            |  标志符 |
| name  | NSString            |  名称 |
| value  | NSString            |  value值 |
| itemId  | NSInteger            |  id |
| unit  | NSString            |  单位 |
| min  | NSString            |  最小值 |
| max  | NSString            |  最大值 |
| step  | NSString            |  步长 |
| length  | NSString            |  文本长度 |
| size  | NSString            |  数组大小 |
| attributeValue  | id            |  属性值 |

**示例代码**

```objc
[QuecDeviceService.sharedInstance getProductTslAndDeviceBusinessAttributesWithProductKey:@"pk"
                                                                                   deviceKey:@"dk"
                                                                                   gatewayPk:@"gatewayPk"
                                                                                   gatewayDk:@"gatewayDk"
                                                                                    codeList:@"codeList"
                                                                                        type:@"type"
                                                                                     success:^(NSArray<QuecProductTSLPropertyModel *> *list) {
        /// Next Action
    } failure:^(NSError *error) {
        NSLog(@"check error: %@", error);
    }];
```

### 设备物模型

**接口说明**

获取设备物模型

```objc
- (void)getProductTSLWithProductKey:(NSString *)productKey
                            success:(void (^)(QuecProductTSLModel *tslModel))success
                            failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- |  
| productKey | 是 | 产品pk |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	|

**QuecProductTSLModel属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| profile       | QuecProductTSLProfileModel       | profile信息    |
| properties  | NSArray<QuecProductTSLPropertyModel *> *       | 属性，item 是 QuecProductTSLPropertyModel |
| services  | NSArray<QuecProductTSLServiceModel *> *   | 服务，item 是 QuecProductTSLServiceModel |
| events  | NSArray<QuecProductTSLEventModel *> *            |  事件，item 是 QuecProductTSLEventModel |

**QuecProductTSLProfileModel属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| productKey       | NSString       | 产品key    |
| version  | NSString      | 版本 |
| tslVersion  | NSString   | tls版本 |

>QuecProductTSLPropertyModel属性定义同上

**QuecProductTSLServiceModel属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| inputData       | NSDictionary       | 服务输入项，描述服务输入的数据    |
| outputData  | NSDictionary      | 服务输出项，描述服务输出的数据 |

**QuecProductTSLEventModel属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| outputData  | NSDictionary      | 事件输出项，描述事件输出的具体事项 |

**示例代码**

```objc
[[QuecDeviceService sharedInstance] getProductTSLWithProductKey:@"productKey" success:^(QuecProductTSLModel *tslModel) {
        /// Next Action
    } failure:^(NSError *error) {
         NSLog(@"check error: %@", error);
    }];
```

### 设备业务属性值

**接口说明**

获取设备业务属性值

```objc
- (void)getDeviceBusinessAttributesWithProductKey:(NSString *)productKey 
                                          deviceKey:(NSString *)deviceKey
                                          gatewayPk:(NSString *)gatewayPk
                                          gatewayDk:(NSString *)gatewayDk
                                           codeList:(NSString *)codeList
                                               type:(NSString *)type
                                            success:(void (^)(QuecProductTSLInfoModel *tslInfoModel))success
                                            failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- |  
| productKey | 是 | 产品pk |
| deviceKey | 是 | 设备dk |
| gatewayPk | 否 | 网关设备的 Product Key |
| gatewayDk | 否 | 网关设备的 Device Key |
| codeList | 否 | 查询的属性标识符 和查询类型配合使用，如果查询多个属性，使用英文逗号分隔 |
| type | 否 | 查询类型 (查询类型可以单选和多选，如果需要查询多个类型的属性值，使用英文逗号分隔) 1 查询设备基础属性 2 查询物模型属性 3 查询定位信息 |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**QuecProductTSLInfoModel属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| customizeTslInfo       | NSArray<QuecProductTSLCustomInfoModel *> *       | 自定义物模型数据上报列表    |
| deviceLocateInfo  | QuecProductTSLLocateInfoModel      | 定位信息 |
| deviceData  | QuecProductTSLDataModel   | 设备资源数据 |
| tslResourcesInfo  | NSArray<QuecProductTSLResourceInfoModel *> *   | 资源物模型数据 |

**QuecProductTSLCustomInfoModel属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| abId       | NSInteger       | 功能ID    |
| dataType  | NSString      | 数据类型 |
| name  | NSString   | 功能名称 |
| resourceCode  | NSString   | 功能标识符 |
| resourceValue  | NSString   | 功能值 |
| subType  | NSString   | 数据操作类型 |
| type  | NSString   | 功能类型 |

**QuecProductTSLLocateInfoModel属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| createTime       | double       | 创建时间    |
| deviceKey  | NSString   | deviceKey |
| hdop  | NSString      | 水平精度因子 |
| lat  | NSString   | 纬度 |
| lng  | NSString   | 经度 |
| productKey  | NSString   | productKey |
| latType  | NSString   | 纬度半球 N/S |
| lngType  | NSString   | 经度半球 W/E |
| locateRaw  | NSString   | 原始数据 |
| locateStatus  | NSString   | 差分定位/非差分定位 |
| locateTime  | NSString   | 定位时间 |
| locateType  | NSString   | 定位类型 |
| satellites  | NSString   | 当前卫星数 |
| bdLat  | NSString   | BD09坐标系纬度 |
| bdLng  | NSString   | BD09坐标系经度 |
| gcjLat  | NSString   | GCJ坐标系纬度 |
| gcjLng  | NSString   | GCJ坐标系经度 |
| wgsLat  | NSString   | GPS 原始坐标纬度 |
| wgsLng  | NSString   | GPS 原始坐标经度 |

**QuecProductTSLDataModel属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| battery       | double       | 电池电量    |
| cellId  | NSInteger      | cellId |
| comProtocolVer  | NSString   | 通信协议版本 |
| dataProtocolVer  | NSString   | 数据协议版本 |
| deviceKey  | NSString   | deviceKey |
| iccid  | NSString   | iccid |
| lac  | NSInteger   | 位置区代码 |
| locator  | NSString   | 定位支持功能 |
| mcc  | NSString   | 移动国家代码 |
| mcuVersion  | NSString   | MCU版本 |
| memoryFree  | NSInteger   | 剩余空间 |
| mnc  | NSInteger   | 移动网络代码 |
| phoneNum  | NSString   | 手机号 |
| productKey  | NSString   | productKey |
| rsrp  | NSInteger   | 参考信号接收功率 |
| rsrq  | NSInteger   | LTE参考信号接收质量 |
| sdkVersion  | NSString   | SDK版本 |
| sim  | NSString   | SIM号 |
| snr  | NSInteger   | 信号与干扰加噪声比 |
| type  | NSString   | 模组型号 |
| version  | NSString   | 模组版本 |
| voltage  | double   | 电压 |

**QuecProductTSLResourceInfoModel属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| deviceKey  | NSString   | deviceKey |
| createTime  | NSString   | 创建时间 |
| deviceId  | NSString   | 设备id |
| resourceCode  | NSString   | 资源标识 |
| resourceValue  | NSString   | 资源值 |
| updateTime  | NSString   | 更新时间 |

**示例代码**

```objc
[QuecDeviceService.sharedInstance getDeviceBusinessAttributesWithProductKey:@"productKey" deviceKey:@"deviceKey" gatewayPk:@"" gatewayDk:@"" codeList:@"" type:@"1" success:^(QuecProductTSLInfoModel *tslInfoModel) {
        /// Next Action
    } failure:^(NSError *error) {
        NSLog(@"check error: %@", error);
    }];
```

### 离线提醒开关

**接口说明**

设置设备离线提醒开关

```objc
- (void)setOfflineReminderWithProductKey:(NSString *)productKey 
                                 deviceKey:(NSString *)deviceKey
                    enableOfflineReminder:(int)enableOfflineReminder
                                   success:(QuecVoidBlock)success
                                   failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- | 
| productKey | 是 | 产品pk |
| deviceKey | 是 | 设备dk |
| enableOfflineReminder | 是 | 0：关闭，1：开启 |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	|  

**示例代码**

```objc
[[QuecDeviceService sharedInstance] setOfflineReminderWithProductKey:@"productKey" deviceKey:@"deviceKey" enableOfflineReminder:enable success:^{
       /// Next Action
   } failure:^(NSError *error) {
       NSLog(@"check error: %@", error);
   }];
```

### 离线提醒开关查询

**接口说明**

获取设备离线提醒开关状态

```objc
- (void)getOfflineReminderWithProductKey:(NSString *)productKey 
                                 deviceKey:(NSString *)deviceKey
                                   success:(void(^)(int enableOfflineReminder))success
                                   failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- |  
| productKey | 是 | 产品pk |
| deviceKey | 是 | 设备dk |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**示例代码**

```objc
[[QuecDeviceService sharedInstance] getOfflineReminderWithProductKey:@"productKey" deviceKey:@"deviceKey" success:^(int enableOfflineReminder) {
       /// Next Action
   } failure:^(NSError *error) {
       NSLog(@"check error: %@", error);
   }];
```

### 设置设备时区

**接口说明**

设置设备时区， 用于设备时区同步当前App所设置时区

```objc
- (void)setDeviceTimeZoneWithProductKey:(NSString *)productKey 
                                deviceKey:(NSString *)deviceKey
                                 timeZone:(NSString *)timeZone
                                 timeZoneId:(NSString *)timeZoneId
                                  success:(QuecVoidBlock)success
                                  failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- |  
| productKey | 是 | 产品pk |
| deviceKey | 是 | 设备dk |
| timeZone | 是 | 时区,符合GMT+-HH:mm或者GMT+-HH格式 |
| timeZoneId | 是 | 时区ID |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**示例代码**

```objc
[[QuecDeviceService sharedInstance] setDeviceTimeZoneWithProductKey:@"productKey" deviceKey:@"deviceKey" timeZone:@"GMT+08:00" timeZoneId:[NSTimeZone localTimeZone].name success:^{
        /// Next Action
    } failure:^(NSError *error) {
        NSLog(@"check error: %@", error);
    }];
```

### 获取设备时区

**接口说明**

获取当前设备时区信息

```objc
- (void)getDeviceTimeZoneWithProductKey:(NSString *)productKey 
                                deviceKey:(NSString *)deviceKey
                                  success:(void(^)(QuecDeviceTimeZoneModel *model))success
                                  failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- | 
| productKey | 是 | 产品pk |
| deviceKey | 是 | 设备dk |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	|  

**QuecDeviceTimeZoneModel属性定义**

| 字段        | 类型                 | 描述   |
|-----------|--------------------|------|
| timeZone  | NSString  | 设备时区 |
| timeZoneId  | NSString  | 时区id |

**示例代码**

```objc
[QuecDeviceService.sharedInstance getDeviceTimeZoneWithProductKey:@"productKey" deviceKey:@"deviceKey" success:^(QuecDeviceTimeZoneModel *model) {
        /// Next Action
    } failure:^(NSError *error) {
        NSLog(@"check error: %@", error);
    }];
```

### 获取产品说明书

**接口说明**

获取产品说明书

```objc
- (void)getProductDescriptionWithProductKey:(NSString *)productKey 
                                      success:(void(^)(NSString *url))success
                                      failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- |  
| productKey | 是 | 产品pk |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**示例代码**

```objc
[QuecDeviceService.sharedInstance getProductDescriptionWithProductKey:@"productKey" success:^(NSString *url) {
        if (url) {
            /// Next Action
        }
    } failure:^(NSError *error) {
        NSLog(@"check error: %@", error);
    }];
```

### 获取设备重置凭证

**接口说明**

查询纯蓝牙设备重置凭证，可用于设备云端重置

```objc
- (void)getPureBtResetCredentialsWithDeviceList:(NSArray<QuecPureBtResetEnterModel *> *)deviceList
                                            success:(void(^)(QuecPureBtResetModel *model))success
                                            failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- |  
| deviceList | 是 | 待查询设备QuecPureBtResetEnterModel数组 |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**QuecPureBtResetEnterModel属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| pk  | NSString  | 产品pk |
| dk  | NSString  | 设备dk |

**QuecPureBtResetModel属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| successList  | NSArray<QuecPureBtResetCredentialsModel *> *  | 成功列表 |
| failureList  | NSArray<QuecPureBtResetCredentialsModel *> *  | 失败列表 |

**QuecPureBtResetCredentialsModel属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| pk  | NSString  | 产品pk |
| dk  | NSString  | 设备dk |
| resetCredentials  | NSString  | 纯蓝牙设备重置凭证（仅成功列表有此属性） |
| msg  | NSString  | 错误提示（仅失败列表有此属性） |

**示例代码**

```objc
QuecPureBtResetEnterModel *resetModel = QuecPureBtResetEnterModel.new;
resetModel.pk = @"pk";
resetModel.dk = @"dk";
[QuecDeviceService.sharedInstance getPureBtResetCredentialsWithDeviceList:@[resetModel] success:^(QuecPureBtResetModel *model) {
        /// Next Action
    } failure:^(NSError *error) {
        NSLog(@"check error: %@", error);
    }];
```

### 单设备解绑

**接口说明**

设备解绑接口

```objc
- (void)unbindDeviceWithDeviceKey:(NSString *)deviceKey
                        productKey:(NSString *)productKey
                            isInit:(BOOL)isInit
                            random:(NSString *)random
                 resetCredentials:(NSString *)resetCredentials
                           success:(QuecVoidBlock)success
                           failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- |  
| deviceKey | 是 | 设备dk |
| productKey | 是 | 产品pk |
| isInit | 否 | 是否初始化，默认false （纯蓝牙设备需填） |
| random | 否 | （纯蓝牙设备需填） |
| resetCredentials | 否 | 纯蓝牙设备重置凭证 （纯蓝牙设备需填） |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**示例代码**

```objc
[QuecDeviceService.sharedInstance unbindDeviceWithDeviceKey:@"deviceKey"
                                                         productKey:@"productKey"
                                                             isInit:NO
                                                             random:@""
                                                   resetCredentials:@"" success:^{
            /// Next Action
        } failure:^(NSError *error) {
            NSLog(@"check error: %@", error);
        }];
```

### 批量解绑设备

**接口说明**

批量解绑设备

```objc
- (void)batchUnbindDeviceWithIsInit:(BOOL)isInit
                          deviceList:(NSArray<QuecDeviceModel *> *)deviceList
                             success:(void(^)(QuecBatchUnbindModel *model))success
                             failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- |  
| isInit | 否 | 是否初始化，默认false |
| deviceList | 是 | QuecDeviceModel数组 |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

> QuecDeviceModel属性定义同上

**QuecBatchUnbindModel属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| successList  | NSArray<QuecBatchUnbindDeviceModel *> *  | 成功列表 |
| failList  | NSArray<QuecBatchUnbindDeviceModel *> *  | 失败列表 |

**QuecBatchUnbindDeviceModel属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| pk  | NSString  | 产品pk |
| dk  | NSString  | 设备dk |
| gdid  | NSString  | 群组id |
| shareCode  | NSString  | 分享码 |
| bindingSum  | NSInteger  | 解绑操作后设备在云端的绑定用户数，包括伪绑定 |
| code  | NSInteger  | 错误码（仅失败列表有此属性） |
| msg  | NSString  | 错误提示（仅失败列表有此属性） |

**示例代码**

```objc
[QuecDeviceService.sharedInstance batchUnbindDeviceWithIsInit:NO deviceList:@[deviceModel1, deviceModel2] success:^(QuecBatchUnbindModel *model) {
        /// Next Action
    } failure:^(NSError *error) {
        NSLog(@"check error: %@", error);
    }];
```

### 云端生成AuthKey

**接口说明**

辅助云端生成AuthKey, 场景为设备激活上云后建立通道AuthKey为空情况

```objc
- (void)regenerateAuthKeyWithDeviceKey:(NSString *)deviceKey
                            productKey:(NSString *)productKey
                               success:(QuecStringBlock)success
                               failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- |  
| deviceKey | 是 | 设备dk |
| productKey | 是 | 产品pk |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**示例代码**

```objc
[[QuecDeviceService sharedInstance] regenerateAuthKeyWithDeviceKey:@"deviceKey" productKey:@"productKey" success:^(NSString *result) {
        /// Next Action
    } failure:} failure:^(NSError *error) {
        NSLog(@"check error: %@", error);
    }];
```

# SOTA Upgrade Example
## __Introduction__
SOTA upgrade of QuecThing generally refers to the process of writing new firmware into the external MCU and replacing the original firmware. In all upgrade modes, you need to upload the firmware package to Developer Center, and the device obtains the firmware package from Developer Center to realize the upgrade on the air. Finally, MCU receives the upgrade package and writes it into the local flash memory to realize a firmware upgrade. This section explains how to perform a SOTA upgrade in QuecOpen solution.


## __Related APIs__

|       Function        | Description                       |
| :-------------------: | :-------------------------------- |
|   Qth_otaRequest()    | Requests OTA upgrade plans.       |
| Qth_configSetMcuVer() | Sets module's version number.     |
| Qth_configGetMcuVer() | Gets the module's version number. |

<span style='color:#999AAA'>Note: See [OTA Upgrade API](/deviceDevelop/DeviceAccessPlan/wifi/QuecOpen/api/quecopen-api-07) and [Product Configuration API](/deviceDevelop/DeviceAccessPlan/wifi/QuecOpen/api/quecopen-api-02) for details.</span>


## __Procedure__
### __1. Log in to Developer Center__
Log in to <a href="https://core.acceleronix.io"  target="_blank">Developer Center</a>. If you don't have an account, click <a href="https://core.acceleronix.io/registerType" target="_blank">Register Now</a> to register.

### __2. Create a Product__
Creating a product is the first step in product development. A product is an abstract description of a class of devices defined by Developer Center. A product is used to manage devices of the same category. For example, if you have an aromatherapy diffuser with Wi-Fi networking capability and you need to connect the aromatherapy diffuser to Developer Center to monitor it, you can define it as a product "Smart Aromatherapy Diffuser".

### __3. OTA Upgrade Operations__
#### **a. Add a Version Package**

<a data-fancybox title="img" href="/en/deviceDevelop/develop/OTA/SOTA/03-2-1.png">![img](/en/deviceDevelop/develop/OTA/SOTA/03-2-1.png)</a>

* __Version Package Name__: Version package name.
* __Component Type__: Module firmware or MCU firmware.
  * Module firmware upgrade (FOTA): Write the new firmware to the Quectel module.
  * MCU firmware upgrade (SOTA): Download the new software package to the external MCU through the Quectel module.
* __Version Package Type__: Full Package or Delta Package.
  * Delta Package is for upgrading the specified original version, i.e., for minor feature upgrading or bug fixing.
  * Full Package is not for a specified source version, and all devices can be upgraded.
* __Source Version__: Version of the source firmware package. This item is displayed if "Delta Package" is selected, but not for the "Full Package". (Developer Center determines whether the version number in the device information reported when the device connects to Developer Center is consistent with this parameter. If it is consistent, Developer Center issues the upgrade information.)
* **Target Version**: New version number of the firmware package. (Once the device is upgraded, it needs to report the upgraded version number to Developer Center. Developer Center will check whether the version number reported by the device is consistent with this parameter. If it is consistent, the upgrade is successful.)
* **Upload File**: The file format should be .bin/.zip/.rar/.pack/.mini_1/.mini_2/.py/.tar/.tar.gz/.s19/.bin_1/.bin_2. Up to 5 files are supported and a single file size cannot exceed 100 MB.
* **Remarks**: Information describing the uploaded firmware version.


#### **b. Verify Firmware Package (Optional)**

Firmware verification is for ensuring the security and availability of the firmware to be mass upgraded to minimize the upgrade risk in case of unexpected losses. Before mass upgrading devices, you may verify a single device’s firmware and perform the OTA upgrade.

__i.__ Select the version package to be verified and click "**Verify Firmware**".

<a data-fancybox title="img" href="/en/deviceDevelop/develop/OTA/SOTA/03-2-2.png">![img](/en/deviceDevelop/develop/OTA/SOTA/03-2-2.png)</a>

__ii.__ Select the device to be verified, enter the component identifier to be verified and then submit the verification.

<a data-fancybox title="img" href="/en/deviceDevelop/develop/OTA/SOTA/03-2-3.png">![img](/en/deviceDevelop/develop/OTA/SOTA/03-2-3.png)</a>

__iii.__ Click "**Details**" of the version package to view the "Verification Details".

<a data-fancybox title="img" href="/en/deviceDevelop/develop/OTA/SOTA/03-2-4.png">![img](/en/deviceDevelop/develop/OTA/SOTA/03-2-4.png)</a>

#### **c. Create an Upgrade Plan**

An upgrade plan should be created or selected from existing plans before a device upgrade. The plan contains device(s) to be upgraded, upgrade time, components configurations and etc.

<a data-fancybox title="img" href="/en/deviceDevelop/develop/OTA/SOTA/03-2-5.png">![img](/en/deviceDevelop/develop/OTA/SOTA/03-2-5.png)</a>

__i. Complete Basic Information__

<a data-fancybox title="img" href="/en/deviceDevelop/develop/OTA/SOTA/03-2-6.png">![img](/en/deviceDevelop/develop/OTA/SOTA/03-2-6.png)</a>

* **Plan Name**: Plan name.
* **Product to Be Upgraded**: The product that needs upgrading.

* **Set Blacklist and Whitelist**: All devices under the product will be upgraded if no whitelist or blacklist is set. The blacklist has a higher priority than the whitelist if both the whitelist and blacklist are set.
  - **Whitelist**: Only the device(s) in the whitelist can be upgraded.
  - **Blacklist**: The device(s) in the blacklist cannot be upgraded.

* __Time Zone__: A required parameter. Developer Center will only push the upgrade plan during the push period in this time zone.

* __Push Period__: An optional parameter. The period of Developer Center automatically pushing the upgrade plan.
* __Upgrade Mode__:

  * __Silent Upgrade__: Upgrade is automatically completed by the application, without requiring any user confirmation. The device will be upgraded and restarted in the new version once it is opened again.

  * __Manual Upgrade__: You need to enter the "Device Control" page, check and confirm the firmware upgrade on the "Device Details" page. 

__ii. Formulate Upgrade Strategy__

During the firmware upgrade, it is inevitable that the upgrade fails due to a poor upgrade environment. To avoid this situation, you need to formulate an upgrade strategy to reduce the probability of upgrade failure. Click "**Custom**" next to the  "Upgrade Strategy" to configure the upgrade strategy in the pop-up window. 

<a data-fancybox title="img" href="/en/deviceDevelop/develop/OTA/SOTA/03-2-7.png">![img](/en/deviceDevelop/develop/OTA/SOTA/03-2-7.png)</a>


__iii. Add Upgrade Component__

The upgrade component is a collection of one or more components created by the user. It is used to select and plan the product components that need to be upgraded when you create the upgrade plan. A component identifier must be unique.

<a data-fancybox title="img" href="/en/deviceDevelop/develop/OTA/SOTA/03-2-8.png">![img](/en/deviceDevelop/develop/OTA/SOTA/03-2-8.png)</a>


__iv. Configure Upgrade Text__

Upgrade text configuration is required in manual upgrades but can be omitted in silent upgrades. You must configure Chinese and English upgrade texts, and the texts in other languages are optional.

<a data-fancybox title="img" href="/en/deviceDevelop/develop/OTA/SOTA/03-2-12.png">![img](/en/deviceDevelop/develop/OTA/SOTA/03-2-12.png)</a>



__v. Activate Upgrade Plan__

After completing the above steps of **Add a Version Package** and **Create an Upgrade Plan**, you can click "**Activate Plan**" in the lower left corner to activate the configured plan. Then Developer Center will execute the upgrade task and distribute the selected target version firmware to the target devices.

<span style="color:#999AAA">Note: The activated upgrade tasks cannot be deleted.</span>

#### __d. Initialize the Device and Connect It to Developer Center__
To establish device management and OTA upgrade, initialize the device and connect it to Developer Center. This connection needs to be safe and stable to ensure communication between the device and Developer Center.

```c
/* Initialize QuecThing SDK. */
Qth_cloudInit();
/* Set module's version number. */
Qth_mcuInfo_t mcuList[1] = {0};
Quos_strcpy(mcuList[0].identity, "STM32");
Quos_strcpy(mcuList[0].version, "1_0_0");
Qth_configSetMcuVer(mcuList, sizeof(mcuList) / sizeof(Qth_mcuInfo_t));
/* Register event callback functions. */
Qth_eventCb_t event_cb = {0};
Qth_otaEventCb_t otaCb = {0};
otaCb.planCb = otaPlanCb;
otaCb.downloadCb = otaDownloadCb;
otaCb.resultCb = otaResultCb;
otaCb.sotaInfoCb = sotaInfoCb;
otaCb.sotaFileCb = sotaFileCb;
event_cb.otaEventCb = otaCb;
Qth_configSetEventCb(event_cb);
/* Initiates the connection to Developer Center. */
Qth_cloudStart();
```
<span style="color:#999AAA">Note: The device connected to Developer Center over MQTT protocol will be automatically created on Developer Center.</span>

#### __e. Request an OTA Upgrade__

<span style="color:#999AAA">Precondition: Before a device requests an OTA upgrade, make sure that the device is connected to Developer Center.</span>

1）Once the upgrade plan is made and activated, the device can request an OTA upgrade by calling  __Qth_otaRequest()__.<br>
2）The device does not need to send an OTA upgrade request to Developer Center and Develop Center verifies the upgrade plan and issues the plan to the device every 10 minutes, such as 10:00 or 10:10.

* Sample code
	```c
	/* Request an OTA upgrade plan from Developer Center. */
	Qth_otaRequest(0);
	```


#### __f. Check Upgrade Result__

Once the device requests an OTA upgrade plan, it will automatically call the callback function **Qth_otaPlanCb_f** and report the OTA upgrade plan information to Developer Center if the device is included in the upgrade plan. The OTA upgrade plan information includes component identifier + source version + target version + minimum power + minimum signal strength + firmware package size. If the device meets the upgrade strategy requirements specified in the plan, it will return TRUE to accept the upgrade, or return FALSE to reject the upgrade.

* Issue the upgrade plan. The reported content is shown as follows (including component identifier, source version, target version, minimum power, minimum signal strength, and disk space required for OTA upgrade).

	Sample code of processing the event callback function:
	```c
	qbool otaPlanCb(Qth_otaPlan_t *planList, quint16_t count)
    {
        quint16_t i;
        for (i = 0; i < count; i++)
        {
            Quos_logPrintf(APPS_OPEN, LL_DBG, "identity:%s,type:%d,srcVer:%s,dstVer:%s,minBattery:%d,minFlash:%d,minSignal:%d",
                            planList[i].identity, (qint32_t)planList[i].type, planList[i].srcVer, planList[i].dstVer, (qint32_t)planList[i].minBattery, (qint32_t)planList[i].minFlash, (qint32_t)planList[i].minSignal);
        }
        return TRUE;
    }
	```

#### __g. MCU Confirms Upgrade Mode__

Once the device accepts the upgrade, Developer Center will issue the firmware information and download URL. The MCU can make the module download the firmware and send it to the MCU for upgrade, or the MCU downloads the firmware itself.

* Issue the firmware information. The reported content is shown as follows (including component identifier, firmware version, MD5 value of the firmware, CRC32 value of the firmware, SHA256 value of the firmware, firmware download URL and firmware size). The default download mode is that the module downloads the firmware. If you hope the MCU to download the firmware itself, set the parameter *type* to QTH_SOTA_DOWN_TYPE_MCU.

	Sample code of processing the event callback function:
	```c
	qbool sotaInfoCb(char *identity, Qth_otaInfo_t *info, Qth_otaDownType_e *type)
    {
        UNUSED(type);
        Quos_logPrintf(APPS_OPEN, LL_DBG, "identity:%s,version:%s,fileMd5:%s,fileCrc:%s,fileSha256:%s,fileUrl:%s,fileSize:%d",
                        identity, info->version, info->fileMd5, info->fileCrc, info->fileSha256, info->fileUrl, (qint32_t)info->fileSize);
        return TRUE;
    }
	```

#### __h. Device Upgrade Process Feedback__

A successful SOTA upgrade involves 4 steps: __receive upgrade plan__, __download in progress__,  __transfer firmware__ and __successful upgrade__. Each upgrade step triggers a corresponding event reported through the callback function. The SOTA upgrade process feedbacks are as follows.


* Firmware download in progress (The downloading result will be sent through the callback function every 10% download)
	```c	
    void otaDownloadCb(char *identity, quint8_t percent)
    {
        Quos_logPrintf(APPS_OPEN, LL_DBG, "identity:%s, percent:%d", identity, percent);
    }
	```
	
* MCU firmware updating

  When the module receives the new firmware, it will send the firmware data through *Qth_sotaFileCb_f()*. You can carry out the corresponding processing and transfer the firmware data to the MCU through the serial port or other connection methods.

  ```c
  qbool sotaFileCb(char *identity, quint32_t offset, quint8_t *data, quint32_t dataLen)
    {
        Quos_logPrintf(APPS_OPEN, LL_DBG, "identity:%s,offset:%d,dataLen:%d", identity, offset, dataLen);
        /* Write firmware data to the MCU */
        /* to do something */
        /* When dataLen = 0, it indicates that the firmware has been transferred to the MCU. */
        if(0 == dataLen)
        {
            /* to do something */
        }
        return TRUE;
    }
  ```

* Upgrade the MCU version number (When the MCU upgrade is completed, the MCU version number needs to be updated and sent to the SDK).
	```c
    Qth_mcuInfo_t mcuList[1] = {0};
    Quos_strcpy(mcuList[0].identity, "STM32");
    Quos_strcpy(mcuList[0].version, "1_0_1");
    Qth_configSetMcuVer(mcuList, sizeof(mcuList) / sizeof(Qth_mcuInfo_t));
	```

* Firmware upgrade result

  ```python
  void otaResultCb(char *identity, qbool result)
  {
      Quos_logPrintf(APPS_OPEN, LL_DBG, "identity:%s, result:%d", identity, result);
  }
  ```

### __4. Monitor Upgrade__

Click "__Upgrade Monitoring__" in the upgrade plan list. In the device upgrade list, you can view the upgrade status of all devices in this plan.

<a data-fancybox title="img" href="/en/deviceDevelop/develop/OTA/SOTA/03-2-9.png">![img](/en/deviceDevelop/develop/OTA/SOTA/03-2-9.png)</a>

Click "__Component Details__" to view the upgrade logs as shown in the following figure.

<a data-fancybox title="img" href="/en/deviceDevelop/develop/OTA/SOTA/03-2-10.png">![img](/en/deviceDevelop/develop/OTA/SOTA/03-2-10.png)</a>


<a data-fancybox title="img" href="/en/deviceDevelop/develop/OTA/SOTA/03-2-11.png">![img](/en/deviceDevelop/develop/OTA/SOTA/03-2-11.png)</a>




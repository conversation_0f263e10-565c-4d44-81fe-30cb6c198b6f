<!--
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2025-04-18 10:14:21
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2025-04-23 17:58:45
 * @FilePath: \quec-doc-web\docs\zh\deviceDevelop\DeviceAccessPlan\KnowledgeBase\page-02.md
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<!--
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2025-04-18 10:14:21
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2025-04-22 15:28:39
 * @FilePath: \quec-doc-web\docs\zh\deviceDevelop\DeviceAccessPlan\KnowledgeBase\page-02.md
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
# TTLV格式与JSON格式的区别

<span style="color:#999AAA">提示：本文主要描述设备侧选择数据格式时，TTLV格式数据与JSON格式数据的区别。</span>

# **TTLV**

TTLV（即Tag，Type，Length，Value）是一种按照协议规定处理后的16进制数据，TTLV格式将数据压缩为较小的体积，对于空间的利用率高，不允许冗余字段的产生。

## **TTLV主要特性**
* **数据紧凑**：TTLV格式数据紧凑，格式简洁，因此数据量小，在模组与MCU间的传输更高效。
* **安全性高**：没有协议规定文档的情况下，无法解读出数据内容。
* **数据不直观**：数据内容不直观，TTLV格式数据需要根据协议规定文档解析后才能解读。

## **TTLV 格式**
| 字段               | 长度                         | 说明                                                                                                                               |
| ------------------ | ---------------------------- | ---------------------------------------------------------------------------------------------------------------------------------- |
| 数据标识（id）     | 13 Bit                       | 范围 1~8191，在不同的命令(cmd)内唯一（如物模型，设备状态，模组信息等）                                                             |
| 数据类型（type）   | 3 Bit                        | 目前已经定义的数据类型（二进制）：<br>布尔 false：000<br>布尔 true：001<br />枚举和数值：010<br />二进制数据：011<br />结构体：100 |
| 数据长度（length） | 不同数据类型 length 位数不同 | 不同数据类型 length 位数不同，详见下表                                                                                             |
| 数据值（value）    |                              | 详见下表                                                                                                                           |


## **数据长度及数据值详情解析**
| 数据类型   | 数据长度（length）                                                                                         | 数据值（value）                                     |
| ---------- | ---------------------------------------------------------------------------------------------------------- | --------------------------------------------------- |
| 布尔 false | 无                                                                                                         | 无                                                  |
| 布尔 true  | 无                                                                                                         | 无                                                  |
| 枚举和数值 | 1 Bit （标识位，0 为正，1 为负）<br>4 Bit （衰减 10 的 N 次方 ）<br>3 Bit （数据字节数，数据域的字节数-1） | 数据内容                                            |
| 二进制数据 | 2 Byte （数据字节数）                                                                                      | 数据内容                                            |
| 结构体     | 2 Byte （成员个数）                                                                                        | 数据内容（若为数组类型，则没有功能ID，使用 0 标识） |


**举例说明**：
| 功能ID | 类型       | 数值                                                                                                                                                                                                                                                                                                                                      | 二进制数据                                                                                                                                                                                               | 16进制数据                                                                                          |
| ------ | ---------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------------------------------- |
| 1      | BOOL       | false   | 0000 0000 0000 1000   | 0x00 0x08         |
| 2      | BOOL       | true  | 0000 0000 0001 0001   | 0x00 0x11     |
| 3      | 数值       | 4  | 0000 0000 0001 1010 0000 0000 0000 0100        | 0x00 0x1A 0x00 0x04        |
| 4      | 数值       | 5     | 0000 0000 0010 0010 0000 0000 0000 0101    | 0x00 0x22 0x00 0x05      |
| 5      | 数值       | 258   | 0000 0000 0010 1010 0000 0001 0000 0001 0000 0010  |  0x00 0x2A  0x01 0x01 0x02   |
| 6      | 数值       | 0.18   |0000 0000 0011 0010 0001 0000 0001 0010 | 0x00  0x32  0x10 0x12   |
| 7      | 数值       | -0.32 |0000 0000 0011 1010 1001 0000 0010 0000  | 0x00 0x3A 0x90 0x20    |
| 8      | 字符串	  | 0xAB  |0000 0000 0100 0011 0000 0000 0000 0010 0100 0001 0100 0010          |  0x00 0x43 0x00 0x02 0x41   0x42   |
| 9      | 结构体     | 参数功能ID：1 ，类型： Bool 值：false <br>参数功能ID：2 ，类型： 数值 值：10 |0000 0000 0100 1100 0000 0000 0000 0001 0000 0000 0000 1000 | 0x00 0x4C 0x00 0x01 0x00 0x08 |

**图文解析**：

<a data-fancybox title="img" href="/zh/deviceDevelop/KnowledgeBase/pict-1.png">![img](/zh/deviceDevelop/KnowledgeBase/pict-1.png)</a>

<a data-fancybox title="img" href="/zh/deviceDevelop/KnowledgeBase/pict-2.png">![img](/zh/deviceDevelop/KnowledgeBase/pict-2.png)</a>

# **JSON**
JSON 是基于 JavaScript（Standard ECMA-262 3rd Edition - December 1999）的一个子集，是一种开放的、轻量级的数据交换格式，采用独立于编程语言的文本格式来存储和表示数据。JSON对象是一个无序的 “名称/值” 键值对的集合：以 “{” 开始，以 “}” 结束。JSON格式组成部分如下：

```c
{
    "key":vlaue
}
```

## **JSON主要特性**
* **自我描述性**：JSON格式数据内容直观，易于开发者阅读与编写。
* **格式简单**：JSON格式数据比较简单，在编写MCU程序中可较容易进行组包与解析。


**举例说明**：

| 功能ID | 数据类型 | 数值                                     | JSON数据                                       |
| ------ | -------- | ---------------------------------------- | ---------------------------------------------- |
| 1      | BOOL     | false                                    | <span v-pre>{"1":false}</span>                 |
| 2      | BOOL     | true                                     | <span v-pre>{"2":true} </span>                 |
| 3      | INT      | 100                                      | <span v-pre>{"3":100}                  </span> |
| 4      | DOUBLE   | 12.3                                     | <span v-pre>{"4":12.3}                 </span> |
| 5      | FLOAT    | 25.6                                     | <span v-pre>{"5":25.6}                 </span> |
| 6      | ENUM     | 1                                        | <span v-pre>{"6":1}                    </span> |
| 7      | TEXT     | ABC123                                   | <span v-pre>{"7":"ABC123"}             </span> |
| 8      | DATE     | 1659664758817                            | <span v-pre>{"8":1659664758817}        </span> |
| 9      | STRUCT   | <span v-pre> {"10":true,"11":100}</span> | <span v-pre>{"9":{"10":true,"11":100}} </span> |
| 12     | ARRAY    | [10,20,30]                               | <span v-pre>{"12":[10,20,30]}          </span> |





# 该如何选择数据格式?
* JSON格式数据组合方便，便于阅读，不需要移植特定的库文件生成和解析数据，但发送的数据字节数相对于TTLV较多，所以在发送物模型数据较少的情况下，建议使用JSON数据格式
* TTLV格式数据紧凑，数据格式相对于JSON更加简洁，但是MCU内需要移植TTLV库，所以在MCU资源充足且发送物模型数据量大的情况下，建议使用TTLV数据格式。


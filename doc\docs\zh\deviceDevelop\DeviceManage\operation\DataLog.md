<!--
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-11-15 15:11:01
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2024-11-26 19:44:44
 * @FilePath: \quec-doc-web\docs\zh\DeviceDevelop\devicemanage\operation\DataLog.md
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
# 数据日志

本文主要介绍数据日志，所谓数据日志是指的设备发送的上下行数据记录。

## **前提条件**

● 创建产品<br />
● 完成产品开发-设备调试步骤<br />
● 发送过设备上下行数据

## **操作步骤**

1..登录开发者中心后，点击左侧菜单“设备管理”→ “设备运维”，出现设备列表页，点击设备操作栏中“查看”，跳转至“设备信息” tab页，点击“数据日志”tab页。

<a data-fancybox title="img" href="/zh/guide/image2022-3-8_20-11-43.png?version=1&modificationDate=1646740927000&api=v2">![img](/zh/guide/image2022-3-8_20-11-43.png?version=1&modificationDate=1646740927000&api=v2)</a>

参数说明

| 参数     | 描述                                                         |
| :------- | :----------------------------------------------------------- |
| Ticket   | 此条数据的唯一标识                                           |
| 创建时间 | 此数据入库的时间                                             |
| 数据类型 | **上行：** 是指设备上报开发者中心，产生的指令或消息。**下行：** 是指通过开发者中心API或界面，向设备发送的指令或消息。 |
| 发送状态 | **待发送：** 是指从用户通过设备调试或者应用的API进行的命令下行时，若设置了缓存时间，且设备不在线，下行数据将处于”**待发送**“的状态，在缓存时间内设备再次上线或发送上行数据时，将进行缓存数据下发。此状态仅在下行数据时存在。</br> **发送成功：** 下行发送成功是指从用户通过设备调试或者应用的API进行的命令下行，下行数据发送到了设备，并在5秒内收到响应成功的信息。上行发送成功是指从设备到开发者中心上行成功。</br>**发送失败：** 上行发送失败是指从设备到开发者中心网关服务连接成功，但是没有进行后续的处理，服务器、网络等环境出现了异常。下行发送失败是指从用户通过设备调试或者应用的API进行的命令下行，下行数据到设备，并在5秒内未收到响应成功的信息；大多数情况下的**发送失败**，是模组与开发者中心的连接已经断开，设备未主动注销，造成了伪连接。|
| 数据内容 | 可以点击查看，选择多种数据格式查看。**HexString** ：将数据或者byte数组转换为十六进制字符;(示例：二进制内容0x1234AB(3 bytes)转换成字符串为"1234AB"(6 bytes);字符串"1234AB"(6 bytes)转换成二进制内容为0x1234AB(3 bytes))。**Base64** ：基于64个可打印字符（大写A-Z，小写a-z，及+,/）来表示二进制数据。**Text** ： 以UTF-8编码格式呈现String的方式。**Json** ： 物模型定义进行传输和展现的格式。|
| 发送时间 | 下行时代表数据从平台发送到设备的时间。上行时代表平台收到上行数据的时间。 |


# 消息订阅

本文介绍SaaS开发第三步，消息订阅。

整体消息订阅的步骤和流程如下：

<a data-fancybox title="img" href="/zh/guide/image2022-3-21_16-14-0.png?version=1&modificationDate=1646716773000&api=v2">![img](/zh/guide/image2022-3-21_16-14-0.png?version=1&modificationDate=1646716773000&api=v2)</a>

## **前提条件**

● 已创建至少一个SaaS应用

## **操作步骤**

### **1.队列管理**

1.登录开发者中心后，点击“SaaS开发”→"消息订阅"，进入消息订阅列表页。

<a data-fancybox title="img" href="/zh/guide/image2022-0706-03.png?version=1&modificationDate=1646655516000&api=v2">![img](/zh/guide/image2022-0706-03.png?version=1&modificationDate=1646655516000&api=v2)</a>

2.点击“消息队列”tab页，首先需要创建一个队列用于之后的订阅。

输入一个队列名称，如果有必要可以输入队列的描述，用于更加清晰的表达该队列的作用或意义。

注：队列名称的约束：需要账号内唯一，最大长度64个字符，支持大小写英文字符串(区分大小写)、数字、下划线(_)、中划线(-)、不支持斜杠(/)、空格等其他字符。

3.创建完成后，队列列表会多出一个队列，可以点击操作栏对其进行查看和删除操作。

<span>注：队列的命名规则：a.{标识id}.{队列名称}</span>

每个用户最多可创建50个队列，30天未使用的消息队列将会被停用，用以回收资源

4.点击“查看”消息队列，可以查看消息队列消费信息、队列信息、连接信息。

当消息堆积量大于0时，可以选择手动清理。

| 队列状态 | 描述                                                                                         |
| :------- | :------------------------------------------------------------------------------------------- |
| 未启用   | 代表该队列未有相关订阅                                                                       |
| 启用     | 代表队列有相关订阅存在，当某队列相关订阅都被删除时，该队列状态变更为未启用。                 |
| 停用     | 代表该队列长时间无消费，已经被停用。被停用的队列不能被恢复，在查看详情时会多显示一列停用时间 |

### **2.订阅管理**

1.消息队列建立好，可以针对队列进行消息订阅了。切换至“订阅列表”tab页，点击“创建订阅”按钮，弹出如下对话框

<a data-fancybox title="img" href="/zh/guide/image2022-0706-04.png?version=1&modificationDate=1646710965000&api=v2">![img](/zh/guide/image2022-0706-04.png?version=1&modificationDate=1646710965000&api=v2)</a>

参数说明

| 参数                    | 描述                                                                                                                                                                                                                                                                                                                      |
| :---------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| 订阅名称                | 输入订阅名称，最大长度128字符， 支持中文、大小写英文字符串(区分大小写)、数字、下划线(_)、中划线(-)、不支持斜杠(/)、空格等其他字符。                                                                                                                                                                                       |
| 选择产品                | 消息订阅针对具体产品进行订阅                                                                                                                                                                                                                                                                                              |
| 数据级别                | 产品级别：针对上述选择的产品下所有设备数据进行订阅。设备级别：针对下选择的产品下具体设备数据进行订阅。当选择设备级别时，页面可模糊搜索和查询具体设备、                                                                                                                                                                    |
| 消息类型（物模型）      | 选择订阅的消息类型，可多选 设备上下线事件、设备和模组状态、设备命令响应数据、物模型属性信息、物模型事件上报-信息、物模型事件上报-告警、物模型事件上报-故障、物模型服务调用日志、设备定位下行信息、设备定位原始信息、设备定位信息、设备信息变更。 具体消息类型数据格式参见 [SaaS应用开发](/saasDevelop/CommunicatOverview) |
| 消息类型（透传/自定义） | 选择订阅的消息类型，可多选 设备上下线事件、设备和模组状态、设备上行数据、设备下行数据、设备命令响应数据、设备定位下行信息、设备定位原始信息、设备定位信息、设备信息变更。 具体消息类型数据格式参见 [SaaS应用开发](/saasDevelop/CommunicatOverview)                                                                        |
| 消息队列                | 选择或创建一个消息队列用来绑定此订阅                                                                                                                                                                                                                                                                                      |

2.订阅创建完成后为未启动状态，需要点击“**启动**”才能使订阅规则生效。

>备注: 已启动的订阅规则不能进行修改和删除，如果要修改订阅规则，需要先点击“**停止**”，在订阅停止后方可进行操作。

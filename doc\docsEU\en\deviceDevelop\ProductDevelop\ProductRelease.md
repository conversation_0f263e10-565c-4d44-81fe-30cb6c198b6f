# **Debugging Release**

Product release is a process of locking the product status to prevent unauthorized modifications to critical data. Its main purpose is to ensure device availability and prevent data parsing error caused by console misoperations or other unintended changes.

On the "**Debugging Release**" page, you can view the number of activation codes assigned to the product. If no activation codes are available, new devices cannot be activated, and you must assign activation codes first.

After completing the activation code assignment, enter the DeviceKey that you plan to connect to Developer Center. Once the device is successfully activated, you can use the data debugging feature to debug uplink and downlink data.

<a data-fancybox title="img" href="/en/deviceDevelop/creatproduct/productrelease01.png">![img](/en/deviceDevelop/creatproduct/productrelease01.png)</a>

## Product Release

After the device is successfully activated, you can click "**Release**". Before releasing, please check whether all configuration items have been confirmed. After the product is released, the TSL model cannot be edited and the web-based device debugging feature cannot be used.

If adjustments to the TSL model are required after releasing, you may revert to the "**Developing**" status by clicking "**Cancel Release**". Modifications to the TSL model may affect shipped devices. Please proceed with caution.

<a data-fancybox title="img" href="/en/deviceDevelop/creatproduct/productrelease02.png">![img](/en/deviceDevelop/creatproduct/productrelease02.png)</a>
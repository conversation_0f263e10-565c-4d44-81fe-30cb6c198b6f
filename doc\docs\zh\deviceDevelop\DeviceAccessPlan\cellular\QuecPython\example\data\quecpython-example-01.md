# 物模型格式数据业务交互示例


## __场景描述__
本章节指导用户通过 QuecPython 接入方案连接上开发者中心并进行物模型数据格式的业务交互。

## __涉及指令__


|     函数      | 说明               |
| :-----------: | :----------------- |
| Qth.sendTsl() | 发送物模型数据     |
| Qth.ackTsl()  | 应答物模型读取请求 |

<span style='color:#999AAA'>提示：详情请参考[数据业务交互 相关接口](/deviceDevelop/DeviceAccessPlan/cellular/QuecPython/api/quecpython-api-05)。</span>

## __操作步骤__
### __平台侧__
#### __一、登录开发者中心__
登录<a href="https://iot.quectelcn.com" target="_blank">开发者中心</a>，未注册可单击<a href="https://iot.quectelcn.com/registerType" target="_blank">立即注册</a>完成注册。

#### __二、创建产品__


创建产品详情请参考 [创建产品](/deviceDevelop/DeviceAccessPlan/speediness-01)。


## __设备侧__
### __物模型数据交互__

物模型是开发者中心针对物理实体设备在平台端建立的数据模型，主要用于描述产品的功能，从而方便数据的解析。设备成功接入平台后，通过构建物模型数据来实现与开发者中心的数据交互。


#### __示例一(主动发送物模型数据)__

|    功能ID    | 功能类型 | 功能名称                        | 数据类型 | 数值范围                        | 引用关系       |
| :----------: | :------- | :------------------------------ | :------- | :------------------------------ | :------------- |
|      1       | 属性     | 开关                            | BOOL     | true - 开启；<br>false - 关闭； |
|      2       | 属性     | 电池电量                        | INT      | 0~100                           |
|      3       | 属性     | 精油剩余容量                    | FLOAT    | 0~300                           |
|      4       | 属性     | 工作模式                        | ENUM     | 1~HPC<br>2~CPL                  |
|      5       | 属性     | 设备编号                        | TEXT     | 0~20                            |
|      6       | 属性     | 时间                            | DATE     | --                              |
|      7       | 属性     | 结构体                          | STRUCT   | --                              | 功能ID：8，9   |
|  8(父ID：7)  | 参数     | <span v-pre> {$参数名称}</span> | BOOL     | true - 开启；<br>false - 关闭； |
|  9(父ID：7)  | 参数     | <span v-pre> {$参数名称}</span> | INT      | 0~100                           |
|      10      | 属性     | 整型数组                        | ARRAY    | 最大数量：10，元素类型：INT     |
|      11      | 属性     | 结构体数组                      | ARRAY    |                                 | 功能ID：12，13 |
| 12(父ID：11) | 参数     | <span v-pre> {$参数名称}</span> | BOOL     | true - 开启；<br>false - 关闭； |
| 13(父ID：11) | 参数     | <span v-pre> {$参数名称}</span> | INT      | 0~100                           |

设备端可根据业务场景需求，对设备端数据属性采取一定的策略进行检测处理，例如设备端读取香薰机传感器精油剩余容量值等数据后，设备则将读取到的精油剩余容量值等数据上传到开发者中心。
* 示例代码如下所示：
	```python
    # 创建一个空字典
	my_dict = {}  
	# 添加布尔类型数据 {"1":true}
    my_dict.update({"1":true})
	# 添加整型数据  {"2":100}
	my_dict.update({"2":100})
	# 添加浮点型数据  {"3":25.6}
	my_dict.update({"3":25.6})
	# 添加ENUM数据  {"4":1}
	my_dict.update({"4":1})
	# 添加字符串类型数据  {"5":"ZWS"}
	my_dict.update({"5":"ZWS"})
	# 添加字符串类型数据  {"6":1659664758817}
	my_dict.update({"6":1659664758817})
	# 添加结构体类型数据  {"7":{"8":true,"9":100}}
	my_dict.update({"7":{"8":true,"9":100}})
	# 添加数组INT类型数据  {"10":[10,20,30]}
	my_dict.update({"10":[10,20,30]})
	# 添加数组结构体类型数据，引用参数：12、13 {"11":[{"12":true,"13":100},{"12":false,"13":98}]}
	my_dict.update({"11":[{"12":true,"13":100},{"12":false,"13":98}]})
	# 发送数据，QOS模式：1
	Qth.sendTsl(1,my_dict)
	```

#### __示例二(响应平台读取数据)__
__1、开发者中心批量读取物模型实时数据__

在开发者中心 __设备信息__ 详情页的 __设备调试__ 中，点击 __批量读取__ 按钮可一键批量读取物模型属性数值。


__2、模组收到查询指令处理__

当模组接收到物模型请求数据后，则会自动调用 __readTsl()__ 事件回调函数，您可根据下发的物模型ID数组进行数据响应处理。

* 事件回调处理示例代码如下所示：
	```python
    def App_cmdReadTslCb(ids, pkgId):
        print('readTsl:', ids, pkgId)
        # 创建一个空字典
        my_dict = {}  
        for id in ids:
            if id == 1:
                my_dict.update({"1":true})
            elif if id == 2:
                my_dict.update({"2":100})
            elif if id == 3:
                my_dict.update({"3":25.6})
            elif if id == 4:
                my_dict.update({"4":1})
            elif if id == 5:
                my_dict.update({"5":"ZWS"})
            elif if id == 6:
                my_dict.update({"6":1659664758817})
            elif if id == 7:
                my_dict.update({"7":{"8":true,"9":100}})
            elif if id == 10:
                my_dict.update({"10":[10,20,30]})
            elif if id == 11:
                my_dict.update({"11":[{"12":true,"13":100},{"12":false,"13":98}]})
        Qth.ackTsl(0, my_dict, pkgId)
	```


<span style="color:#999AAA">提示：return的ttlvHead的内存会由SDK自行释放，用户可无需关心，只需要把回复的物模型数据增加到ttlvHead链表中即可。</span>

#### __示例三(主动上报物模型告警事件)__

|    功能ID    | 功能类型 | 功能名称     | 数据类型 | 数值范围 | 引用关系           |
| :----------: | :------- | :----------- | :------- | :------- | :----------------- |
|      14      | 事件     | 剩余精油不足 | BOOL     |          | 输出功能ID：15，16 |
| 15(父ID：14) | 参数     | 电池电量     | INT      | 0~100    |
| 16(父ID：14) | 参数     | 精油剩余容量 | FLOAT    | 0~300    |

模拟精油不足时主动上告给平台端，该事件包含输出参数：__精油当前容量__，将输出参数 __ID：15，16__ 包含进告警事件内组成结构体一并发送到开发者中心。

* 示例代码如下所示：
	```python
	# 创建一个空字典
    my_dict = {}  
	# 添加物模型事件类型数据，引用参数：15、16
    my_dict.update({"14":{"15":true,"16:20.5"}})
	# 发送物模型数据至平台
	Qth.sendTsl(1,my_dict)
	```

#### __示例四(读取平台下发的物模型服务数据)__

|    功能ID    | 功能类型 | 功能名称 | 数据类型 | 数值范围 | 引用关系       |
| :----------: | :------- | :------- | :------- | :------- | :------------- |
|      17      | 服务     | 服务     | BOOL     |          | 输出功能ID：18 |
| 18(父ID：17) | 输入参数 | 模式     | ENUM     | 0~2      |


__1、开发者中心下发物模型服务__

 在开发者中心 __设备调试__ 页面，第一步点击 __服务调用__，第二步选择要下发的物模型服务，第三步点击左下角的 __发送指令__ 即可下发物模型服务给终端。


__2、设备接收到数据__

当模组接收到物模型数据后，则会自动调用recvTsl回调函数，您可进行对应的数据响应处理。

* 事件回调函数如下所示：
	```python
    def App_cmdRecvTslServerCb(serverId, value, pkgId):
        print('recvTslServer:', serverId, value, pkgId)
        if serverId == 17:
            num = value["18"]
            print('num:', num)
	```


 


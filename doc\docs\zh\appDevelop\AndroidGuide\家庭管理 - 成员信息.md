# 成员信息

## 功能概述

本文介绍了如何管理家庭中的成员, 包括添加成员, 修改成员信息, 移除成员, 离开家庭, 查询家庭成员列表等。

## 成员管理

### 邀请家庭成员

**接口说明**

邀请家庭成员

```kotlin
fun inviteFamilyMember(
    inviteModel: QuecInviteFamilyMemberParamModel, callback: QuecCallback<Unit>
)
```

**参数说明**

| 参数          | 	是否必传 | 说明     |	
|-------------|-------|--------| 
| inviteModel | 	是    | 邀请信息模型 | 
| callback    | 是     | 请求回调   |

**QuecInviteFamilyMemberParamModel属性定义**

| 字段          | 类型     | 描述                         |
|-------------|--------|----------------------------|
| fid         | String | 家庭Id，邀请成员时必填               |
| memberRole  | String | 成员角色：2-管理员  3-普通成员，邀请成员时必填 |
| memberName  | String | 成员名称，邀请成员时非必填              |
| invalidTime | Long   | 邀请失效时间，毫秒时间戳，邀请成员时必填       |
| phone       | String | 手机号，邀请成员时非必填               |
| email       | String | 邮箱，邀请成员时非必填                |
| uid         | String | 用户Id，邀请成员时非必填              |

**示例代码**

```kotlin
QuecSmartHomeService.inviteFamilyMember(
    QuecInviteFamilyMemberParamModel(
        "fid",
        "3",
        "memberName",
        System.currentTimeMillis() + 90000,
        null,
        null,
        null
    )
) {
    if (it.isSuccess) {
        //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 家庭成员邀请的处理

**接口说明**

家庭成员邀请的处理, 其他用户邀请加入他的家庭后, 被邀请成员调用.

```kotlin
fun familyMemberInviteHandle(fid: String, decide: Int, callback: QuecCallback<Unit>)
```

**参数说明**

| 参数       | 	是否必传 | 说明                         |	
|----------|-------|----------------------------| 
| fid      | 	是    | 家庭Id                       | 
| decide   | 	是    | 对于邀请的决定：0-拒绝邀请   1-同意邀请，必填 | 
| callback | 是     | 请求回调                       |

**示例代码**

```kotlin
QuecSmartHomeService.familyMemberInviteHandle("fid", 1) {
    if (it.isSuccess) {
        //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 修改家庭成员名称

**接口说明**

修改家庭成员名称

```kotlin
fun setFamilyMemberName(
    fid: String, memberUid: String, memberName: String, callback: QuecCallback<Unit>
)
```

**参数说明**

| 参数         | 	是否必传 | 说明       |	
|------------|-------|----------| 
| fid        | 	是    | 家庭Id     | 
| memberUid  | 	是    | 家庭成员用户Id | 
| memberName | 	是    | 成员名称     | 
| callback   | 是     | 请求回调     |

**示例代码**

```kotlin
QuecSmartHomeService.setFamilyMemberName("fid", "memberUid", "newName") {
    if (it.isSuccess) {
        //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 修改家庭成员角色

**接口说明**

修改家庭成员角色

```kotlin
fun setFamilyMemberRole(
    fid: String, memberUid: String, memberRole: String, callback: QuecCallback<Unit>
)
```

**参数说明**

| 参数         | 	是否必传 | 说明                 |	
|------------|-------|--------------------| 
| fid        | 	是    | 家庭Id               | 
| memberUid  | 	是    | 家庭成员用户Id           | 
| memberRole | 	是    | 成员角色：2-管理员  3-普通成员 |  
| callback   | 是     | 请求回调               |

**示例代码**

```kotlin
QuecSmartHomeService.setFamilyMemberRole("fid", "memberUid", "2") {
    if (it.isSuccess) {
        //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 移除家庭成员

**接口说明**

移除家庭成员

```kotlin
fun deleteFamilyMember(
    fid: String, memberUid: String, callback: QuecCallback<Unit>
)
```

**参数说明**

| 参数        | 	是否必传 | 说明       |	
|-----------|-------|----------| 
| fid       | 	是    | 家庭Id     | 
| memberUid | 	是    | 家庭成员用户Id | 
| callback  | 是     | 请求回调     |

**示例代码**

```kotlin
QuecSmartHomeService.deleteFamilyMember("fid", "memberUid") {
    if (it.isSuccess) {
        //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 离开家庭

**接口说明**

家庭成员离开当前家庭

```kotlin
fun leaveFamily(fid: String, callback: QuecCallback<Unit>)
```

**参数说明**

| 参数       | 	是否必传 | 说明   |	
|----------|-------|------| 
| fid      | 	是    | 家庭Id | 
| callback | 是     | 请求回调 |

**示例代码**

```kotlin
QuecSmartHomeService.leaveFamily("fid") {
    if (it.isSuccess) {
        //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 家庭中的家庭成员列表

**接口说明**

查询家庭中的家庭成员列表

```kotlin
fun getFamilyMemberList(
    fid: String,
    pageNumber: Int,
    pageSize: Int,
    callback: QuecCallback<QuecPageResponse<QuecFamilyMemberItemModel>>
)
```

**参数说明**

| 参数         | 	是否必传 | 说明           |	
|------------|-------|--------------| 
| fid        | 	是    | 家庭id         | 
| pageNumber | 	否    | 页码，非必填，默认1   | 
| pageSize   | 	否    | 页大小，非必填，默认10 | 
| callback   | 是     | 请求回调         |

**QuecFamilyMemberItemModel属性定义**

| 字段                      | 类型     | 描述                   |
|-------------------------|--------|----------------------|
| uid                     | String | 用户Id                 |
| phone                   | String | 手机号                  |
| nikeName                | String | 昵称                   |
| sex                     | String | 性别                   |
| address                 | String | 地址                   |
| email                   | String | 邮箱                   |
| headimg                 | String | 头像                   |
| wechatMiniprogramUserId | String | 小程序Id                |
| wechatUnionId           | String | 微信Id                 |
| appleUserId             | String | apple Id             |
| twitterUserId           | String | twitter Id           |
| facebookUserId          | String | facebook Id          |
| alipayUserId            | String | alipay Id            |
| qqUserId                | String | qq Id                |
| wechatOffiaccountUserId | String | wechatOffiaccount Id |
| registerTime            | String | 注册时间                 |
| registerTimeTs          | Long   | 注册时间戳                |
| lastLoginTime           | String | 上次登录时间               |
| lastLoginTime           | String | 上次登录时间戳              |
| timezone                | String | 时区                   |
| nationality             | String | 国家                   |
| province                | String | 省                    |
| city                    | String | 市                    |
| lang                    | String | 语言                   |
| status                  | Int    | 状态                   |
| signature               | String | 签名                   |
| remark                  | String | 备注                   |
| memberRole              | Int    | 角色类型                 |
| memberName              | String | 名称                   |

**示例代码**

```kotlin
QuecSmartHomeService.getFamilyMemberList("fid", 1, 10) {
    if (it.isSuccess) {
        val data = it.data //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 被邀请列表

**接口说明**

查询被邀请列表

```kotlin
fun getFamilyInviteList(
    pageNumber: Int,
    pageSize: Int,
    callback: QuecCallback<QuecPageResponse<QuecInviteItemModel>>
)
```

**参数说明**

| 参数         | 	是否必传 | 说明           |	
|------------|-------|--------------| 
| pageNumber | 	否    | 页码，非必填，默认1   | 
| pageSize   | 	否    | 页大小，非必填，默认10 | 
| callback   | 是     | 请求回调         |

> QuecInviteItemModel属性定义同上

**示例代码**

```kotlin
QuecSmartHomeService.getFamilyInviteList(1, 10) {
    if (it.isSuccess) {
        val data = it.data //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误
    }
}
```

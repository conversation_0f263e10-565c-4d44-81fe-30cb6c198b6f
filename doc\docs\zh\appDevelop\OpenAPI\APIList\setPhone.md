# 修改用户手机号


**接口地址**:`/v2/enduser/enduserapi/setPhone`


**请求方式**:`PUT`


**请求数据类型**:`application/json`


**响应数据类型**:`*/*`


**接口描述**:<p>修改用户手机号</p>


**请求参数**:


| 参数名称             | 参数说明                 | 请求类型 | 是否必须 | 数据类型 | schema |
| -------------------- | ------------------------ | -------- | -------- | -------- | ------ |
| newInternationalCode | 新手机号码国际代码       | query    | true     | string   |        |
| newPhone             | 新手机号码               | query    | true     | string   |        |
| newPhoneCode         | 新手机号码接收到的验证码 | query    | true     | string   |        |
| oldInternationalCode | 原手机号码国际代码       | query    | true     | string   |        |
| oldPhone             | 原手机号码               | query    | true     | string   |        |
| oldPhoneCode         | 原手机号码接收到的验证码 | query    | true     | string   |        |


**响应状态**:


| 状态码 | 说明                                              | schema             |
| ------ | ------------------------------------------------- | ------------------ |
| 200    | 用户手机号修改成功                                | 返回注册码响应数据 |
| 5032   | token 验证失败                                    |                    |
| 5033   | 用户不存在                                        |                    |
| 5162   | 请输入原手机号码国际代码                          |                    |
| 5163   | 请输入原手机号码                                  |                    |
| 5164   | 请输入原手机号码接收到的验证码                    |                    |
| 5165   | 请输入新手机号码国际代码                          |                    |
| 5166   | 请输入新手机号码                                  |                    |
| 5167   | 请输入新手机号码接收到的验证码                    |                    |
| 5168   | 原手机号码验证失败                                |                    |
| 5169   | 新手机号码验证失败                                |                    |
| 5170   | 用户手机号码修改失败                              |                    |
| 5174   | 原手机号码不正确                                  |                    |
| 5175   | 新手机号处于 7 天注销状态，不能作为更换的手机号码 |                    |
| 5176   | 新手机号已注册                                    |                    |


**响应参数**:


| 参数名称 | 参数说明   | 类型           | schema         |
| -------- | ---------- | -------------- | -------------- |
| code     | 响应状态码 | integer(int32) | integer(int32) |
| data     | 响应数据   | object         |                |
| extMsg   | 扩展消息   | string         |                |
| msg      | 响应消息   | string         |                |


**响应示例**:
```javascript
{
	"code": 0,
	"data": {},
	"extMsg": "",
	"msg": ""
}
```

# Standard Features

Developer Center has predefined TSL models for certain product category solutions. When your selected solution supports standard features and uses TSL model data format, this page will be displayed. You can quickly add standard features to your current product by ticking the corresponding boxes to enable quick definition.

## Add a Standard Feature

After creating a product, you can go to the "**Product Details**" page, a pop window of standard feature property will appear. You can tick the standard TSL models you need.

<a data-fancybox title="img" href="/en/deviceDevelop/creatproduct/standerfunction01.png">![img](/en/deviceDevelop/creatproduct/standerfunction01.png)</a>

In "**Draft Mode**" on "**Define Feature**" page, you can select standard TSL models by clicking "**Add a Standard Feature**" under the "**Standard Features**" tab.

If the feature ID of a standard feature is occupied by a custom property, the addition may fail, which requires you to modify or delete the feature to release the feature ID in "**Custom Features**" tab.

## **Delete a Standard Feature**

In "**Draft Mode**", you can click "**Delete**" or "**Bulk Delete**" in the actions column to remove standard TSL models.

<a data-fancybox title="img" href="/en/deviceDevelop/creatproduct/standerfunction02.png">![img](/en/deviceDevelop/creatproduct/standerfunction02.png)</a>

After deletion, devices cannot communicate with application systems through TSL model. If the feature ID is used by devices, please proceed with caution.

## Release Application

After defining the product feature, click "**Save Changes**". Upon success, changes work for all devices under the product. These devices need to transmit data according to the latest TSL model definitions.

<a data-fancybox title="img" href="/en/deviceDevelop/creatproduct/standerfunction03.png">![img](/en/deviceDevelop/creatproduct/standerfunction03.png)</a>


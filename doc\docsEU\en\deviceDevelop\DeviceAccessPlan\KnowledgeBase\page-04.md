## Steps to Capture Logs with <PERSON><PERSON><PERSON>

This chapter introduces you to capture logs of Quectel modules by using Coolwatcher, and you can analyze the logs to find the problem.

### 1. Install the Driver
Double click to run __Setup.bat__ and wait for the completion of installation.

<a data-fancybox title="img" href="/en/deviceDevelop/KnowledgeBase/step16.png">![img](/en/deviceDevelop/KnowledgeBase/step16.png)</a>

Check whether there is a  __AP Log Port__ in the port column of the computer "Device Manager" after installation.

<a data-fancybox title="img" href="/en/deviceDevelop/KnowledgeBase/step17.png">![img](/en/deviceDevelop/KnowledgeBase/step17.png)</a>

### 2. Open Coolwatcher and Configure the Port
Double click to run __coolwatcher_usb.exe__ and select profiles and ports in the pop-up window. Then click "**OK**".

<a data-fancybox title="img" href="/en/deviceDevelop/KnowledgeBase/step18.png">![img](/en/deviceDevelop/KnowledgeBase/step18.png)</a>

Click "__Plugins__" on the tool bar and select "__Active Tracer__" in the drop-down window.

<a data-fancybox title="img" href="/en/deviceDevelop/KnowledgeBase/step19.png">![img](/en/deviceDevelop/KnowledgeBase/step19.png)</a>

<a data-fancybox title="img" href="/en/deviceDevelop/KnowledgeBase/step20.png">![img](/en/deviceDevelop/KnowledgeBase/step20.png)</a>

You can enter keywords to filter logs in the following box.

<a data-fancybox title="img" href="/en/deviceDevelop/KnowledgeBase/step21.png">![img](/en/deviceDevelop/KnowledgeBase/step21.png)</a>

### 3. Save the Log
Click "__Tracer__" on the tool bar and select "__Save Trace (bin/trc)__" in the drop-down window.  

<a data-fancybox title="img" href="/en/deviceDevelop/KnowledgeBase/step22.png">![img](/en/deviceDevelop/KnowledgeBase/step22.png)</a>

Select the path to save the file in the pop-up window.


<a data-fancybox title="img" href="/en/deviceDevelop/KnowledgeBase/step24.png">![img](/en/deviceDevelop/KnowledgeBase/step24.png)</a>
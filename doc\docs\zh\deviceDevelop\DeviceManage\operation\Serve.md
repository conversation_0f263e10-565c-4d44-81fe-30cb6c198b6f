# 服务调用

本文介绍设备运维中服务调用。服务调用是针对物模型产品中功能定义为服务类型的数据的一个数据展示。

## **前提条件**

● 已创建物模型产品，并在功能定义中定义了服务类型的功能<br />
● 已完成设备端开发，并发送服务调用数据

## **操作步骤**

1.登录开发者中心后，点击左侧菜单“设备管理”→ “设备运维”，出现设备列表页，点击设备操作栏中“查看”，跳转至“设备信息” tab页，点击“服务调用”tab页。

<a data-fancybox title="img" href="/zh/guide/image2022-3-10_11-45-7.png?version=1&modificationDate=1646883318000&api=v2">![img](/zh/guide/image2022-3-10_11-45-7.png?version=1&modificationDate=1646883318000&api=v2)</a>

参数说明

| 参数     | 描述                                         |
| :------- | :------------------------------------------- |
| 功能ID   | 物模型功能id,产品下功能唯一标识              |
| 服务名称 | 物模型中定义服务的功能名称                   |
| 标识符   | 物模型中定义服务的标识符，与功能名称一一对应 |
| 发送时间 | 服务调用的发送时间，即输入参数的创建时间     |
| 响应时间 | 服务调用的响应时间，即输出参数的更新时间     |
| 输入参数 | 服务调用时，输入参数值                       |
| 输出参数 | 设备响应的输出参数值                         |

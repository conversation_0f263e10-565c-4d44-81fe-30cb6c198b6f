# Presentation

## **1. Device Online**

### **1) Device Online**

Download the program into a module, and the module will automatically connect to Developer Center according to the code steps. After the module is connected to Developer Center, Developer Center will automatically add the device under the product and __automatically authenticate__ it.

<a data-fancybox title="img" href="/en/deviceDevelop/develop/speediness/resource/QuecOpen/Speediness-QuecOpen-08.png">![img](/en/deviceDevelop/develop/speediness/resource/QuecOpen/Speediness-QuecOpen-08.png)</a>

### **2) Enable Device Debug**

After the module is successfully connected to Developer Center, you can access the device console to debug the device.

On the "__Product Management__" page, click __"View"__ in the corresponding Action bar to receive data. Enter the "__Device Details__" page and click "__Device Debug__" to debug TSL data online.


<a data-fancybox title="img" href="/en/deviceDevelop/develop/speediness/resource/QuecOpen/Speediness-QuecOpen-09.png">
  <img src="/en/deviceDevelop/develop/speediness/resource/QuecOpen/Speediness-QuecOpen-09.png" width="700" alt="img">
</a>

## **2. Debug Device Online**

 <span style="color:#999AAA">Note: Online debugging can be used to debug device features, properties and services. To ensure successful debugging, it is important to verify that the device is online and properly connected.</span>

Draw up TSL features according to the actual project requirements of the smart aromatherapy diffuser. The default TSL model is presented in the table below.

|Feature ID| Feature Type |              Feature Name              | Data Format | Read/Write Type |                                  Unit                                  |
|:----------:| :----------: | :------------------------------------: | :---------: | :-------------: | :--------------------------------------------------------------------: |
|1|   Property   | The remaining content of essential oil |    FLOAT    |    Read-Only    |                                   ML                                   |
|2|   Property   |     Frequency of regular reporting     |     INT     |   Read-Write    |                                 Second                                 |
|3|   Property   |             On/Off status              |    BOOL     |    Read-Only    |                       True: On；<br>False: Off；                       |
|4|    Event     |     Alert to lack of essential oil     |    BOOL     |       --        |               True: Alert； <br> False: Alert canceled；               |
|5|   Property   |     mode     |     ENUM     |   Read-Write    |    1: Working Mode 1;<br> 2: Working Mode 2;<br>3: Working Mode 3;                          |
|6|   Service    |            Set working mode            |         |       --        | Feature ID: 5|

### __1) View Data Logs__

The communication logs indicate that the device reports the remaining content of essential oil to Developer Center every 30 seconds.

<a data-fancybox title="img" href="/en/deviceDevelop/develop/speediness/resource/QuecOpen/Speediness-QuecOpen-10.png">![img](/en/deviceDevelop/develop/speediness/resource/QuecOpen/Speediness-QuecOpen-10.png)</a>

### __2) Read the Current Property Value__

Click "**Read Data in Batches**" to read a batch of data, or click "**Read**" to read an individual property. Developer Center will then send the read command to the device. You can also read real-time communication logs to monitor the device's data transmissions and reports .


<a data-fancybox title="img" href="/en/deviceDevelop/develop/speediness/resource/QuecOpen/Speediness-QuecOpen-11.png">![img](/en/deviceDevelop/develop/speediness/resource/QuecOpen/Speediness-QuecOpen-11.png)</a>

### __3) Service Calls__

According to "__Device Debug__" > "__Service Calls__" defined by TSL features, you can simulate the condition of remotely issuing commands. On the "Device Debug" page, select "__Set Working Mode__" , set parameter to __Working Mode 2__ and click "__Send Command__", then you can see that the device has received the service issued by Developer Center.

<a data-fancybox title="img" href="/en/deviceDevelop/develop/speediness/resource/QuecOpen/Speediness-QuecOpen-12.png">
  <img src="/en/deviceDevelop/develop/speediness/resource/QuecOpen/Speediness-QuecOpen-12.png" width="500" alt="img">
</a>

### __4) Alert Event Logs__

 You can use the **Event Logs** feature to monitor alert events generated by the device. The feature allows you to view how the device automatically reports alert events to Developer Center in real time.

<a data-fancybox title="img" href="/en/deviceDevelop/develop/speediness/resource/QuecOpen/Speediness-QuecOpen-13.png">![img](/en/deviceDevelop/develop/speediness/resource/QuecOpen/Speediness-QuecOpen-13.png)</a>

# Wi-Fi 品类设备固件下载

## __1.下载烧写工具__

进入<a href="https://iot.quectelcn.com/download?menuCode=MODULE_DEVL" target="_blank">**烧录工具下载**</a>，点击进入指定模块的相关下载页面，找到烧写工具并下载。

## __2.PC连接设备__

PC通过 MicroUSB 线连接模组的 M_UART_USB 串口。

## __3.烧写工具使用__

我们打开 Beken Writer烧写软件 ，选择需要烧录的固件，操作步骤如下图所示。
<a data-fancybox title="img" href="/zh/deviceDevelop/KnowledgeBase/step31.png">![img](/zh/deviceDevelop/KnowledgeBase/step31.png)</a>

①、选择连接模组的 __串口__。<br>
②、选择需要烧录的 __固件__。<br>
③、点击 __烧录__ 按键及通过 __复位引脚__ 复位模组。<br>

注意：烧写过程中，要确保该端口不被占用
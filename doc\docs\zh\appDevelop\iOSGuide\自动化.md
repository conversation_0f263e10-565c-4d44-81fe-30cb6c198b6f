# 自动化SDK:QuecAutomateKit

## 功能概述

SDK支持用户配置一定的规则, 使设备在指定条件下执行指定的动作

```objc
#import <QuecAutomateKit/QuecAutomateKit.h>
/// 初始化
[QuecAutomateService sharedInstance]
```

## SDK集成方式

> **注意**
>
> 自动化SDK运行依赖核心库QuecIotSdk, 请先按照快速集成文档集成核心库

```objc
pod 'QuecAutomateKit', '~> 0.3.0'
```

## 自动化管理

### 获取自动化列表

**接口说明**

获取自动化列表, 通过分页请求

```objc
- (void)getAutomationListWithPageNumber:(NSInteger)pageNumber
                               pageSize:(NSInteger)pageSize
                                success:(QuecAutomationModelsSuccessBlock)success
                                failure:(QuecErrorBlock)failure;
```

**参数说明**

| 参数         | 是否必传 | 说明   |
|------------|------|------|
| pageNumber | 是    | 页码号  |
| pageSize   | 是    | 分页大小 |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	|

**示例代码**

```objc
[QuecAutomateService.sharedInstance getAutomationListWithPageNumber:1 pageSize:10 success:^(NSArray<QuecAutomateModel *> * _Nonnull models, NSUInteger total) {
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

**QuecAutomateModel属性定义**

| 字段            | 类型                                      | 描述                      |
|---------------|-----------------------------------------|-------------------------|
| automationId  | long long                                    | 自动化ID                   |
| fid          | String                                  | 家庭ID，开启家居模式必填，否则不填   
| conditionType | QuecAutomateConditionRuleType                                     | 触发类型。<br />QuecAutomateConditionRuleAny = 1 //1: 满足任意<br />QuecAutomateConditionRuleAll //2: 满足所有   |
| name          | String                                  | 自动化名称                   |
| nameType      | int                                     | 自动化名称生成方式。1：系统生成，2：用户填写 |
| status        | boolean                                 | 启停状态。true：启用，false：停用   |
| precondition   | QuecAutomationPreconditionModel | 自动化生效时间                    |
| conditions   | NSArray<QuecAutomationConditionModel *> * | 自动化触发条件                   |
| actions    | NSArray<QuecAutomationActionModel *> *    | 自动化执行动作                    |

**QuecAutomationPreconditionModel属性定义**

| 字段   | 类型     | 描述               |
|------|--------|------------------|
| effectDateType | QuecAutomateEffectDateType | 生效日期类型。<br />QuecAutomateEffectDateTypeEveryday = 0, /// 每天<br />QuecAutomateEffectDateTypeWeekly,       /// 每周的<br />QuecAutomateEffectDateTypeMonthly,      /// 每月<br />QuecAutomateEffectDateTypeNamedDate     /// 指定日期         |
| effectTimeType | QuecAutomateEffectTimeType | 生效时间类型。<br />QuecAutomateEffectTimeTypeDaytime = 0, /// 白天<br />QuecAutomateEffectTimeTypeNight,       /// 夜晚<br />QuecAutomateEffectTimeTypeWholeDay,      /// 全天<br />QuecAutomateEffectTimeTypeSpecificTimePeriod     /// 特定时间段             |
| startTime | NSString    | 开始时间。生效时间类型为全天和指定时间段时必填，符合HH:mm格式 |
| endTime | NSString    | 结束时间。生效时间类型为全天和指定时间段时必填，符合HH:mm格式     | 
| effectDate | NSString    | 生效日期。生效日期类型为每天时不填。每周：1,2,3,...7表示周一到周日，周月：1-31号，指定日期：符合MM-dd/MM-dd格式，例：12-19/12-2     | 
| regionName | NSString    | 地区名称     | 
| location | NSString    | 经纬度    | 
| timeZone | NSString    | 时区     | 

**QuecAutomationConditionModel属性定义**

| 字段         | 类型                      | 描述                   |
|------------|-------------------------|----------------------|
| type       | int                     | 触发类型。1: 满足任意，2: 满足所有 |
| icon       | String                  | 图标                   |
| name       | String                  | 条件名称                 |
| timer      | QuecAutomationTimeModel | 自动化触发条件时间                 |
| productKey | String                  | 设备的productKey        |
| deviceKey  | String                  | 设备的deviceKey         |
| property   | QuecAutoProductTSLModel | 设备属性                 |
| sort       | int                     | 条件排序从1开始依次递增         |

**QuecAutomationTimeModel属性定义**

| 字段        | 类型     | 描述                             |
|-----------|--------|--------------------------------|
| type      | int    | 定时类型 0:仅一次 ,1:每天 ,2:自定义        |
| time      | String | 定时时间 23:00 24小时制               |
| dayOfWeek | String | 定时自定义时间段, 格式为 "1,3,4"，以 "," 分隔 |
| timeZone  | String | 时区id 例如: GMT+08:00             |

**QuecAutomationPropertyModel属性定义**

| 字段       | 类型     | 描述                                                                 |
|----------|--------|--------------------------------------------------------------------|
| identifier       | int    | 物模型id                                                              |
| code     | String | 物模型id，由于 id 是 Objective-C 的关键字，所以使用 identifier       |
| name     | String | 物模型名称                                                              |
| compare  | String | 作为条件的时候有比较符号, 属性值比较运算符：> >= < <= == != REPORT COUNT，其中REPORT为上报的数据 |
| unit     | String | 单位                                                                 |
| dataType | String | 数据类型                                                               |
| value    | String | 属性值                                                                |
| subName  | String | 条件名称                                                               |

**QuecAutomationActionModel属性定义**

| 字段         | 类型                      | 描述                                                     |
|------------|-------------------------|--------------------------------------------------------|
| type       | int                     | 执行的动作类型 1-延迟执行后面一个动作 2-修改设备属性 3-修改群组属性 3-修改群组属性 4-执行场景 |
| icon       | String                  | 图标                                                     |
| name       | String                  | 名称                                                     |
| productKey | String                  | 设备的productKey                                          |
| deviceKey  | String                  | 设备的deviceKey                                           |
| sceneId    | String                  | 场景id                                                   |
| delayTime  | NSNumber                     | 延迟时间 单位秒                                               |
| sort       | int                     | 动作排序从1开始递增                                             |
| property   | QuecAutomationPropertyModel | 设备属性                                                   |           

### 创建自动化

**接口说明**

创建一条自动化规则可以拆分为4个部分

1. 基础信息
    1. 名称
2. 生效时间
    1. 时间段
        1. 特定时间段(0:00 ~ 23:59:59)
        2. 全天
        3. 白天/夜晚
    2. 日期
        1. 每一天
        2. 每周的第几天
        3. 每月的第几天
        4. 指定的开始日期和结束日期
3. 触发条件
    1. 条件选择
        1. 满足任意条件
        2. 满足所有条件
    2. 条件类型 (可有多个)
        1. 指定设备的联动条件
        2. 定时
            1. 一天中的时间点
            2. 一周中哪几天
4. 执行动作 (可有多个)
    1. 执行设备联动配置
    2. 执行指定的场景
    3. 添加延时

需将4个部分的信息都填入后, 方可创建一条可用的规则

```objc
- (void)addAutomationWithModel:(QuecAutomateModel *)model
                       success:(QuecVoidBlock)success
                       failure:(QuecErrorBlock)failure;
```

**参数说明**

| 参数              | 是否必传 | 说明    |
|-----------------|------|-------|
| model | 是    | 自动化信息 |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	|

**示例代码**

```objc
QuecAutomationPreconditionModel *precondition = [[QuecAutomationPreconditionModel alloc] init];
precondition.effectDate = @"";
precondition.effectDateType = QuecAutomateEffectDateTypeEveryday;
precondition.effectTimeType = QuecAutomateEffectTimeTypeWholeDay;
precondition.location = @"";
precondition.timeZone = @"GMT+08:00";

QuecAutomationTimeModel *time = QuecAutomationTimeModel.new;
time.type = 0;
time.time = @"14:10";
time.dayOfWeek = @"";
time.timeZone = @"GMT+8:00";
QuecAutomationConditionModel *condition = [[QuecAutomationConditionModel alloc] initWithType:1
                                                                                        icon:@""
                                                                                        name:@""
                                                                                        timer:time
                                                                                    productKey:@""
                                                                                    deviceKey:@""
                                                                                    property:nil
                                                                                        sort:1];
QuecAutomationActionModel *action = [[QuecAutomationActionModel alloc] initWithType:4
                                                                                icon:@"https://xxxxx.png"
                                                                                name:@"sence name"
                                                                            productKey:@""
                                                                            deviceKey:@""
                                                                            sceneId:@"Scene Id"
                                                                            delayTime:@(0)
                                                                            property:nil
                                                                                sort:1];

QuecAutomateModel *automateModel = [[QuecAutomateModel alloc] initWithFid:@"your fid"
                                                            conditionType:QuecAutomateConditionRuleAny
                                                                        name:@"自动化名称"
                                                                    nameType:1
                                                            preconditions:precondition
                                                                conditions:@[condition]
                                                                    actions:@[action]];

[QuecAutomateService.sharedInstance addAutomationWithModel:automateModel
                                                    success:^{
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 编辑自动化

**接口说明**

编辑已有的自动化规则

```objc
- (void)editAutomationWithModel:(QuecAutomateModel *)model
                        success:(QuecVoidBlock)success
                        failure:(QuecErrorBlock)failure;
```

**参数说明**

| 参数              | 是否必传 | 说明    |
|-----------------|------|-------|
| model | 是    | 自动化信息 |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	|

**示例代码**

```objc
QuecAutomateModel *automate = QuecAutomateModel.new; /// 云端返回的自动化模型
automate.name = @"new name";
[QuecAutomateService.sharedInstance editAutomationWithModel:automate
                                                    success:^{
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 删除自动化

删除指定的自动化规则

**接口说明**

```objc
- (void)deleteAutomationWithAutomationId:(long long)automationId
                                 success:(QuecVoidBlock)success
                                 failure:(QuecErrorBlock)failure;
```

**参数说明**

| 参数           | 是否必传 | 说明    |
|--------------|------|-------|
| automationId | 是    | 自动化id |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	|

**示例代码**

```objc
[QuecAutomateService.sharedInstance deleteAutomationWithAutomationId:10010 success:^{
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 查询设备可作为自动化条件和触发动作的物模型

**接口说明**

```objc
- (void)getAutomationTSLWithProductKey:(NSString *)productKey
                                  type:(NSInteger)type
                               success:(QuecAutomationPropertySuccessBlock)success
                               failure:(QuecErrorBlock)failure;
)
```

**参数说明**

| 参数         | 是否必传 | 说明                  |
|------------|------|---------------------|
| productKey | 是    | 设备的productKey       |
| type       | 是    | 查询类型：0：全部，1：条件，2：动作 |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	|

**示例代码**

```objc
[QuecAutomateService.sharedInstance getAutomationTSLWithProductKey:@"pk" type:0 success:^(NSArray<QuecProductTSLPropertyModel *> * _Nonnull conditions, NSArray<QuecProductTSLPropertyModel *> * _Nonnull actions) {
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

> QuecProductTSLPropertyModel属性定义同设备SDK

### 获取自动化详情

**接口说明**

获取自动化详情

```objc
- (void)getAutomationInfoWithAutomationId:(long long)automationId
                                  success:(void(^)(QuecAutomateModel *model))success
                                  failure:(QuecErrorBlock)failure;
```

**参数说明**

| 参数           | 是否必传 | 说明    |
|--------------|------|-------|
| automationId | 是    | 自动化id |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	|

**示例代码**

```objc
[QuecAutomateService.sharedInstance getAutomationInfoWithAutomationId:10009 success:^(QuecAutomateModel * _Nonnull model) {
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 开启关闭自动化

**接口说明**

可使自动化规则启动或者暂停

```objc
- (void)updateAutomationSwitchStatusWithAutomationId:(long long)automationId
                                              status:(BOOL)status
                                             success:(QuecVoidBlock)success
                                             failure:(QuecErrorBlock)failure;
```

**参数说明**

| 参数           | 是否必传 | 说明    |
|--------------|------|-------|
| automationId | 是    | 自动化id |
| status | 是    | 启停状态，启用：true，停用：false |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	|

**示例代码**

```objc
[QuecAutomateService.sharedInstance updateAutomationSwitchStatusWithAutomationId:10011 status:YES success:^{
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

## 自动化测试

### 测试自动化

**接口说明**

可测试配置的自动化的动作是否可以执行

```objc
- (void)testAutomationInfoWithActions:(NSArray<QuecAutomationActionModel *> *)actions
                                timeout:(long)timeout
                              success:(void(^)(long long testResultId))success
                              failure:(QuecErrorBlock)failure;
```

**参数说明**

| 参数       | 是否必传 | 说明              |
|----------|------|-----------------|
| actions  | 是    | 自动化任务           |
| timeout  | 是    | 超时时间, 单位秒       |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	|

**示例代码**

```objc
QuecAutomationActionModel *action = [[QuecAutomationActionModel alloc] initWithType:4
                                                                                   icon:@"https://xxxxx.png"
                                                                                   name:@"sence name"
                                                                             productKey:@""
                                                                              deviceKey:@""
                                                                                sceneId:@"Scene Id"
                                                                              delayTime:@(0)
                                                                               property:nil
                                                                                   sort:1];
    [QuecAutomateService.sharedInstance testAutomationInfoWithActions:@[action]
                                                              timeout:30
                                                              success:^(long long testResultId) {
        /// Next Action
    } failure:^(NSError *error) {
        NSLog(@"check error: %@", error);
    }];
```

### 获取自动化测试的结果

**接口说明**

在执行了自动化测试后, 可以通过结果id来查询自动化执行的结果

```objc
- (void)getTestAutomationResultWithId:(long long)testResultId
                              success:(QuecAutomationResultSuccessBlock)success
                              failure:(QuecErrorBlock)failure;
```

**参数说明**

| 参数           | 是否必传 | 说明   |
|--------------|------|------|
| testResultId | 是    | 结果id |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	|

**示例代码**

```objc
[QuecAutomateService.sharedInstance getTestAutomationResultWithId:10002 success:^(QuecAutomationResultModel * _Nullable resultModel) {
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

**QuecAutomationResultModel属性定义**

| 字段             | 类型                           | 描述                           |
|----------------|------------------------------|------------------------------|
| result         | NSInteger                          | 自动化执行结果 0-全部成功 1-部分成功 2-全部失败 |
| successCount   | NSInteger                          | 成功数量                         |
| failCount      | NSInteger                          | 失败数量                         |
| overallFailMsg | NSString                       | 错误信息                         |
| timeoutStatus  | BOOL                      | 超时状态                         |
| failActionList | NSArray<QuecAutomationResultItemModel *> * | 失败的动作列表                      |

**QuecAutomationResultItemModel属性定义**

| 字段       | 类型                      | 描述       |
|----------|-------------------------|----------|
| actionName     | NSString                  | 动作名称     |
| icon     | NSString                  | 动作图标     |
| failMsg  | NSString                  | 失败原因     |

## 自动化日志

### 获取自动化日志列表

**接口说明**

自动化执行之后, 可以查询其执行的记录

```objc
- (void)getAutomationLogListWithLastLogId:(long long)lastLogId
                                    limit:(NSInteger)limit
                                  success:(void(^)(NSArray<QuecAutomationLogItemModel *> *list))success
                                  failure:(QuecErrorBlock)failure;
```

**参数说明**

| 参数        | 是否必传 | 说明       |
|-----------|------|----------|
| lastLogId | 是    | 最后一条日志id |
| limit     | 是    | 获取几条     |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	|

**示例代码**

```objc
[QuecAutomateService.sharedInstance getAutomationLogListWithLastLogId:99
                                                                limit:10
                                                                success:^(NSArray<QuecAutomationLogItemModel *> * _Nonnull list) {
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

**QuecAutomationLogItemModel属性定义**

| 字段           | 类型                                            | 描述                                           |
|--------------|-----------------------------------------------|----------------------------------------------|
| automationId | long                                          | 自动化id                                        |
| logId        | long                                          | 日志id                                         |
| name         | String                                        | 自动化任务名称                                      |
| icon         | String                                        | 自动化图标                                        |
| result       | int                                           | 执行结果 0.成功：所有都执行成功即视为成 1.失败：所有都失败即视为失败 2.部分成功 |
| time         | long                                          | 执行时间(时间戳)                                    |                                             
| logDetails   | NSArray<QuecAutomationLogItemActionModel *> * | 日志详情                                         |

**QuecAutomationLogItemActionModel属性定义**

| 字段          | 类型                      | 描述                  |
|-------------|-------------------------|---------------------|
| actionName  | String                  | 动作名称                |
| type        | int                     | 类型  2设备 3群组 4场景     |
| result      | boolean                 | 执行结果：true成功 false失败 |
| executeTime | long                    | 执行时间                |
| action      | QuecAutomationPropertyModel | 物模型属性                |
| actionIcon      | NSString | 执行动作图标，设备/群组/群组                |

### 获取日志详情

**接口说明**

获取自动化日志详情

```objc
- (void)getAutomationLogDetailWithLogId:(long long)logId
                                success:(void(^)(QuecAutomationLogItemModel *detailInfo))success
                                failure:(QuecErrorBlock)failure;
```

**参数说明**

| 参数       | 是否必传 | 说明   |
|----------|------|------|
| logId    | 是    | 日志id |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	|

**示例代码**

```objc
[QuecAutomateService.sharedInstance getAutomationLogDetailWithLogId:100
                                                                success:^(QuecAutomationLogItemModel * _Nonnull detailInfo) {
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 清除自动化日志

**接口说明**

清除自动化日志, 会删掉所有的日志

```objc
- (void)clearAutomationLogsWithSuccess:(QuecVoidBlock)success failure:(QuecErrorBlock)failure;
```

**参数说明**

| 参数       | 是否必传 | 说明   |
|----------|------|------|
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	|

**示例代码**

```objc
[QuecAutomateService.sharedInstance clearAutomationLogsWithSuccess:^{
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

# 创建房间


**接口地址**:`/v2/family/enduserapi/addFamilyRoom`


**请求方式**:`POST`


**请求数据类型**:`application/json`


**响应数据类型**:`*/*`


**接口描述**:<p>创建房间</p>


**请求参数**:


| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型 | schema |
| -------- | -------- | -------- | -------- | -------- | ------ |
| fid      | 家庭ID   | query    | true     | string   |        |
| roomName | 房间名称 | query    | true     | string   |        |


**响应状态**:


| 状态码 | 说明                                     | schema                        |
| ------ | ---------------------------------------- | ----------------------------- |
| 200    | 创建房间成功                             | 返回注册码响应数据«AddRoomVO» |
| 5041   | 没有权限                                 |                               |
| 5618   | 请输入家庭ID                             |                               |
| 5620   | 创建房间失败                             |                               |
| 5635   | 未开启家居模式                           |                               |
| 5640   | 请输入房间名称                           |                               |
| 5641   | 房间名称超长，请输入30个字以内的房间名称 |                               |
| 5642   | 房间名称已存在，请重新输入               |                               |
| 6049   | 权限不足,当前用户是普通成员              |                               |


**响应参数**:


| 参数名称             | 参数说明   | 类型           | schema         |
| -------------------- | ---------- | -------------- | -------------- |
| code                 | 响应状态码 | integer(int32) | integer(int32) |
| data                 | 响应数据   | AddRoomVO      | AddRoomVO      |
| &emsp;&emsp;frid     | 房间ID     | string         |                |
| &emsp;&emsp;roomName | 房间名称   | string         |                |
| extMsg               | 扩展消息   | string         |                |
| msg                  | 响应消息   | string         |                |


**响应示例**:
```javascript
{
	"code": 0,
	"data": {
		"frid": "",
		"roomName": ""
	},
	"extMsg": "",
	"msg": ""
}
```

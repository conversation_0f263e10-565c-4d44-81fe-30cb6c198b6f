# 第三方平台

本文介绍了如何使用第三方方式进行登录的功能

## 功能概述

```objc
#import <QuecUserKit/QuecUserKit.h>
[QuecUserService sharedInstance]
```

### 一键登录

**接口说明**

一键登录入参QuecOneKeyLoginModel数据模型, 所有参数都必填

```objc
- (void)oneKeyLoginWithModel:(QuecOneKeyLoginModel *)model
                   success:(QuecVoidBlock)success
                   failure:(QuecErrorBlock)failure;
```

**QuecOneKeyLoginModel属性定义**

|参数|	类型|说明|
| --- | --- | --- |
| appid | NSString |中国移动开放平台申请appid|
| msgid | NSString   |uuid|
| strictcheck | NSString   |0：不对服务器ip白名单进行强校验,1：对服务器ip白名单进行强校验|
| systemtime | NSString   |系统时间 yyyyMMddHHmmssSSS|
| appSecret | NSString       |中国移动开放平台申请appSecret|
| loginToken | NSString      |获取权限后移动SDK返回token|
| version | NSString   |版本|


**示例代码**

```objc
QuecOneKeyLoginModel *loginModel = QuecOneKeyLoginModel.new;
loginModel.appid = appid;
loginModel.msgid = uuid;
loginModel.strictcheck = @"0";;
loginModel.systemtime = systemtime;
loginModel.appSecret = appSecret;
loginModel.loginToken = loginToken;
loginModel.version = @"2.0";
[QuecUserService.sharedInstance oneKeyLoginWithModel:loginModel success:^{
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 第三方用户登录

**接口说明**

用于第三方用户登录

```objc
- (void)loginByAuthCode:(NSString *)authCode success:(QuecVoidBlock)success failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|
| --- | --- | --- |
| authCode | 是       | 授权code         |
| success  | 否       | 接口请求成功回调 |
| failure  | 否       | 接口请求失败回调 |

**示例代码**

```objc
[QuecUserService.sharedInstance loginByAuthCode:@"AuthCode" success:^{
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

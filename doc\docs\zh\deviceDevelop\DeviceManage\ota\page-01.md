# OTA概述

OTA全称为Over-the-Air Technology,中文即为空中下载技术。OTA在物联网中的主要应用是为设备更新模组固件或外设MCU固件，使固件升级变得无需物理访问，节省了已出售产品召回、固件烧录所需的经费和时间。

本文主要介绍OTA升级的能力以及操作流程。

前提条件

使用OTA升级功能前，请确保设备端已完成OTA升级能力的开发。

## 平台OTA能力说明

| **功能**   | **描述**                                                     |
| ---------- | ------------------------------------------------------------ |
| 支持协议   | 支持MQTT协议、LwM2M协议、Http协议的设备OTA  **注：** 暂不支持电信设备的OTA升级。 |
| 固件升级包 | 仅支持.bin,.rar,.pack,.mini_1,.mini_2,.py,.tar,.s19,.bin_1,.bin_2,.zip,.tar.gz格式的文件，单文件大小不能超过100MB，最多支持5个文件。  **注：** 如果当前支持的文件类型不能满足您的业务需求，可[**提交工单**](/personalCenter/WorkOrder)申请增加。 |
| 升级设备   | 支持产品级设备批次升级，同时可通过添加白名单、黑名单方式排除特殊情况。最大同时支持10000设备同时升级。 |
| 升级计划   | 整包升级：直接指定升级后的目标版本号；固件包为完整的固件包。  l   差分升级：需要指定设备当前版本（源版本）以及升级后的目标版本；固件包为差分包，需要上传对应源版本和目标版本的差分包。 |
| 升级方式   | 静默升级:由平台直接推送升级计划到设备进行OTA升级，无需用户进行确认。  用户确认升级:上报用户确认升级后，平台才会返回升级计划给到设备进行OTA升级。 |
| 支持地域   | 全球                                                         |

 


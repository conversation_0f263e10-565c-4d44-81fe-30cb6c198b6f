# Presentation

## **1. Device Online**
### **1) Device Online**

Download the program code to the module and the module will automatically connect to Developer Center according to the program. When the module is connected to Developer Center, the device will be automatically added to the product and __automatically authenticated__.

<a data-fancybox title="img" href="/en/deviceDevelop/develop/speediness/resource/QuecOpen/Speediness-QuecOpen-08.png">![img](/en/deviceDevelop/develop/speediness/resource/QuecOpen/Speediness-QuecOpen-08.png)</a>

### **2) Device Debug**
After the module is connected to Developer Center, you can debug the device in the "Console" page.

Enter the "**Device Management**" > "**Device Maintenance**" page, click "__View__" in the "Action" bar of the corresponding device to enter the "**Device Details**" page, and then click the "**Device Debugging**" tab to debug TSL data online.

<a data-fancybox title="img" href="/en/deviceDevelop/develop/speediness/resource/QuecOpen/Speediness-QuecOpen-09.png">
  <img src="/en/deviceDevelop/develop/speediness/resource/QuecOpen/Speediness-QuecOpen-09.png" width="700" alt="img">
</a>

## **2. Debug Device Online**
 <span style="color:#999AAA">Note: "Device Debugging" is used to debug device features, properties and services. Please ensure that the device is online normally during debugging.</span>

Draw up TSL features according to the actual project requirements of the smart aromatherapy diffuser. The default TSL model is presented in the table below.

|Feature ID| Feature Type |              Feature Name              | Data Format | Read/Write Type |                                  Unit                                  |
|:----------:| :----------: | :------------------------------------: | :---------: | :-------------: | :--------------------------------------------------------------------: |
|1|   Property   | The remaining content of essential oil |    FLOAT    |    Read-Only    |                                   ML                                   |
|2|   Property   |     Frequency of regular reporting     |     INT     |   Read-Write    |                                 Second                                 |
|3|   Property   |             On/Off status              |    BOOL     |    Read-Only    |                       True: On；<br>False: Off；                       |
|4|    Event     |     Alert to lack of essential oil     |    BOOL     |       --        |               True: Alert； <br> False: Alert canceled；               |
|5|   Property   |     mode     |     ENUM     |   Read-Write    |    1: Working Mode 1;<br> 2: Working Mode 2;<br>3: Working Mode 3;                          |
|6|   Service    |            Set working mode            |         |       --        | Feature ID: 5|

### __1) View Data Logs__

The communication logs indicate that the device reports the remaining content of essential oil to Developer Center every 30 seconds.

<a data-fancybox title="img" href="/en/deviceDevelop/develop/speediness/resource/QuecOpen/Speediness-QuecOpen-10.png">![img](/en/deviceDevelop/develop/speediness/resource/QuecOpen/Speediness-QuecOpen-10.png)</a>

### __2) Read the Current Property Value__

Click "**Read in Batches**" or "**Read**" of a single property, and then Developer Center will send the querying command to the device. You can also read Communication Logs in real time to check the situation of data sent and reported by the device.

<a data-fancybox title="img" href="/en/deviceDevelop/develop/speediness/resource/QuecOpen/Speediness-QuecOpen-11.png">![img](/en/deviceDevelop/develop/speediness/resource/QuecOpen/Speediness-QuecOpen-11.png)</a>

### __3) Call Service__

According to __Device Debug__ > __Service Calls__ defined by TSL features, you can simulate the condition of remotely issuing commands. On the "Device Debug" page, select "__Set Working Mode__", set the parameter to __Working Mode 2__ and click __"Send Command"__, then you can see that the device has received the service issued by Developer Center.

<a data-fancybox title="img" href="/en/deviceDevelop/develop/speediness/resource/QuecOpen/Speediness-QuecOpen-12.png">
  <img src="/en/deviceDevelop/develop/speediness/resource/QuecOpen/Speediness-QuecOpen-12.png" width="500" alt="img">
</a>

### __4) Alert Event Logs__

You can use the **Event Logs** feature to monitor alert events generated by the device.

<a data-fancybox title="img" href="/en/deviceDevelop/develop/speediness/resource/QuecOpen/Speediness-QuecOpen-13.png">![img](/en/deviceDevelop/develop/speediness/resource/QuecOpen/Speediness-QuecOpen-13.png)</a>


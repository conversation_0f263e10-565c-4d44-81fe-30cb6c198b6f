---
title: "设备升级"
description: "设备升级相关文档"
---

# 设备升级

## 功能概述

通过此SDK, 可以实现设备升级功能(通过云升级,或者通过蓝牙升级)

```kotlin
QuecOtaManager
```

## 设备升级

设备升级的流程为:

1. 查询设备升级计划
2. 设置事件监听器
3. 开始设备升级
4. 通过事件回调获取升级进度和升级结果
5. 展示升级结果, 移除事件监听器

SDK会根据设备的在线状态来确定是通过云OTA还是蓝牙OTA:

1. 设备云在线时, 通过云OTA
2. 设备云不在线时, 通过蓝牙OTA

### 查询单个设备的升级计划

**接口说明**

查询单个设备的升级计划, 当设备有升级计划时, 返回升级计划信息, 否则返回null

```kotlin
fun checkVersion(pk: String, dk: String, callback: QuecCallback<QuecOtaInfo?>)
```

**参数说明**

| 参数       | 是否必传 | 说明           |
|----------|------|--------------|
| pk       | 是    | 设备productKey |
| dk       | 是    | 设备deviceKey  |
| callback | 是    | 请求回调         |

**示例代码**

```kotlin
QuecOtaManager.checkVersion("pk", "dk") {
    if (it.isSuccess) {
        //请求成功, 当data值不为空时, 表示有升级计划
        val data = it.data
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg   //请求失败, 错误信息
    }
}
```

**QuecOtaInfo属性定义**

| 字段                 | 类型              | 描述           |
|--------------------|-----------------|--------------|
| productKey         | String          | 产品productKey |
| deviceKey          | String          | 设备deviceKey  |
| upgradeVersionInfo | String          | 升级版本信息       |
| planId             | String          | 升级计划ID       |
| planName           | String          | 升级计划名称       |
| comVerList         | List&lt;ComVer> | 组件版本信息       |
| productIcon        | String          | 产品图标         |
| deviceName         | String          | 设备名称         |

**QuecOtaInfo.ComVer属性定义**

| 字段          | 类型            | 描述                  |
|-------------|---------------|---------------------|
| sort        | Int           | 组件排序                |
| comNo       | String        | 组件编号                |
| comType     | int           | 组件类型, 0: 模组 ,1: MCU |
| cver        | String        | 当前版本号               |
| tver        | String        | 目标版本号               |
| versionInfo | String        | 组件版本信息              |
| fileList    | List&lt;File> | 升级文件列表              |

**QuecOtaInfo.File属性定义**

| 字段         | 类型     | 描述          |
|------------|--------|-------------|
| updIndex   | Int    | 升级文件序号      |
| fileName   | String | 升级文件名       |
| filePath   | String | 升级文件路径      |
| fileSize   | Long   | 升级文件大小      |
| fileSha256 | String | 升级文件sha256值 |

### 批量查询设备的升级计划

**接口说明**

```kotlin
 fun checkListVersion(list: List<Info>, callback: QuecCallback<List<QuecOtaInfo>>)
```

**参数说明**

| 参数       | 是否必传 | 说明   |
|----------|------|------|
| list     | 是    | 设备列表 |
| callback | 是    | 请求回调 |

**示例代码**

```kotlin
QuecOtaManager.checkListVersion(
    listOf(
        IQuecOtaManager.Info("pk1", "dk1"),
        IQuecOtaManager.Info("pk2", "dk2"),
    )
) {
    if (it.isSuccess) {
        //请求成功, 当data中的size>0时, 表示有升级计划
        val data = it.data
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg   //请求失败, 错误信息
    }
}
```

**IQuecOtaManager.Info属性定义**

| 字段 | 类型     | 描述           |
|----|--------|--------------|
| pk | String | 设备productKey |
| dk | String | 设备deviceKey  |

### 查询账号下所有设备的升级计划

**接口说明**

```kotlin
fun checkAllVersion(
    page: Int,
    pageSize: Int,
    callback: QuecCallback<QuecPageResponse<QuecOtaInfo>>
)
```

**参数说明**

| 参数       | 是否必传 | 说明     |
|----------|------|--------|
| page     | 是    | 当前页码   |
| pageSize | 是    | 每页显示数量 |
| callback | 是    | 请求回调   |

**示例代码**

```kotlin
QuecOtaManager.checkAllVersion(1, 10) {
    if (it.isSuccess) {
        //请求成功
        val data = it.data
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg   //请求失败, 错误信息
    }
}
```

### 获取设备版本信息

**接口说明**

```kotlin
fun queryCurrentVersion(
    pk: String,
    dk: String,
    callback: QuecCallback<List<QuecOtaInfo.ComVer>>
)
```

**参数说明**

| 参数       | 是否必传 | 说明           |
|----------|------|--------------|
| pk       | 是    | 设备productKey |
| dk       | 是    | 设备deviceKey  |
| callback | 是    | 请求回调         |

**示例代码**

```kotlin
QuecOtaManager.queryCurrentVersion("pk", "dk") {
    if (it.isSuccess) {
        //请求成功, 当data值不为空时, 表示有版本信息
        val data = it.data
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg   //请求失败, 错误信息
    }
}
```

### 开始设备升级

**接口说明**

指定设备开始ota升级, 在调用此方法时, 会自动确认升级方式

```kotlin
fun startOta(info: QuecOtaInfo)
```

**参数说明**

| 参数   | 是否必传 | 说明   |
|------|------|------|
| info | 是    | 升级信息 |

**示例代码**

```kotlin
//查询升级计划获取到的信息
val otaInfo = getOtaInfo()
QuecOtaManager.startOta(otaInfo)
```

### 停止设备升级

**接口说明**

> 注意
>
> 只有蓝牙通道ota升级时, 可以停止, 如果是云ota时, 升级流程无法停止, 设备会继续升级, 直到结束

指定设备停止ota升级

```kotlin
fun stopOta(info: QuecOtaInfo)
```

**参数说明**

| 参数   | 是否必传 | 说明   |
|------|------|------|
| info | 是    | 升级信息 |

**示例代码**

```kotlin
//查询升级计划获取到的信息
val otaInfo = getOtaInfo()
QuecOtaManager.stopOta(otaInfo)
```

### 添加升级状态监听器

**接口说明**

添加升级状态监听器, 通过该监听器回调, 能获取到设备的升级状态和进度

```kotlin
interface QuecOnOtaStateChangeListener {
    fun onCall(state: QuecOTAStateModel)
}

fun addStateListener(listener: QuecOnOtaStateChangeListener)
```

**参数说明**

| 参数       | 是否必传 | 说明  |
|----------|------|-----|
| listener | 是    | 监听器 |

**示例代码**

```kotlin
val listener = object : QuecOnOtaStateChangeListener {
    override fun onCall(state: QuecOTAStateModel) {
        //设备状态变更
    }
}
QuecOtaManager.addStateListener(listener)
```

**QuecOTAStateModel属性定义**

| 字段          | 类型     | 描述                                         |
|-------------|--------|--------------------------------------------|
| planId      | long   | 升级计划ID                                     |
| state       | enum   | 升级状态: QuecOTAState                         |
| pk          | String | 设备productKey                               |
| dk          | String | 设备deviceKey                                |
| progress    | double | 升级进度 (0.0 ~ 100.0)                         |
| failCode    | enum   | 失败原因: IQuecOtaManager.ErrorCode, 当升级失败时才有值 |
| channelMode | enum   | 升级通道类型: QuecOTAChannelMode                 |

**QuecOTAStateModel枚举定义**

| 字段                    | 描述     |
|-----------------------|--------|
| QuecOTAUpgradeEmpty   | 无升级计划  |
| QuecOTAUpgradeWait    | 等待升级   |
| QuecOTAUpgrading      | 升级中    |
| QuecOTAUpgradeSuccess | 升级成功   |
| QuecOTAUpgradeFailure | 升级失败   |
| QuecOTAUpgradeExpired | 升级计划过期 |

**IQuecOtaManager.ErrorCode枚举定义**

| 字段                 | 描述        |
|--------------------|-----------|
| COMMON             | 通用错误      |
| FILE_DOWNLOAD_FAIL | ota文件下载失败 |
| NOT_CONNECT        | 设备连接失败    |
| NO_FILE_PATH       | 升级计划失效    |
| FILE_CHECK_FAIL    | ota文件校验失败 |
| DEVICE_REFUSE      | 设备拒绝升级    |
| DEVICE_CANCELLED   | 设备取消升级    |
| DEVICE_FAIL        | 设备响应升级失败  |
| TIMEOUT            | 升级超时      |

**QuecOTAChannelMode枚举定义**

| 字段                    | 描述   |
|-----------------------|------|
| QuecOTAChannelUnknown | 未知   |
| QuecOTAChannelCloud   | 云升级  |
| QuecOTAChannelBle     | 蓝牙升级 |

### 移除升级状态监听器

**接口说明**

移除升级状态监听器, 请在完成ota升级后及时移除监听器, 避免内存泄漏

```kotlin
fun removeStateListener(listener: QuecOnOtaStateChangeListener)
```

**参数说明**

| 参数       | 是否必传 | 说明  |
|----------|------|-----|
| listener | 是    | 监听器 |

**示例代码**

```kotlin
val listener = object : QuecOnOtaStateChangeListener {
    override fun onCall(state: QuecOTAStateModel) {
        //设备状态变更
    }
}
QuecOtaManager.removeStateListener(listener)
```

### 移除类中所有的缓存

**接口说明**

移除类中所有的缓存, 在不再使用ota资源时调用

```kotlin
fun release()
```

**示例代码**

```kotlin
QuecOtaManager.release()
```

### 获取指定设备的OTA升级进度和状态

**接口说明**

获取指定设备的OTA升级进度和状态, 为空时表示此设备无状态

```kotlin
fun getOtaState(pk: String, dk: String): QuecOTAStateModel?
```

**参数说明**

| 参数 | 是否必传 | 说明           |
|----|------|--------------|
| pk | 是    | 设备productKey |
| dk | 是    | 设备deviceKey  |

返回值为升级状态和进度对象

**示例代码**

```kotlin
val state = QuecOtaManager.getOtaState("pk", "dk")
```

### 获取设备的升级方式

**接口说明**

在开始设备升级之前调用, 可获取当前开始升级时, 将选择的升级方式

```kotlin
fun getDeviceUpgradeChannel(pk: String, dk: String): QuecOTAStateModel.QuecOTAChannelMode
```

**参数说明**

| 参数 | 是否必传 | 说明           |
|----|------|--------------|
| pk | 是    | 设备productKey |
| dk | 是    | 设备deviceKey  |

返回值为升级方式枚举

**示例代码**

```kotlin
val mode = QuecOtaManager.getDeviceUpgradeChannel("pk", "dk")
```

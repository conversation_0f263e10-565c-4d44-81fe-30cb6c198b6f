# Quecthing 更新历史

## __版本2.23.0__
>* 增加modbus从机OTA功能
>* 优化modbus网关版本子设备上下线逻辑
>* 域名引导模块化
>* 重构一机一证与一机一密
>* SOTA功能优化

## __版本2.22.1__
>* 修复物模型精度丢失问题
>* 修复其他问题

## __版本2.22.0__
>* 增加全链路日志功能

## __版本2.21.0__
>* 增加域名引导功能

## __版本2.20.0__
>* 增加一机一证、双向认证、PSK支持

## __版本2.19.0__
>* 增加子设备OTA、信息、状态及定位功能

## __版本2.18.0__
>* 更改AT方案获取设备信息时，uuid由原先的dk改为mac/imei
>* oc方案中，获取设备信息和状态API传参为空时获取全部内容
>* 增加内置Wi-Fi定位功能
>* 增加NTP时间时区获取功能
>* 增加物模型的RAW类型支持
>* 增加一机一密的功能
>* 增加物模型服务功能
>* 增加数采功能(coap)
>* 增加子设备发送离线信息失败重发机制
>* 增加modbus读重发机制
>* 增加SOTA使用外部下载时，对外发送url信息

## __版本2.10.6__
>* ModbusDTU方案直连版方案变更为只允许添加一个modbus从机设备
>* ModbusDTU方案增加事件触发上报功能
>* ModbusDTU方案增加离散量支持

## __版本2.10.5__
>* openAPI新增Ql_iotTtlvldAddFloat，允许添加指定的浮点数精度
>* DMP FOTA更新成功后，再次连接到平台后才检测FOTA是否更新成功
 

### __版本2.10.4__
>* 设备绑定需求更新(ProductKey，ProductSecret，DeviceKey，DeviceSecret等产生变化更新BindSecret)
>* 配置本地设备服务器地址信息时不再清除DeviceSecret


### __版本2.10.3__
>* 更新设备重置功能
>* 更新设备绑定功能
>* 修复开发者中心 OTA潜在缺陷
>* 修复HTTP OTA合入模组问题
>* 修复AT方案中物模型JSON格式内存泄漏问题

### __版本2.10.0__
>* 新增modbus功能
>* 新增无线网关功能
>* 新增设备重置DS功能
>* 新增上行消息返回PkgID功能
>* 新增modbus网关版功能  

### __版本2.9.2__
>* 增加BindCode上报功能
>* 优化AT指令读取发送数据长度由适配层配置
>* HTTP OTA指令更改(之前与软件部CloudOTA指令冲突)
>* 修复OTA升级线程异步问题；
>* 优化错误码处理机制(删除部分不用错误码，新增平台下发错误码)
>* 新增平台下发错误码场景区分
>* 新增通信重复包过滤机制

### __版本2.9.0__
>* 增加支持网关设备（支持子设备认证、登录、登出、注销、业务通信）
>* 优化设备数据收发逻辑
>* 增加 HTTP OTA 对多固件计划的支持

### __版本2.8.3__
>* 增加 HTTP OTA
>* 增加 DNS优化处理  
>* 增加 OTA 对多组件和多固件的支持 
>* 优化内核层socket管理的资源锁

### __版本2.8.2__
>* 增加发起接入M2M后网络连接异常时发2,10476事件通知  
>* 增加应用API由用户设置DK和DS  
>* 增加M2M接入异常的惩罚机制  
>* 增加物模型AT支持JSON格式数据  
>* 增加生产环境HTTP域名强制转换  
>* 增加DNS后IP缓存，下次直接采用IP连接，除非IP失败再重新DNS  

### __版本2.7.2__
>* 增加局域网UDP发现设备，仅用于有局域网设备  
>* 对接新的mqtt broker，支持服务器域名切换和产品的PKPS切换  
>* 增加M2M通信数据可选加密  

### __版本2.6.1__
>* 修改位置上报命令，增加上报外挂GPS数据  
>* 对外增加获取设备信息和状态的API  
>* 用户配置信息增加AES128 ECB pcks#7加密存储  
>* 增加定位功能  
>* 发送透传或物模型为空内容时直接返回失败  
>* quos_socket发送数据限制由节点数量限制改为发送总字节数限制(默认50kbytes，具体按具体芯片RAM资源)  


### __版本2.3.4__
>* 修改MQTT心跳间隔默认5S  
>* 增加AT透传模式支持  


### __版本2.3.3__
>* 修改物模型发送结果通知，成功(4,10210)或失败(4,10310)  
>* SDK版本号恢复为2.3.3  
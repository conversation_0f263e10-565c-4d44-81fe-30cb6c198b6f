# 设备关联

设备关联可以将多个设备关联在一起，如：主设备是智能插座，从设备是智能开关。

## 功能概述

> QuecDeviceModel属性定义参考[设备管理](设备管理.md)

```objc
#import <QuecDeviceKit/QuecDeviceKit.h>
/// 初始化
[QuecDeviceAssociationService sharedInstance]
```

## 设备关联管理

### 设备关联绑定设备

**接口说明**

设备关联管理 - 绑定设备

```objc
- (void)deviceAssociationWithList:(NSArray<QuecDeviceModel *> *)list
                           master:(QuecDeviceModel *)masterDevice
                          success:(QuecVoidBlock)success
                          failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- |  
| list | 是 | 需要关联的从设备列表, QuecDeviceModel元素数组 |
| masterDevice | 是 | 主设备 |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**示例代码**

```objc
[QuecDeviceAssociationService.sharedInstance deviceAssociationWithList:@[deviceModel] master:masterDevice success:^{
        /// Next Action
    } failure:^(NSError *error) {
        NSLog(@"check error: %@", error);
    }];
```

### 关联关系查询

**接口说明**

设备关联管理 - 关联关系查询

```objc
- (void)getDeviceAssociationInfoWithMaster:(QuecDeviceModel *)masterDevice
                                      code:(NSString *)code
                                   success:(void(^)(QuecDeviceAssociationModel *model))success
                                   failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- |  
| masterDevice | 是 | 主设备 QuecDeviceModel类型 |
| code | 是 | 要查询的属性标识符，如果查询多个属性，使用英文逗号分隔 |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**QuecDeviceAssociationModel属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| slaveDeviceList  | NSArray<QuecDeviceAssociationSlaveModel *> *  | 从设备数据列表 |
| masterDevice  | QuecDeviceAssociationMasterModel  | 主设备数据 |

**QuecDeviceAssociationSlaveModel属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| deviceAssociationMasterId  | NSInteger  | 主设备id |
| deviceAssociationSlaveId  | NSInteger  | 从设备id |
| slaveDeviceKey  | NSString  | 从设备dk |
| slaveDeviceName  | NSString  | 从设备名 |
| slaveProductKey  | NSString  | 从设备pk |
| tslDeviceDetail  | QuecProductTSLInfoModel  | 物模型 |

>QuecProductTSLInfoModel属性定义同上

**QuecDeviceAssociationMasterModel属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| deviceAssociationMasterId  | NSInteger  | 主设备id |
| masterProductKey  | NSString  | 主设备pk |
| masterDeviceKey  | NSString  | 主设备dk |
| masterDeviceName  | NSString  | 主设备名 |
| siddhiName  | NSString  | 规则名称。全局唯一，由大小写英文字母和阿拉伯数字组成，长度32位 |

**示例代码**
```objc
[QuecDeviceAssociationService.sharedInstance getDeviceAssociationInfoWithMaster:masterDevice code:@"" success:^(QuecDeviceAssociationModel * _Nonnull model) {
        /// Next Action
    } failure:^(NSError *error) {
        NSLog(@"check error: %@", error);
    }];
```

### 关联关系解除

**接口说明**

设备关联管理 - 关联关系解除

```objc
- (void)deviceDisassociationWithDevice:(QuecDeviceModel *)model success:(QuecVoidBlock)success failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- |  
| model | 是 | 需要解除绑定设备, QuecDeviceModel类型 |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**示例代码**

```objc
[QuecDeviceAssociationService.sharedInstance deviceDisassociationWithDevice:device success:^{
        /// Next Action
    } failure:^(NSError *error) {
        NSLog(@"check error: %@", error);
    }];
```

### 关联关系配置

**接口说明**

设备关联管理 - 关联关系配置

```objc
- (void)deviceAssociationConfigWithProductKey:(NSString *)productKey success:(void(^)(QuecDeviceAssociationConfig *config))success failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- |  
| productKey | 是 | 产品pk |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	|

**QuecDeviceAssociationConfig属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| productKey  | NSString  | 主设备产品Key |
| masterItemCode  | NSString  | 主设备品类编码 |
| slaveItemCode  | NSString  | 从设备品类编码 |
| slaveDeviceLimit  | NSInteger  | 从设备数量上限 |
| slaveProductKey  | NSArray<NSString *> *  | 从设备pk数组 |

**示例代码**

```objc
[QuecDeviceAssociationService.sharedInstance deviceAssociationConfigWithProductKey:@"pk" success:^(QuecDeviceAssociationConfig * _Nonnull config) {
        /// Next Action
    } failure:^(NSError *error) {
        NSLog(@"check error: %@", error);
    }];
```

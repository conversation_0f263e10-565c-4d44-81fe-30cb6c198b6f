<template>
  <div class="cms-container">
    <div id="nc-root"></div>
  </div>
</template>

<script setup>
import { onMounted, ref } from "vue";
import CMS from "decap-cms-app";
import { cmsConfig } from "@/config/CmsConfig.js";
import { initializeSubmodulePlugin } from "@/plugins/SubmoduleCmsPlugin.js";

const isLoading = ref(true);
const error = ref(null);

const initCMS = async () => {
  try {
    console.log("正在初始化CMS...");

    // 初始化子模块插件
    console.log("正在初始化子模块插件...");
    await initializeSubmodulePlugin(CMS);

    // 初始化CMS
    console.log("正在初始化Decap CMS...");
    CMS.init({
      config: cmsConfig,
    });

    console.log("CMS初始化完成");
    isLoading.value = false;
  } catch (err) {
    console.error("CMS初始化失败:", err);
    error.value = err.message;
    isLoading.value = false;
  }
};

onMounted(() => {
  initCMS();
});
</script>

<style scoped>
.cms-container {
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

#nc-root {
  width: 100%;
  height: 100%;
}
</style>

# 概述
## **简介**
> 本工具通过图形化的配置界面，使用户可以快速进行modbus从机设备的产品信息配置以及功能定义操作，生成的配置文件可以直接在开发者中心进行物模型文件导入，以及设备配置文件烧写等，大大减轻了modbus设备的开发对接工作。
## **背景**
> 市面上存在着大量的modbus存量设备，很多客户有对设备进行智能化改造的需求，但是定制化开发对应的DTU成本过大，因此迫切需要一款可以兼容市面上标准modbus协议设备的DTU，把整个设备接入的成本和时间降低。开发者购买移远通用DTU，无需代码开发，使用PC端工具图形化配置modbus仪表信息和功能点信息，即可生成标准物模型文件和设备端文件，将物模型文件提交IoT平台，设备端文件写入DTU，即可通过DTU将modbus设备数据和物模型互转实现IoT平台的信息采集和控制。

## **工具下载**
<a href="https://iot.quectelcn.com/download?menuCode=DEVL_UTIL&resourceType=C" target="_blank">下载中心</a>


  

/**
 * Decap CMS 子模块插件
 * 为CMS提供子模块文件访问和同步功能
 */
import { submoduleService } from '../services/SubmoduleService.js';

export class SubmoduleCmsPlugin {
  constructor() {
    this.name = 'submodule-plugin';
    this.initialized = false;
  }

  /**
   * 初始化插件
   */
  async initialize(cms) {
    if (this.initialized) return;

    try {
      // 初始化子模块服务
      await submoduleService.initialize();

      // 注册CMS事件监听器
      this.registerCmsEventListeners(cms);

      // 添加自定义预览组件
      this.registerPreviewComponents(cms);

      // 添加编辑器工具栏
      this.registerEditorWidgets(cms);

      this.initialized = true;
      console.log('子模块CMS插件初始化完成');
    } catch (error) {
      console.error('子模块CMS插件初始化失败:', error);
    }
  }

  /**
   * 注册CMS事件监听器
   */
  registerCmsEventListeners(cms) {
    // 监听文件加载事件
    cms.registerEventListener({
      name: 'preSave',
      handler: async ({ entry }) => {
        console.log('准备保存文件:', entry.get('path'));
        // 在这里可以添加保存前的子模块同步逻辑
      }
    });

    // 监听文件保存事件
    cms.registerEventListener({
      name: 'postSave',
      handler: async ({ entry }) => {
        console.log('文件保存完成:', entry.get('path'));
        // 在这里可以添加保存后的子模块同步逻辑
      }
    });
  }

  /**
   * 注册预览组件
   */
  // registerPreviewComponents(cms) {
  //   // 注册自定义预览模板
  //   cms.registerPreviewTemplate('introduction', ({ entry, widgetFor }) => {
  //     const title = entry.getIn(['data', 'title']);
  //     const body = widgetFor('body');
      
  //     return `
  //       <div class="submodule-preview">
  //         <div class="preview-header">
  //           <h1>${title || '未命名文档'}</h1>
  //           <div class="submodule-info">
  //             <span class="badge">子模块: doc</span>
  //             <span class="sync-status">已同步</span>
  //           </div>
  //         </div>
  //         <div class="preview-content">
  //           ${body}
  //         </div>
  //       </div>
  //     `;
  //   });
  // }

  /**
   * 注册编辑器组件
   */
  registerEditorWidgets(cms) {
    // 注册子模块状态显示组件
    cms.registerWidget('submodule-status', {
      controlComponent: ({ value, onChange }) => {
        const status = submoduleService.getAllSubmoduleStatus();
        
        return `
          <div class="submodule-status-widget">
            <h4>子模块状态</h4>
            ${Object.entries(status).map(([name, stat]) => `
              <div class="status-item">
                <span class="name">${name}</span>
                <span class="status ${stat}">${this.getStatusText(stat)}</span>
              </div>
            `).join('')}
          </div>
        `;
      },
      previewComponent: ({ value }) => {
        return `<div class="submodule-status-preview">子模块状态: ${value || '正常'}</div>`;
      }
    });
  }

  /**
   * 获取状态文本
   */
  getStatusText(status) {
    const statusMap = {
      'synced': '已同步',
      'syncing': '同步中',
      'error': '错误',
      'unknown': '未知'
    };
    return statusMap[status] || status;
  }

  /**
   * 检查子模块文件访问权限
   */
  async checkFileAccess(collection, entry) {
    try {
      const folder = collection.get('folder');
      const slug = entry.get('slug');
      const filePath = `${folder}/${slug}.md`;
      
      // 检查是否为子模块路径
      if (folder.startsWith('doc/')) {
        const exists = await submoduleService.checkSubmoduleFile('doc', filePath.replace('doc/', ''));
        if (!exists) {
          console.warn(`子模块文件不存在: ${filePath}`);
          return false;
        }
      }
      
      return true;
    } catch (error) {
      console.error('检查文件访问权限失败:', error);
      return false;
    }
  }

  /**
   * 同步子模块
   */
  async syncSubmodules() {
    try {
      await submoduleService.syncAllSubmodules();
      console.log('子模块同步完成');
      return true;
    } catch (error) {
      console.error('子模块同步失败:', error);
      return false;
    }
  }

  /**
   * 获取插件状态
   */
  getStatus() {
    return {
      initialized: this.initialized,
      submodules: submoduleService.getAllSubmoduleStatus()
    };
  }
}

// 创建插件实例
export const submoduleCmsPlugin = new SubmoduleCmsPlugin();

/**
 * 初始化子模块CMS插件的辅助函数
 */
export const initializeSubmodulePlugin = async (cms) => {
  await submoduleCmsPlugin.initialize(cms);
  return submoduleCmsPlugin;
};

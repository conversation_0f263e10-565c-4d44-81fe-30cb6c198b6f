# 被分享人接受分享


**接口地址**:`/v2/binding/enduserapi/shareDevice`


**请求方式**:`POST`


**请求数据类型**:`application/json`


**响应数据类型**:`*/*`


**接口描述**:<p>被分享人接受分享</p>


**请求参数**:


| 参数名称   | 参数说明 | 请求类型 | 是否必须 | 数据类型 | schema |
| ---------- | -------- | -------- | -------- | -------- | ------ |
| shareCode  | 分享码   | query    | true     | string   |        |
| deviceName | 设备名称 | query    | false    | string   |        |


**响应状态**:


| 状态码 | 说明                                                         | schema             |
| ------ | ------------------------------------------------------------ | ------------------ |
| 200    | 被分享人接受分享成功                                         | 返回注册码响应数据 |
| 5032   | token 验证失败                                               |                    |
| 5049   | 设备已绑定                                                   |                    |
| 5106   | 请输入token                                                  |                    |
| 5136   | 请输入分享码                                                 |                    |
| 5137   | 无效的分享码                                                 |                    |
| 5138   | 设备分享失败                                                 |                    |
| 5151   | 覆盖标志为 3，分享码的设备使用到期时间不能小于正在使用的分享码的设备使用到期时间 |                    |
| 5333   | 设备名称超出长度限制                                         |                    |


**响应参数**:


| 参数名称 | 参数说明   | 类型           | schema         |
| -------- | ---------- | -------------- | -------------- |
| code     | 响应状态码 | integer(int32) | integer(int32) |
| data     | 响应数据   | object         |                |
| extMsg   | 扩展消息   | string         |                |
| msg      | 响应消息   | string         |                |


**响应示例**:
```javascript
{
	"code": 0,
	"data": {},
	"extMsg": "",
	"msg": ""
}
```

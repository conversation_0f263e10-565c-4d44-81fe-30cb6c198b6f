# 发起固件验证(可选)


为了确保用于升级的固件包是可用的，最大限度降低升级风险，在升级之前，您可以选择单台设备发起固件验证。

本文主要介绍固件验证的操作步骤。

## 前提条件

已添加需要验证的固件包。具体操作步骤，详见[**添加固件升级包**](/deviceDevelop/DeviceManage/ota/part02/page-01) 。

## 操作步骤

1、 在版本包管理列表，选择固件包点击 **固件验证**，配置参数后，单击 提交验证**，**在选择的设备上进行升级验证。

2、 如设备在线且与固件包版本匹配，会立即下发计划给到设备。

**注：**

1）固件验证最大时长60分钟，超时即为验证失败。

2）请确保用于验证的设备可用于升级且为在线状态。


**参数说明：**

| **参数** | **描述**                                                                                                                                                                                                                                                                       |
| -------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| 升级产品 | 选择用于验证设备的所属产品                                                                                                                                                                                                                                                     |
| 验证设备 | 选择升级产品再选择用于验证的设备。                                                                                                                                                                                                                                             |
| 组件标识 | 输入用于验证的设备模组固件或者MCU固件标识。  **注：** 请确保此信息的正确性。如何获取设备此信息，AT指令接入方案，详见[**此处**](/deviceDevelop/DeviceAccessPlan/cellular/AT/API/cellular-at-03) ；Open接入方案，详见[**此处**](/deviceDevelop/DeviceAccessPlan/cellular/QuecOpen/api/cellular-quecopen-api-02) 。 |

 
<a data-fancybox title="img" href="/zh/guide/ota/ota07.png">![img](/zh/guide/ota/ota07.png)</a>
 

<a data-fancybox title="img" href="/zh/guide/ota/ota08.png">![img](/zh/guide/ota/ota08.png)</a>

 

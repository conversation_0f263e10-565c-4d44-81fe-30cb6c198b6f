# 添加固件升级包

设备需要OTA升级，需先在平台添加固件升级包。

本文主要介绍添加固件升级包的操作步骤。

## 前提条件

1、 在平台创建升级产品。具体步骤，详见[**创建产品**](/deviceDevelop/ProductDevelop/ProductManagement)。

2、 设备连接平台。具体步骤，详见[**设备接入方案**](/deviceDevelop/DeviceAccessPlan/AccessPlan)。

3、 设备端已支持OTA升级功能。

 

## 操作步骤

1、 登录开发者中心，在左侧导航栏，选择 **设备管理** ，点击**OTA升级**。 

<a data-fancybox title="img" href="/zh/guide/ota/ota04.png">![img](/zh/guide/ota/ota04.png)</a>                     

 

2、 在 **版本包管理** 面板页，点击 **新增版本包**，配置参数后，点击 **确定，**完成添加。

 <a data-fancybox title="img" href="/zh/guide/ota/ota05.png">![img](/zh/guide/ota/ota05.png)</a>

**参数说明：**

| **参数**   | **描述**                                                     |
| ---------- | ------------------------------------------------------------ |
| 版本包名称 | 设置版本包名称，名称在用户账号下唯一，创建后不可修改。  长度限制100个字符。  **注：** 如果是企业用户，名称需在企业用户账号及其子账号下唯一。 |
| 组件类型   | 上传的固件包用于模组升级，选择模组固件；上传的固件包用于MCU升级，选择MCU固件。 |
| 版本包类型 | 整包：上传的版本包文件是完整的升级包文件，会推送整包升级包给设备进行升级。<br>差分包：上传的版本包文件仅包含新版本升级包与之前版本的差异部分，仅此升级包至设备。 |
| 目标版本   | 设置该固件包的版本号，用来说明此固件包升级后的版本号。  支持输入英文、数字、下划线"_"、连接符"-"、英文句号"."。 |
| 源版本     | 用来说明此固件包是从具体什么版本到目标版本的升级。  版本包类型选择差分包，需要设置此参数。 |
| 上传版本包 | 最多支持5个文件，当前支持.bin,.rar,.pack,.mini_1,.mini_2,.py,.tar,.s19,.bin_1,.bin_2,.zip,.tar.gz格式的文件，单文件大小不能超过100MB。  **注：** 如果当前支持的文件类型不能满足您的业务需求，可[**提交工单**](/personalCenter/WorkOrder)申请增加。 |
| 备注       | 用来说明此升级包的一些升级项说明，如bug修复，功能优化等描述信息。不超过200字符。 |


3、 添加成功的固件包，可直接用于升级计划。

4、 版本包管理列表查看已添加的固件包，见下图。


<a data-fancybox title="img" href="/zh/guide/ota/ota06.png">![img](/zh/guide/ota/ota06.png)</a>


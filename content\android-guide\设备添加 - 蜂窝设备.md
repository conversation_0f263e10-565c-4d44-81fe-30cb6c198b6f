---
title: "蜂窝设备添加"
description: "蜂窝设备添加相关文档"
---

# 蜂窝设备添加

## 功能概述

蜂窝设备启动后直接在线, 可以直接通过SN号添加

```kotlin
QuecDeviceService
```

## 在线设备直接添加

对于已经配网在线的设备, 可以直接进行设备添加

### 通过SN绑定设备

**接口说明**

通过SN绑定设备, 可以添加已经在线的设备

```kotlin
fun bindDeviceBySerialNumber(
    serialNumber: String,
    productKey: String,
    deviceName: String?,
    callback: QuecCallback<QuecDeviceBindSNModel>
)
```

**参数说明**

| 参数           | 是否必传 | 说明           |
|--------------|------|--------------|
| serialNumber | 是    | 设备序列号        |
| productKey   | 是    | 设备productKey |
| deviceName   | 否    | 设备名称         |
| callback     | 是    | 请求回调         |

**示例代码**

```kotlin
QuecDeviceService.bindDeviceBySerialNumber(content2, content1, null) {
    if (it.isSuccess) {
        //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg   //请求失败, 错误信息
    }
}
```

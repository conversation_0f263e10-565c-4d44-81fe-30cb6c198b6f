# Data Logs

This section outlines data logs. Data Logs record the uplink data sent from the device to Developer Center and the downlink data sent from Developer Center to the device.

## **Prerequisites**

● A product has been created.<br />
● A device of the product has been developed and debugged successfully.<br />
● An uplink data or downlink data has been sent.

## **Steps**

Log in to Developer Center, click "Device Management" → "Device Maintenance" in the left-hand navigation bar to display the list of devices. Find the device you want to view data logs for and click "View" in the Action column to enter the detail page of the device. Then click "Data Logs" tab.

<a data-fancybox title="img" href="/en/guide/image2022-3-8_20-11-43.png?version=1&modificationDate=1646740927000&api=v2">![img](/en/guide/image2022-3-8_20-11-43.png?version=1&modificationDate=1646740927000&api=v2)</a>

Parameters

| **Parameter**  | **Description**                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   |
| :------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| Ticket         | Unique data identifier.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |
| Creation Time  | The time when the data was stored.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                |
| Data Type      | **Uplink Data:** The commands or messages that the device reports to Developer Center. </br>**Downlink Data:**  The commands or messages that Developer Center sends to the device through API or Developer Center webpage.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| Sending Status | **Pending:**  When Developer Center sends downlink data to the device through device debugging or API, and the device is offline with a cache duration set, the status of the data will be shown as "**Pending**". This indicates that the data will be delivered to the device if it goes online, or sends uplink data within the cache duration. Note that the pending status only applies to downlink data.</br> **Sent:** The "Sent" status for downlink data indicates that the downlink data sent from Developer Center through device debugging or API has been successfully delivered to the device and a response has been received by Developer Center within 5 seconds. Similarly, the "Sent" status for uplink data indicates that the data sent from the device has been successfully delivered to Developer Center. </br>**Failed:** The "Failed" status for uplink data means that the device has connected to the gateway successfully, but no subsequent action has been performed, resulting in abnormal server or network. Similarly the "Failed" status for downlink data indicates that the downlink data sent from Developer Center through device debugging or API was not delivered to the device, or a response was not received by Developer Center within 5 seconds. This could be due to network or server issues, or the device being disconnected from the network. |
| Content        | Click   "View" button to view the data in various data formats. **HexString**:   Data or byte array is converted to hexadecimal characters; (for example,   binary 0x1234AB (3 bytes) is converted to string "1234AB" (6   bytes); String "1234AB" (6 bytes) is converted to binary 0x1234AB   (3 bytes)). **Base64**: Binary data based on 64 printable characters   (A-Z in upper case, a-z in lower case, +, /). **Text**: String in   UTF-8 encoding format. **JSON**: The format in which a TSL model is   transmitted and presented.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
| Send Date      | The "Transmission Date" of downlink data indicates the date when the data was sent from Developer Center to the device. The "Transmission Date" of uplink data indicates the date when Developer Center received the data sent from the device.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   |


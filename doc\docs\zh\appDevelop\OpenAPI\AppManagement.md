# App管理

App是application的缩写，通常专指手机上的应用软件，或称手机客户端。

当您需要在App端对设备进行控制或展示时，需要基于Open或SDK进行App开发。

## 创建App

在“App开发->App SDK”中点击“创建App”按钮，输入App的名称及描述，创建一个App。

App成功创建后会分配一个用户域。用户域用于对终端用户进行分组，不同App用户归属于不同的用户域下。

在App详情页面中，可获取用户域与用户域密码，使用注册/登录API时需通过该参数获取用户Token。

## 关联产品

您可通过关联产品功能，把企业下的产品授权给App。App仅可发现、绑定与控制已关联的产品。

## 自有账号体系

若您已有自己的终端用户系统，云平台支持通过OAuth 2.0认证的方式进行对接。

您可在App详情页中添加相关配置，需要填写已有终端用户系统的访问AppKey、AppSecret、URL相关信息。

在使用时，需在App登录页从App认证服务器获取AuthCode，并提交到云平台，云平台会根据您所填写的AppKey等信息，通过HTTP POST的方式向您的终端用户系统发送请求获取AccessToken，若校验通过，则App用户可调用云平台的相关功能。

## 删除App

当不再需要使用该App时，可通过操作列中的“删除”按钮执行删除操作。

删除后的数据不可恢复，请谨慎操作。
# 启用/停用家居模式


**接口地址**:`/v2/family/enduserapi/enabledFamilyMode`


**请求方式**:`PUT`


**请求数据类型**:`application/json`


**响应数据类型**:`*/*`


**接口描述**:<p>启用停用家居模式</p>


**请求参数**:


| 参数名称 | 参数说明              | 请求类型 | 是否必须 | 数据类型 | schema |
| -------- | --------------------- | -------- | -------- | -------- | ------ |
| enabled  | true-启用  false-停用 | query    | true     | boolean  |        |


**响应状态**:


| 状态码 | 说明                 | schema             |
| ------ | -------------------- | ------------------ |
| 200    | 启用停用家居模式成功 | 返回注册码响应数据 |
| 5623   | 请输入启用停用       |                    |


**响应参数**:


| 参数名称 | 参数说明   | 类型           | schema         |
| -------- | ---------- | -------------- | -------------- |
| code     | 响应状态码 | integer(int32) | integer(int32) |
| data     | 响应数据   | object         |                |
| extMsg   | 扩展消息   | string         |                |
| msg      | 响应消息   | string         |                |


**响应示例**:
```javascript
{
	"code": 0,
	"data": {},
	"extMsg": "",
	"msg": ""
}
```

# 用户密码修改


**接口地址**:`/v2/enduser/enduserapi/setUserPwd`


**请求方式**:`PUT`


**请求数据类型**:`application/json`


**响应数据类型**:`*/*`


**接口描述**:<p>用户密码修改</p>


**请求参数**:


| 参数名称 | 参数说明                                                     | 请求类型 | 是否必须 | 数据类型 | schema |
| -------- | ------------------------------------------------------------ | -------- | -------- | -------- | ------ |
| newPwd   | 新密码                                                       | query    | true     | string   |        |
| oldPwd   | 原密码                                                       | query    | true     | string   |        |
| random   | 用来对密码加密和解密的随机数。<br/><b>密码加密解密规则：</b><br/>AES128(pwd,MD5(random),初始向量)。<br/><b>AES 加密约定：</b><br/>加密模式：CBC<br/>填充：PKCS5Padding<br/>数据块位数：128位<br/>内容：pwd<br/>AES秘钥：MD5(random) ，MD5(random) 生成 16B 大写字符串（截取 32 长度 MD5 字符串的 8-24 部分）<br/>初始向量：将生成的AES密码（MD5(random)）拆分成长度为 8B 的两组字节数据，前后对调后加在一起生成新的 16B 字节数据作为偏移量<br/>输出：BASE64<br/>字符集：utf-8<br/><b>示例：</b><br/>pwd：china1234<br/>random：j1acpdj2bmtqZXVb<br/>AES key：89A049614445CCA8<br/>AES iv：4445CCA889A04961<br/>加密后：lkZMvj0KDSJXlp66jBieHA==<br/>解密后：china1234 | query    | false    | string   |        |


**响应状态**:


| 状态码 | 说明                                                 | schema             |
| ------ | ---------------------------------------------------- | ------------------ |
| 200    | 用户密码修改成功                                     | 返回注册码响应数据 |
| 5008   | 原密码不正确                                         |                    |
| 5032   | token 验证失败                                       |                    |
| 5043   | 用户密码修改失败                                     |                    |
| 5063   | 新密码不能和原密码一样                               |                    |
| 5505   | 请输入原密码                                         |                    |
| 5506   | 原密码解密失败                                       |                    |
| 5507   | 请输入新密码                                         |                    |
| 5508   | 新密码解密失败                                       |                    |
| 5509   | 原密码格式不正确，请输入 6-20 位字母、数字或特殊字符 |                    |
| 5510   | 新密码格式不正确，请输入 6-20 位字母、数字或特殊字符 |                    |


**响应参数**:


| 参数名称 | 参数说明   | 类型           | schema         |
| -------- | ---------- | -------------- | -------------- |
| code     | 响应状态码 | integer(int32) | integer(int32) |
| data     | 响应数据   | object         |                |
| extMsg   | 扩展消息   | string         |                |
| msg      | 响应消息   | string         |                |


**响应示例**:
```javascript
{
	"code": 0,
	"data": {},
	"extMsg": "",
	"msg": ""
}
```

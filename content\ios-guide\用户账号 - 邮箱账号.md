---
title: "邮箱账号"
description: "邮箱账号相关文档"
---

# 邮箱账号

本文介绍了如何使用邮箱注册账号, 登录等功能

## 功能概述

```objc
#import <QuecUserKit/QuecUserKit.h>
/// 初始化
[QuecUserService sharedInstance]
```

### 邮箱密码登录

**接口说明**

用于用户邮箱+密码登录

```objc
 */
- (void)loginByEmail:(NSString *)email 
            password:(NSString *)password
             success:(QuecVoidBlock)success
             failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- | 
| email |	是|邮箱| 
| password |	是|密码| 
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调，error表示失败原因	| 

**示例代码**

```objc
[[QuecUserService sharedInstance] loginByEmail:@"email" password:@"password" success:^{
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 邮箱密码注册

**接口说明**

用于用户邮箱+密码注册，需要获取优先验证码

```objc
- (void)registerByEmail:(NSString *)email 
                   code:(NSString *)code
               password:(NSString *)password
            nationality:(NSInteger)nationality
                   lang:(NSInteger)lang
               timezone:(NSInteger)timezone
                success:(QuecVoidBlock)success
                failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- | 
| email |	是|邮箱| 
| code |	是|验证码| 
| password |	是|密码| 
| nationality |	否|国家| 
| lang |	否|语言| 
| timezone |	否|时区| 
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调，error表示失败原因	| 

**示例代码**

```objc
[[QuecUserService sharedInstance] registerByEmail:@""
                                             code:@""
                                         password:@""
                                      nationality:0
                                             lang:0
                                         timezone:0
                                          success:^{
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```


### 发送邮件

**接口说明**

用于获取账号注册、密码重置、注销 验证码

```objc
 - (void)sendEmailWithType:(QuecEmailCodeType)type 
                     email:(NSString *)email
                   success:(QuecVoidBlock)success
                   failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|
| --- | --- | --- |
| type |	是|QuecEmailCodeType|
| email |是|收件人邮箱|
| success |	否|接口请求成功回调	|
| failure |	否|接口请求失败回调，error表示失败原因	|

```objc
typedef NS_ENUM(NSUInteger, QuecEmailCodeType)
{
    QuecEmailCodeTypeRegister = 1 ,  // 注册验证码
    QuecEmailCodeTypeReset = 2,      // 密码重置验证码
    QuecEmailCodeTypeLogout = 5      // 注销验证码
};
```

**示例代码**

```objc
[[QuecUserService sharedInstance] sendEmailWithType:QuecEmailCodeTypeRegister email:@"email" success:^{
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 验证用户发送的邮件验证码

**接口说明**

用于邮箱验证码正确性验证，需先获取验证码

```objc
- (void)validateEmailCodeByUserEmail:(NSString *)email 
                                  code:(NSString *)code
                            isDisabled:(NSInteger)isDisabled
                               success:(QuecVoidBlock)success
                               failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|
| --- | --- | --- |
| email      | 是       | 收件人邮箱                                      |
| code       | 是       | 验证码                                          |
| isDisabled | 是       | 验证码验证后是否失效，1：失效 2：不失效，默认 1 |
| success    | 否       | 接口请求成功回调                                |
| failure    | 否       | 接口请求失败回调，error表示失败原因                    |

**示例代码**

```objc
[[QuecUserService sharedInstance] validateEmailCodeByUserEmail:@"email" code:@"code" isDisabled:1 success:^{
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 邮箱重置密码

**接口说明**

用户通过邮箱+验证码重置密码

```objc
- (void)resetPasswordByEmail:(NSString *)email 
                        code:(NSString *)code
           internationalCode:(NSString *)internationalCode
                    password:(NSString *)password
                     success:(QuecVoidBlock)success
                     failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|
| --- | --- | --- |
| email |	是|邮箱	|
| code | 是 |验证码	|
| internationalCode |	否|国际代码，配合手机号码使用，默认为国内	|
| password |	否|用户重置的密码，如果不输入默认为 12345678求成功回调	|
| success |	否|接口请求成功回调	|
| failure |	否|接口请求失败回调	|

**示例代码**

```objc
[[QuecUserService sharedInstance] resetPasswordByEmail:@"email" code:@"code" internationalCode:@"countryCode" password:@"password" success:^{
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 邮箱是否已注册

**接口说明**

查询邮箱是否已注册

```objc
- (void)checkEmailRegister:(NSString *)email
                   success:(void(^)(BOOL isRegister))success
                   failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|
| --- | --- | --- |
| email   | 是       | 邮箱             |
| success | 否       | 接口请求成功回调 |
| failure | 否       | 接口请求失败回调 |


**示例代码**

```objc
[[QuecUserService sharedInstance] checkEmailRegister:@"account" success:^(BOOL isRegister) {
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

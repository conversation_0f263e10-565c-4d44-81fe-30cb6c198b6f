---
title: "设备控制"
description: "设备控制相关文档"
---

# 设备控制

## 功能概述

设备控制主要提供设备控制, 设备通道连接与断开

支持以下通道连接

1. 云端通道(ws)
2. wifi局域网通道
3. ble通道

当设备上云的话, ws通道会默认建立

当以默认方式连接设备时, 连接的优先级为:  云端 > wifi > ble

> QuecDeviceModel属性定义参考[设备管理](设备管理.md)

## 设备控制

### 初始化设备

根据设备 ID 去初始化设备控制类。

**接口说明**

> 注意
>
> 您需要调用获取设备列表接口后, 使用QuecDeviceModel初始化设备才能成功, 在初始化完成后, 可以使用initWithId方法直接取出缓存的client

```kotlin
//初始化or更新设备信息
fun initWithDevice(device: QuecDeviceModel): QuecDeviceClientApi
```

**参数说明**

| 参数     | 	是否必传 | 说明                |	
|--------|-------|-------------------|  
| device | 	是    | QuecDeviceModel对象 | 

```kotlin
//取出缓存的client
fun initWithId(deviceId: String): QuecDeviceClientApi?
```

| 参数       | 是否必传 | 说明                                                                                |
|----------|------|-----------------------------------------------------------------------------------|
| deviceId | 是    | QuecDeviceModel中属性, 自定义格式为:pk_dk. 例: "p00001_AABBCCDD0001" , channelId与deviceId一样 |

**示例代码**

```kotlin
val deviceModel = getDeviceModel() //从SDK设备列表接口获取的设备对象

val deviceClient = QuecDeviceClient.initWithDevice(deviceModel)

val cacheDeviceClient = QuecDeviceClient.initWithId(deviceModel.channelId)
```

### 设备状态监听

**接口说明**

> 实现 QuecDeviceClientDelegate 代理协议后，您可以在设备状态更变的回调中进行处理刷新设备在离线状态、页面UI显示等.

```kotlin
//添加监听
fun addListener(listener: Listener)

//移除监听
fun removeListener(listener: Listener)
```

**参数说明**

| 参数       | 	是否必传 | 说明                              |	
|----------|-------|---------------------------------|  
| listener | 	是    | QuecDeviceClientApi.Listener 对象 |

**QuecDeviceClientApi.Listener接口定义**

```kotlin
/**
 *  设备信息发生更新, 例如设备名发生改变
 *  @param device 设备对象
 */
fun deviceInfoUpdate(device: QuecDeviceModel)

/**
 * 设备在线状态更新
 * @param device 设备对象
 * @param onlineState  设备通道在线状态,按bit位放置在线状态 （0: all offline, 1: websocket online, 2 : wifi online, 3: wifi + websocket online, 4: ble online, 5: websocket + ble online, 6: ble + wifi online , 7: wifi + ble + ws online
 */
fun onlineUpdate(device: QuecDeviceModel, onlineState: Int)

/**
 * 设备连接中状态更新
 * @param device 设备对象
 * @param connectingState  设备通道连接中状态, 0: 未连接 1:连接中
 */
fun connectingStateUpdate(device: QuecDeviceModel, connectingState: Int)

/**
 * 设备被移除时调用
 * @param device 设备对象
 */
fun deviceRemoved(device: QuecDeviceModel)

/**
 * 设备数据更新, 例如物模型数据更新
 * @param device 设备对象
 * @param dps  命令字
 */
fun dpsUpdate(device: QuecDeviceModel, dps: QuecIotDataPointsModel)
```

**示例代码**

```kotlin
val deviceStatusListener = object : QuecDeviceClientApi.Listener {
    override fun connectingStateUpdate(device: QuecDeviceModel, connectingState: Int) {
    }
    
    override fun deviceInfoUpdate(device: QuecDeviceModel) {
    }
    
    override fun deviceRemoved(device: QuecDeviceModel) {
    }
    
    override fun dpsUpdate(device: QuecDeviceModel, dps: QuecIotDataPointsModel) {
    }
    
    override fun onlineUpdate(device: QuecDeviceModel, onlineState: Int) {  
    }
}

addListener(deviceStatusListener)

removeListener(deviceStatusListener)
```

**QuecIotDataPointsModel属性定义**

| 字段              | 类型                     | 描述     |
|-----------------|------------------------|--------|
| pk              | String                 | 产品pk   |
| dk              | String                 | 设备dk   |
| channelType     | QuecIotChannelType     | 通道类型   |
| action          | QuecIotDataPointAction | 动作类型   |
| dps             | List&lt;DataModel>     | dps数据  |
| originData      | JSONObject             | 原始通道数据 |
| transparentData | ByteArray              | 透传数据   |

**QuecIotDataPointsModel.DataModel属性定义**

| 字段       | 类型     | 描述     |
|----------|--------|--------|
| id       | int    | 数据id   |
| dataType | String | 数据类型   |
| value    | Any    | 数据值    |
| code     | String | 数据code | 

**QuecIotChannelType枚举定义**

| 字段                        | 描述        |
|---------------------------|-----------|
| QuecIotChannelTypeUnknown | 未知通道      |
| QuecIotChannelTypeWifi    | wifi局域网通道 |
| QuecIotChannelTypeWS      | 云通道       |
| QuecIotChannelTypeBLE     | 蓝牙近场通道    |

**QuecIotDataPointAction枚举定义**

只列举常用的类型

| 字段                                     | 描述      |
|----------------------------------------|---------|
| QuecIotDataPointActionUNKNOWN          | 未知类型    |
| QuecIotDataPointActionEVENT_INFO       | 设备事件信息  |
| QuecIotDataPointActionEVENT_WARN       | 设备告警信息  |
| QuecIotDataPointActionEVENT_ERROR      | 设备错误信息  |
| QuecIotDataPointActionLOCATION_INFO_KV | 设备位置信息  |
| QuecIotDataPointActionTSL_REPORT       | 设备物模型信息 |

### 设备通道连接与断开

**接口说明**

用于设备通道连接或断开, 需要先获取到当前QuecDeviceClient对象.

```kotlin
//建立连接, 自动选择连接方式
fun connect()

//建立连接, 指定连接方式
fun connectWithMode(mode: QuecIotDataSendMode)

//断开所有通道连接
fun disconnect()

//断开指定通道的连接
fun disconnectWithType(type: QuecIotChannelType)
```

**QuecIotDataSendMode枚举定义**

| 字段                      | 描述                |
|-------------------------|-------------------|
| QuecIotDataSendModeAuto | 自动选择              |
| QuecIotDataSendModeWS   | websocket连接, 远程控制 |
| QuecIotDataSendModeWifi | wifi局域网连接         |
| QuecIotDataSendModeBLE  | ble连接             |

**QuecIotChannelType枚举定义**

| 字段                     | 描述                |
|------------------------|-------------------|
| QuecIotChannelTypeWifi | wifi局域网连接         |
| QuecIotChannelTypeWS   | websocket连接, 远程控制 |
| QuecIotChannelTypeBLE  | ble连接             |

**示例代码**

```kotlin
deviceClient.connect()

deviceClient.connectWithMode(QuecIotDataSendMode.QuecIotDataSendModeBLE)

deviceClient.disconnect()

deviceClient.disconnectWithType(QuecIotChannelType.QuecIotChannelTypeBLE)
```

### 设备数据下发

**接口说明**

> 通过组装dps数据, 通过以下接口下发给设备

```kotlin
fun writeDps(dps: List<QuecIotDataPointsModel.DataModel>, callback: QuecSimpleCallBack?)

fun writeDps(
    dps: List<QuecIotDataPointsModel.DataModel>,
    mode: QuecIotDataSendMode,
    callback: QuecSimpleCallBack?
)

fun writeDps(
    dps: List<QuecIotDataPointsModel.DataModel>,
    mode: QuecIotDataSendMode,
    extraData: QuecIotChannelExtraData?,
    callback: QuecSimpleCallBack?
)
```

**参数说明**

| 参数        | 	是否必传 | 说明    |	
|-----------|-------|-------|  
| dps       | 	是    | dps数据 |
| mode      | 	否    | 连接方式  |
| extraData | 	否    | 额外数据  |
| callback  | 	否    | 执行回调  |

**QuecIotChannelExtraData属性定义**

| 字段         | 类型   | 描述       |
|------------|------|----------|
| cacheTime  | Long | 缓存时间     |
| msgId      | Long | 消息id     |
| isCache    | Bool | 是否缓存     |
| isCover    | Bool | 是否覆盖发送   |
| isSendJson | Bool | 是否发送json |

**示例代码**

```kotlin
deviceClient.writeDps(listOf(QuecIotDataPointsModel.DataModel().apply {
    id = 1
    dataType = QuecIotDataPointsModel.DataModel.QuecIotDataPointDataType.BOOL
    code = "switch"
    value = true
})) {
    if (it.isSuccess) {
        //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 设备数据读取

**接口说明**

> 通过组装dps数据, 查询设备信息

```kotlin
fun readDps(
    dps: List<QuecIotDataPointsModel.DataModel>,
    callback: QuecSimpleCallBack?
)

fun readDps(
    dps: List<QuecIotDataPointsModel.DataModel>,
    mode: QuecIotDataSendMode,
    callback: QuecSimpleCallBack?
)

fun readDps(
    dps: List<QuecIotDataPointsModel.DataModel>,
    mode: QuecIotDataSendMode,
    extraData: QuecIotChannelExtraData?,
    callback: QuecSimpleCallBack?
)
```

**参数说明**

> QuecIotDataPoint QuecIotChannelExtraData QuecIotChannelMode 定义同上

查询后通过设备状态更新回调接口dpsUpdate来获取到dps数据

**示例代码**

```kotlin
deviceClient.readDps(listOf(QuecIotDataPointsModel.DataModel().apply { 
    id = 1
    dataType = QuecIotDataPointsModel.DataModel.QuecIotDataPointDataType.BOOL
    code = "switch"
})) {
    if (it.isSuccess) {
        val data = it.data //请求成功
    } else {
        val code = it.code //请求失败, 错误
    }
}
```

### 发送透传数据

**接口说明**

发送透传数据, 注意, 该透传数据只能通过蓝牙通道发送

```kotlin
fun writeTransparent(
    transparentData: ByteArray,
    callback: QuecSimpleCallBack?
)
```

**参数说明**

| 参数              | 是否必传 | 说明    |
|-----------------|------|-------|  
| transparentData | 是    | 二进制数据 |
| callback        | 是    | 请求回调  |

```kotlin
deviceClient.writeTransparent(byteArrayOf(1, 2, 3)) {
    if (it.isSuccess) {
        //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

## 设备批量控制

### 通过云端批量控制

**接口说明**

设备批量控制, 通过云端控制

```kotlin
fun sendDataToDevicesByHttp(
    data: String,
    deviceList: List<QuecBatchControlDeviceModel>,
    type: Int,
    extraData: QuecBatchControlExtraModel?,
    callback: QuecCallback<QuecBatchControlModel>
)
```

**参数说明**

| 参数         | 	是否必传 | 说明                                                                              |	
|------------|-------|---------------------------------------------------------------------------------|  
| data       | 是     | 遵循tsl格式的json string                                                             |
| deviceList | 是     | 设备列表                                                                            |
| type       | 是     | 类型 1：透传 2：属性 3：服务                                                               |
| extraData  | 否     | dataFormat 数据类型 1：Hex 2：Text（当type为透传时，需要指定 dataFormat）,dataFormat在extraData中传递 |
| callback   | 是     | 请求回调                                                                            |

**QuecBatchControlDeviceModel属性定义**

| 字段                | 类型     | 描述     |
|-------------------|--------|--------|
| productKey        | String | 产品pk   |
| deviceKey         | String | 设备dk   |
| gatewayDeviceKey  | String | 网关设备dk |
| gatewayProductKey | String | 网关设备pk |

**QuecBatchControlExtraModel属性定义**

| 字段         | 类型  | 描述                                                    |
|------------|-----|-------------------------------------------------------|
| cacheTime  | int | 缓存时间，单位为秒，缓存时间范围 1-7776000 秒，启用缓存时必须设置缓存时间 （非必填）      |
| dataFormat | int | 数据类型 1：Hex 2：Text （当 type 为透传时，需要指定 dataFormat） （非必填） |
| isCache    | int | 是否启用缓存 1：启用 2：不启用，默认不启用 （非必填）                         |
| isCover    | int | 是否覆盖之前发送的相同的命令 1：覆盖 2：不覆盖，默认不覆盖，启用缓存时此参数有效 （非必填）      |
| qos        | int | QoS等级设置，参数值范围 0、1，默认为1 （非必填）                          |

**QuecBatchControlModel属性定义**

| 字段          | 类型                                   | 描述   |
|-------------|--------------------------------------|------|
| successList | List&lt;QuecBatchControlResultModel> | 成功列表 |
| failureList | List&lt;QuecBatchControlResultModel> | 失败列表 |

**QuecBatchControlResultModel属性定义**

| 字段     | 类型                          | 描述                 |
|--------|-----------------------------|--------------------|
| data   | QuecBatchControlDeviceModel | 控制设备信息             |
| ticket | String                      | ticket (仅成功列表有此属性) |
| msg    | String                      | 错误提示（仅失败列表有此属性）    |

**QuecBatchControlDeviceModel属性定义**

| 字段                | 类型     | 描述   |
|-------------------|--------|------|
| productKey        | String | 产品pk |
| deviceKey         | String | 设备dk |
| gatewayDeviceKey  | String | 网关dk |
| gatewayProductKey | String | 网关pk |

**示例代码**

```kotlin
QuecDeviceControlService.sendDataToDevicesByHttp(
    "[{\"read_write_float\":\"4.4\"}]",
    listOf(QuecBatchControlDeviceModel("pk", "dk")),
    2, null
) {
    if (it.isSuccess) {
        val data = it.data //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

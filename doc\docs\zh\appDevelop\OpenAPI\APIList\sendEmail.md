# 发送邮件验证码


**接口地址**:`/v2/mail/enduserapi/v2/sendEmail`


**请求方式**:`POST`


**请求数据类型**:`application/json`


**响应数据类型**:`*/*`


**接口描述**:<p>发送邮件</p>


**请求参数**:


| 参数名称   | 参数说明                                                     | 请求类型 | 是否必须 | 数据类型       | schema |
| ---------- | ------------------------------------------------------------ | -------- | -------- | -------------- | ------ |
| email      | 收件人邮箱                                                   | query    | true     | string         |        |
| emailType  | 邮件类型（1-注册验证码 2-密码重置验证码 3-关联邮箱验证码 4-删除邮箱关联验证码 5-用户注销验证码） | query    | true     | integer(int32) |        |
| userDomain | 用户域                                                       | query    | true     | string         |        |


**响应状态**:


| 状态码 | 说明           | schema             |
| ------ | -------------- | ------------------ |
| 200    | 发送邮件成功   | 返回注册码响应数据 |
| 5026   | 请输入邮箱     |                    |
| 5040   | 邮箱格式不正确 |                    |


**响应参数**:


| 参数名称 | 参数说明   | 类型           | schema         |
| -------- | ---------- | -------------- | -------------- |
| code     | 响应状态码 | integer(int32) | integer(int32) |
| data     | 响应数据   | object         |                |
| extMsg   | 扩展消息   | string         |                |
| msg      | 响应消息   | string         |                |


**响应示例**:
```javascript
{
	"code": 0,
	"data": {},
	"extMsg": "",
	"msg": ""
}
```

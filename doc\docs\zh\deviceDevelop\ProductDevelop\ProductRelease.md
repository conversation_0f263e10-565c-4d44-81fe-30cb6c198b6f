# 调试发布

产品发布是对当前产品状态的一个锁定，对于一些重要数据不可进行随意修改。其主要目的是为了防止控制台误操作导致量产设备不可用或数据解析错误。

在调试发布页面，可查看当前产品已分配的激活码数。若可用激活码为0，则新设备无法完成激活流程，需要先完成激活码分配操作。

完成激活码分配后，可输入您计划连接到云端的设备DeviceKey。设备激活成功后，您可通过数据调试功能进行上下行数据调试。

<a data-fancybox title="img" href="/zh/deviceDevelop/creatproduct/productrelease01.png">![img](/zh/deviceDevelop/creatproduct/productrelease01.png)</a>

## 产品发布

设备激活成功后可进行发布操作，发布前请检查各配置项是否已完成确认。产品发布后将无法编辑物模型、无法使用Web端设备调试功能。

若发布后需要对物模型进行调整，可通过取消发布操作回滚到开发中的状态。物模型修改后可能会影响已出货的设备，请谨慎操作。

<a data-fancybox title="img" href="/zh/deviceDevelop/creatproduct/productrelease02.png">![img](/zh/deviceDevelop/creatproduct/productrelease02.png)</a>

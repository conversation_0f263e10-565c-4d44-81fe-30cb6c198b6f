---
title: "IoT SDK"
description: "IoT SDK相关文档"
---

# IoT SDK



## 功能概述

QuecIotSdk用于移远IoT SDK的入口SDK，支持用户基于移远云或私有化部署开发。
使用SDK可以帮助开发者快速完成APP开发，开发者仅需关注APP的UI和UE即可，而相对复杂的协议与错误等事项的处理可忽略。
主要功能如下:

- 初始化SDK, 执行后其它用户、设备、家居等接口功能才能正常执行
- 开启Debug模式，打印日志用于分析问题
- 设置国家码，获取全球引导域名相关服务数据
- 获取国家列表数据

## SDK集成

### 第一步: 创建Android工程

在Android Studio中创建一个新的工程, 创建方式可参考[官方文档](https://developer.android.com/studio)

### 第二步: 配置依赖

在项目根目录下, 编辑build.gradle文件

```groovy
buildscript {
    repositories {
        //添加以下配置
        maven {
            url 'http://**************:8081/repository/maven-snapshots/'
            allowInsecureProtocol = true
        }
        maven {
            url 'http://**************:8081/repository/maven-releases/'
            allowInsecureProtocol = true
        }
    }
}
```

在项目app目录下, 编辑build.gradle文件

```groovy
dependencies {
    //添加核心库依赖
    implementation 'com.quectel.app.sdk:quec-iot-sdk:2.0.0'
}
```

### 第三步: 配置userDomain和userDomainSecret

在开发者中心上创建一个app可获取userDomain和userDomainSecret

<a data-fancybox title="img" href="/zh/appDevelop/androidGuide/create_sdk_app_1.png">![img](/zh/appDevelop/androidGuide/create_sdk_app_1.png)</a>

<a data-fancybox title="img" href="/zh/appDevelop/androidGuide/create_sdk_app_2.png">![img](/zh/appDevelop/androidGuide/create_sdk_app_2.png)</a>

### 第四步: 配置混淆规则

在项目app目录中的proguard-rules.pro 文件配置相应混淆配置。

```
-keep class  com.quectel.basic.common.entity.**{*;}
-keep class * implements com.quectel.basic.common.entity.QuecBaseModel{*;}
-keep class * implements com.quectel.basic.common.event.QuecBaseEvent{*;}

# RxJava
-dontwarn sun.misc.**
-keepclassmembers class rx.internal.util.unsafe.*ArrayQueue*Field* {
    long producerIndex;
    long consumerIndex;
}

# okhttp3
-keepattributes Signature
-keepattributes *Annotation*
-keep class com.squareup.okhttp.* { *; }
-keep interface com.squareup.okhttp.** { *; }
-dontwarn com.squareup.okhttp.**

# Okio
-dontwarn com.squareup.**
-dontwarn okio.**
-keep public class org.codehaus.* { *; }
-keep public class java.nio.* { *; }

# Gson specific classes
-dontwarn sun.misc.**
-keep class com.google.gson.examples.android.model.** { <fields>; }
-keep class * implements com.google.gson.TypeAdapterFactory
-keep class * implements com.google.gson.JsonSerializer
-keep class * implements com.google.gson.JsonDeserializer
-keepclassmembers,allowobfuscation class * {
    @com.google.gson.annotations.SerializedName <fields>;
}

# mmkv
-keep class com.tencent.mmkv.** {*;}

# eventbus
-keepattributes *Annotation*
-keepclassmembers class * {
    @org.greenrobot.eventbus.Subscribe <methods>;
}
-keep enum org.greenrobot.eventbus.ThreadMode { *; }
-keepclassmembers class org.greenrobot.eventbus.util.ThrowableFailureEvent {
    <init>(java.lang.Throwable);
}
-keep class org.greenrobot.eventbus.android.AndroidComponentsImpl

```

### 第五步: 初始化SDK

**接口说明**

初始化设置用户域和用户域秘钥, 并指定云服务类型, 设置后将调用对应的移远云中国、欧洲、北美服务

```kotlin
fun startWithConfig(
    application: Application,
    userDomain: String,
    userDomainSecret: String,
    cloudServiceType: QuecCloudServiceType
)
```

**参数说明**

| 参数               | 	是否必传 | 说明                  |	
|------------------|-------|---------------------| 
| application      | 是     | 应用程序对象              |
| userDomain       | 	是    | 用户域，DMP平台创建APP后生成   | 
| userDomainSecret | 	是    | 用户域秘钥，DMP平台创建APP后生成 | 
| cloudServiceType | 	是    | 云服务类型，指定连接的云服务      | 

**QuecCloudServiceType枚举定义**

| 字段                             | 描述 |
| -------------------------------- | ---- |
| QuecCloudServiceTypeChina        | 中国 |
| QuecCloudServiceTypeEurope       | 欧洲 |
| QuecCloudServiceTypeNorthAmerica | 北美 |

**示例代码**

```kotlin
class App : Application() {
    override fun onCreate() {
        super.onCreate()
        QuecIotSdk.startWithConfig(
            this,
            "X.XX.XXXX.X",
            "xxx",
            QuecCloudServiceType.QuecCloudServiceTypeChina
        )
    }
}
```

## 其他配置

### 私有化部署初始化

**接口说明**

支持私有化部署初始化, 用户可以配置SDK接入的云服务地址和域名引导地址等信息

```kotlin
fun startWithConfig(application: Application, config: QuecPublicConfig)
```

**参数说明**

| 参数     | 	是否必传 | 说明      |	
|--------|-------|---------| 
| config | 	是    | 初始化配置模型 | 

**QuecPublicConfig属性定义**

| 字段               | 类型     | 描述                    |
|------------------|--------|-----------------------|
| userDomain       | String | 用户域 （必填）              |
| userDomainSecret | String | 用户域秘钥 （必填）            |
| baseUrl          | String | 请求url （必填）            |
| webSocketUrl     | String | websocket1.0 地址 （必填）  |
| webSocketV2Url   | String | websocket2.0 地址 （必填）  |
| bootstrapUrl     | String | 获取全球域名引导服务请求域名 （必填）   |
| bootstrapPath    | String | 获取全球域名引导服务请求path （必填） |
| bootstrapToken   | String | 获取全球域名引导服务请求key （必填）  |
| mcc              | String | 域名引导国家mcc （非必填）       |
| tcpAddr          | String | 域名引导tcp地址 （非必填）       |
| pskAddr          | String | 域名引导psk地址 （非必填）       |
| tlsAddr          | String | 域名引导tls地址 （非必填）       |
| cerAddr          | String | 域名引导cer地址 （非必填）       |
| reportAddr       | String | 域名引导蓝牙数据上报url （非必填）   |

**示例代码**

```kotlin
QuecIotSdk.startWithConfig(
    application,
    QuecPublicConfig(
        userDomain = "X.XX.XXXX.X",
        userDomainSecret = "xxx",
        baseUrl = "https://xxx.com",
        webSocketUrl = "xxx://xxx.com/xx",
        webSocketV2Url = "xxx://xxx.com:xxxx/xx",
        bootstrapUrl = "https://xxx.com",
        bootstrapPath = "/xxx",
        bootstrapToken = "xxx",
    )
)
```

### Debug模式开关

**接口说明**

在开发的过程中可以开启Debug模式，打印日志用于分析问题。

```kotlin
fun setDebugMode(debugMode: Boolean)
```

**参数说明**

| 参数        | 	是否必传 | 说明           |	
|-----------|-------|--------------| 
| debugMode | 	是    | Debug模式开启或关闭 |

**示例代码**

```kotlin
QuecIotSdk.setDebugMode(true)
```

### 设置国家码

**接口说明**

用于通过国家码获取mcc，通过mcc获取全球引导域名相关服务数据。

```kotlin
fun setCountryCode(countryCode: String)
```

**参数说明**

| 参数          | 	是否必传 | 说明             |	
|-------------|-------|----------------| 
| countryCode | 	是    | 国家码, 如中国传入"86" |

**示例代码**

```kotlin
QuecIotSdk.setCountryCode("86")
```

### 获取国家列表数据

**接口说明**

获取移远云支持国家列表数据 (需要先调用初始化SDK 方法)。

```kotlin
fun getCountryData(): List<QuecCountryModel>
```

**参数说明**

**QuecCountryInfoModel属性定义**

| 字段                | 类型       | 描述             |
|-------------------|----------|----------------|
| name              | String | 国家名称           |
| internationalCode | String | 国家代码（例如："+86"） |

**示例代码**

```kotlin
val data = QuecIotSdk.getCountryData()
```

## 运行demo

### Demo介绍

> 注意
>
> - Demo需要使用到[开发者](https://iot.quectelcn.com/)平台，请先按照SDK集成第二步来获取用户域和用户域秘钥。
> - 该Demo仅用于演示SDK的运行效果，请勿直接商用, 实际使用中需要根据业务需求进行修改。

Demo应用主要包括以下功能:

- 用户管理: 手机号, 邮箱登录, 注册, 找回密码
- 设备管理: 添加设备, 删除设备, 查询设备列表, 查询设备详情, 控制设备, 设备分组
- 消息管理
- 场景,自动化

Demo下载地址: [SDK Demo](https://github.com/thridparty-cloud2/quecloud-iot-android-sdk-demo)

### Demo运行说明

1. 从gitlab上clone demo到本地
2. 修改Demo项目中的CloudConfig文件, 根据数据中心区域配置对应的用户域(USER_DOMAIN)和用户域秘钥(DOMAIN_SECRET)
3. 在Android Studio中点击运行, 即可运行Demo

# Publish Android App (International Version)

## Create Google Account

**1. Register a Google account (Skip this step if you already have one)** 

[https://accounts.google.com/SignUp](https://accounts.google.com/SignUp)

<a data-fancybox title="img" href="/en/appDevelop/oemapp/googlestore/googlestore1.png">![image](/en/appDevelop/oemapp/googlestore/googlestore1.png)</a>

In the "Choose your Gmail address" step, Gmail is recommended. If you want to use your email address, click **"Use your existing email" instead**. After filling it out, click "**Next**", agree to the terms, verify your phone number and email address, then you can log in. The account homepage address is [https://myaccount.google.com/](https://myaccount.google.com/).

**2. Enable developer permissions**: [https://play.google.com/apps/publish/signup/](https://play.google.com/apps/publish/signup/)

Official guidelines: [https://support.google.com/accounts/answer/27441?hl=en&sjid=9464741943980018757-AP](https://support.google.com/accounts/answer/27441?hl=en&sjid=9464741943980018757-AP).

## Create App 

Official guidelines: [https://support.google.com/googleplay/android-developer/answer/9859152?hl=en&ref_topic=7072031&sjid=2374854195900752047-AP](https://support.google.com/googleplay/android-developer/answer/9859152?hl=en&ref_topic=7072031&sjid=2374854195900752047-AP)

Visit [https://play.google.com/apps/publish/](https://play.google.com/apps/publish/) to enter the page below. Click "**Create app**".

<a data-fancybox title="img" href="/en/appDevelop/oemapp/googlestore/googlestore102.png">![image](/en/appDevelop/oemapp/googlestore/googlestore102.png)</a>

Select the **Default Language** and add the app name. Then select "**Create app**" to enter the detailed page.

<a data-fancybox title="img" href="/en/appDevelop/oemapp/googlestore/googlestore103.png">![image](/en/appDevelop/oemapp/googlestore/googlestore103.png)</a>

The required items will be displayed and you need to complete all the information before publishing the app.

<a data-fancybox title="img" href="/en/appDevelop/oemapp/googlestore/googlestore104.png">![image](/en/appDevelop/oemapp/googlestore/googlestore104.png)</a>

**1. Privacy Policy**

Enter the URL of the app privacy policy.

<a data-fancybox title="img" href="/en/appDevelop/oemapp/googlestore/googlestore105.png">![image](/en/appDevelop/oemapp/googlestore/googlestore105.png)</a>

**2. Ads**

Select whether the app contains ads according to the actual situation.

<a data-fancybox title="img" href="/en/appDevelop/oemapp/googlestore/googlestore106.png">![image](/en/appDevelop/oemapp/googlestore/googlestore106.png)</a>

**3. App access**

Select whether login credentials are required according to the actual situation.

<a data-fancybox title="img" href="/en/appDevelop/oemapp/googlestore/googlestore107.png">![image](/en/appDevelop/oemapp/googlestore/googlestore107.png)</a>

If login credentials are required, you need to provide a test account.

<a data-fancybox title="img" href="/en/appDevelop/oemapp/googlestore/googlestore109.png">![image](/en/appDevelop/oemapp/googlestore/googlestore109.png)</a>

<a data-fancybox title="img" href="/en/appDevelop/oemapp/googlestore/googlestore110.png">![image](/en/appDevelop/oemapp/googlestore/googlestore110.png)</a>

**4. Content ratings**

Enter the email address and select the app category.

<a data-fancybox title="img" href="/en/appDevelop/oemapp/googlestore/googlestore108.png">![image](/en/appDevelop/oemapp/googlestore/googlestore108.png)</a>

When filling in whether the app contains violence or frightening content, select "**No**".

<a data-fancybox title="img" href="/en/appDevelop/oemapp/googlestore/googlestore117.png">![image](/en/appDevelop/oemapp/googlestore/googlestore117.png)</a>

**5. Target audience and content**

Select age groups based on app content (multiple choice).

<a data-fancybox title="img" href="/en/appDevelop/oemapp/googlestore/googlestore111.png">![image](/en/appDevelop/oemapp/googlestore/googlestore111.png)</a>

**6. News apps**

Select whether your app is a news app.

<a data-fancybox title="img" href="/en/appDevelop/oemapp/googlestore/googlestore112.png">![image](/en/appDevelop/oemapp/googlestore/googlestore112.png)</a>

**7. COVID-19 contact tracing and status apps**

Select whether your app is a COVID-19 contact tracing or status app.

<a data-fancybox title="img" href="/en/appDevelop/oemapp/googlestore/googlestore113.png">![image](/en/appDevelop/oemapp/googlestore/googlestore113.png)</a>

**8. Data safety**

Fill in the app data safety questionnaire. Refer to [_Fill in Data Safety Questionnaire (Google Play)_](/appDevelop/OEMApp/app/google-data-inof) for details.

<a data-fancybox title="img" href="/en/appDevelop/oemapp/googlestore/googlestore114.png">![image](/en/appDevelop/oemapp/googlestore/googlestore114.png)</a>

**9. Advertising ID**

 Select whether your app uses an advertising ID.

<a data-fancybox title="img" href="/en/appDevelop/oemapp/googlestore/googlestore115.png">![image](/en/appDevelop/oemapp/googlestore/googlestore115.png)</a>

**10. Government apps**

Select whether your app is developed by or on behalf of a government.

<a data-fancybox title="img" href="/en/appDevelop/oemapp/googlestore/googlestore116.png">![image](/en/appDevelop/oemapp/googlestore/googlestore116.png)</a>

**11. Main store listing**

Fill in information such as app name, description, app icon and feature graphic.

<a data-fancybox title="img" href="/en/appDevelop/oemapp/googlestore/googlestore118.png">![image](/en/appDevelop/oemapp/googlestore/googlestore118.png)</a>

**12. Create production release**

Upload the Android App Bundle and set the release name and release notes.

<a data-fancybox title="img" href="/en/appDevelop/oemapp/googlestore/googlestore119.png">![image](/en/appDevelop/oemapp/googlestore/googlestore119.png)</a>

<a data-fancybox title="img" href="/en/appDevelop/oemapp/googlestore/googlestore120.png">![image](/en/appDevelop/oemapp/googlestore/googlestore120.png)</a>

For the app signing preference, select the recommended one.

<a data-fancybox title="img" href="/en/appDevelop/oemapp/googlestore/googlestore121.png">![image](/en/appDevelop/oemapp/googlestore/googlestore121.png)</a>

Select release countries based on the scope of the app. You can select all.

## Publish App

Once the information above is completed, you can publish your app. The first launch review for a new app usually takes some time. You will receive an email notification within 48 hours.

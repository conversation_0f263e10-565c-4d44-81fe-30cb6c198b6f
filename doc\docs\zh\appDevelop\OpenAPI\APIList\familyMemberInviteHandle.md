# 家庭邀请成员处理


**接口地址**:`/v2/family/enduserapi/familyMemberInviteHandle`


**请求方式**:`POST`


**请求数据类型**:`application/json`


**响应数据类型**:`*/*`


**接口描述**:<p>家庭邀请成员的处理</p>


**请求参数**:


| 参数名称 | 参数说明                              | 请求类型 | 是否必须 | 数据类型       | schema |
| -------- | ------------------------------------- | -------- | -------- | -------------- | ------ |
| decide   | 对于邀请的决定：0-拒绝邀请 1-同意邀请 | query    | true     | integer(int32) |        |
| fid      | 家庭ID                                | query    | true     | string         |        |


**响应状态**:


| 状态码 | 说明                   | schema             |
| ------ | ---------------------- | ------------------ |
| 200    | 家庭邀请成员的处理成功 | 返回注册码响应数据 |
| 5636   | 请输入家庭ID           |                    |
| 5654   | 请输入是否接受邀请     |                    |
| 5655   | 接受邀请信息格式错误   |                    |


**响应参数**:


| 参数名称 | 参数说明   | 类型           | schema         |
| -------- | ---------- | -------------- | -------------- |
| code     | 响应状态码 | integer(int32) | integer(int32) |
| data     | 响应数据   | object         |                |
| extMsg   | 扩展消息   | string         |                |
| msg      | 响应消息   | string         |                |


**响应示例**:
```javascript
{
	"code": 0,
	"data": {},
	"extMsg": "",
	"msg": ""
}
```

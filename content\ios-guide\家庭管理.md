---
title: "家庭管理"
description: "家庭管理相关文档"
---

# 家庭管理

## 功能概述

家庭 SDK 是为全屋智能业务场景的移动端开发提供的模块化开发工具，涵盖了各类模块与接口的调用能力。
其中，“家庭”是对全屋智能场景中“家”或“场所”这一单位的抽象概念，用于统一管理用户在该范围内的设备、账号、权限等信息的集合。

家庭管理主要包含以下能力：

- 查询用户的家庭列表
- 获取指定家庭下的所有设备与群组信息
- 添加、编辑或删除家庭
- 管理家庭的名称、地理位置、房间列表、成员等信息
- 在家庭中进行设备的添加、移除及信息修改等操作

## SDK集成方式

> **注意**
>
> 家庭SDK运行依赖核心库QuecIotSdk, 请先按照快速集成文档集成核心库

```objc
pod 'QuecSmartHomeKit', '~> 2.1.0'
```

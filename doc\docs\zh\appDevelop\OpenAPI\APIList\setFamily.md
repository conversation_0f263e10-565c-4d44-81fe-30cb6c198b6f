# 修改家庭


**接口地址**:`/v2/family/enduserapi/setFamily`


**请求方式**:`PUT`


**请求数据类型**:`application/json`


**响应数据类型**:`*/*`


**接口描述**:<p>修改家庭</p>

**请求参数**:


| 参数名称          | 参数说明       | 请求类型 | 是否必须 | 数据类型 | schema |
| ----------------- | -------------- | -------- | -------- | -------- | ------ |
| fid               | 家庭ID         | query    | true     | string   |        |
| familyCoordinates | 家庭经纬度坐标 | query    | false    | string   |        |
| familyLocation    | 家庭地址       | query    | false    | string   |        |
| familyName        | 家庭名称       | query    | false    | string   |        |


**响应状态**:


| 状态码 | 说明                                      | schema             |
| ------ | ----------------------------------------- | ------------------ |
| 200    | 家庭修改成功                              | 返回注册码响应数据 |
| 5041   | 没有权限                                  |                    |
| 5616   | 家庭名称已存在                            |                    |
| 5618   | 请输入家庭ID                              |                    |
| 5621   | 家庭修改失败                              |                    |
| 5633   | 经纬度格式错误                            |                    |
| 5635   | 未开启家居模式                            |                    |
| 5659   | 家庭名称超长，请输入256字符以内的家庭名称 |                    |
| 5660   | 家庭地址超长，请输入256字符以内的家庭名称 |                    |
| 6049   | 权限不足,当前用户是普通成员               |                    |


**响应参数**:


| 参数名称 | 参数说明   | 类型           | schema         |
| -------- | ---------- | -------------- | -------------- |
| code     | 响应状态码 | integer(int32) | integer(int32) |
| data     | 响应数据   | object         |                |
| extMsg   | 扩展消息   | string         |                |
| msg      | 响应消息   | string         |                |


**响应示例**:
```javascript
{
	"code": 0,
	"data": {},
	"extMsg": "",
	"msg": ""
}
```

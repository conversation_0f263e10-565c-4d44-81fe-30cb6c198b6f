# QQ Authorization Login


**Interface Address**:`/v2/enduser/enduserapi/qqAuthLogin`


**Request Method**:`POST`


**Request Data Type**:`application/json`


**Response Data Type**:`*/*`


**Interface Description**:`QQ Authorization Login`


**Request Parameters**:


| Parameter Name | Parameter Description                                         | Request Type | Required | Data Type      | schema |
| -------------- | ------------------------------------------------------------- | ------------ | -------- | -------------- | ------ |
| accessToken    | access_token obtained after QQ authorization                  | query        | true     | string         |        |
| signature      | Signature<br/>Signature generation rule: SHA256(accessToken+userDomainSecret) | query        | true     | string         |        |
| userDomain     | User domain                                                   | query        | true     | string         |        |
| mcc            | MCC                                                           | query        | false    | integer(int32) |        |


**Response Status**:


| Status Code | Description                       | schema                                      |
| ----------- | --------------------------------- | ------------------------------------------- |
| 200         | Login successful, token returned in data | Registration code response data«Login token return» |
| 5013        | User domain format incorrect      |                                             |
| 5015        | User domain does not exist        |                                             |
| 5023        | Please enter user domain          |                                             |
| 5104        | User domain has been disabled     |                                             |
| 5366        | Please enter accessToken          |                                             |
| 5367        | Exception requesting user OpenID  |                                             |
| 5368        | Failed to get user OpenID         |                                             |
| 5369        | Failed to parse user OpenID       |                                             |
| 5420        | Signature verification failed     |                                             |
| 5550        | Please enter signature            |                                             |
| 6084        | MCC does not belong to the current data center |                                |
| 6085        | Failed to query data center for MCC |                                           |


**Response Parameters**:


| Parameter Name                         | Parameter Description                                        | Type             | schema           |
| -------------------------------------- | ------------------------------------------------------------ | ---------------- | ---------------- |
| code                                   | Response status code                                         | integer(int32)   | integer(int32)   |
| data                                   | Response data                                                | Login token return | Login token return |
| &emsp;&emsp;accessToken                | Interface access token                                       | token            | token            |
| &emsp;&emsp;&emsp;&emsp;expirationTime | Expiration timestamp in seconds. Token becomes invalid after this time. | integer          |                  |
| &emsp;&emsp;&emsp;&emsp;token          | token                                                        | string           |                  |
| &emsp;&emsp;refreshToken               | Refresh token                                                | token            | token            |
| &emsp;&emsp;&emsp;&emsp;expirationTime | Expiration timestamp in seconds. Token becomes invalid after this time. | integer          |                  |
| &emsp;&emsp;&emsp;&emsp;token          | token                                                        | string           |                  |
| extMsg                                 | Extended message                                             | string           |                  |
| msg                                    | Response message                                             | string           |                  |


**Response Example**:
```javascript
{
	"code": 0,
	"data": {
		"accessToken": {
			"expirationTime": 0,
			"token": ""
		},
		"refreshToken": {
			"expirationTime": 0,
			"token": ""
		}
	},
	"extMsg": "",
	"msg": ""
}
```
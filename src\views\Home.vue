<template>
  <div class="home">
    <header class="header">
      <h1>This is a test demo in the local.env</h1>
      <nav class="nav">
        <router-link to="/" class="nav-link">首页</router-link>
        <router-link to="/admin" class="nav-link admin-link"
          >管理后台</router-link
        >
      </nav>
    </header>
  </div>
</template>

<script setup></script>

<style scoped>
.home {
  min-height: 100vh;
  background: black;
  color: white;
}

.header {
  text-align: center;
  padding: 60px 20px 40px;
}

.header h1 {
  font-size: 6rem;
  margin-bottom: 10px;
  font-weight: 700;
}

.nav {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.nav-link {
  color: white;
  text-decoration: none;
  padding: 12px 24px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.nav-link:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.admin-link {
  background: rgba(174, 0, 255, 0.2);
  font-weight: 600;
}

@media (max-width: 768px) {
  .header h1 {
    font-size: 2rem;
  }
}
</style>

# 群组控制

## 功能概述

设备群组，将多个设备组织起来，形成一个群体，以便于管理和控制。

> 注意
>
> **群组功能只有在家居模式启动后, 才能使用群组功能**



SDK所有需要传fid参数的地方, 均可以调用QuecFamilyUtil.getFamilyId()来获取

## SDK集成方式

> **注意**
>
> 群组SDK运行依赖核心库quec-iot-sdk, 请先按照快速集成文档集成核心库

引入依赖

```groovy
implementation 'com.quectel.app.sdk:quec-group-sdk:2.14.0'
```

## 功能列表

### 创建群组

**接口说明**

创建群组

```kotlin
fun createGroup(bean: QuecGroupCreateBean, callback: QuecCallback<QuecGroupCreateResultBean>)
```

**参数说明**

| 参数       | 	是否必传 | 说明                     |	
|----------|-------|------------------------| 
| bean     | 	是    | QuecGroupCreateBean类对象 | 
| callback | 是     | 请求回调                   |

**QuecGroupCreateBean属性定义**

| 字段              | 类型                                 | 描述                 |
|-----------------|------------------------------------|--------------------|
| groupDeviceName | String                             | 群组名称               |
| fid             | String                             | 家庭id               |
| frid            | String                             | 房间id               |
| isCommonUsed    | BOOL                               | 是否是常用设备：0-不常用 1-常用 |
| deviceList      | List&lt;QuecGroupCreateDeviceBean> | 设备列表               |

**QuecGroupCreateDeviceBean属性定义**

| 字段         | 类型     | 描述   |
|------------|--------|------|
| productKey | String | 产品pk |
| deviceKey  | String | 设备dk |

**示例代码**

```kotlin
QuecGroupService.createGroup(QuecGroupCreateBean().apply {
    groupDeviceName = "test"
    fid = QuecFamilyUtil.getFamilyId()
    frid = "C17592"
    isCommonUsed = true
}) {
    if (it.isSuccess) {
        val data = it.data //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 群组详情

**接口说明**

查询群组详情

```kotlin
fun getGroupInfo(gdid: String, callback: QuecCallback<QuecGroupBean>)
```

**参数说明**

| 参数       | 	是否必传 | 说明   |	
|----------|-------|------| 
| groupId  | 	是    | 群组id | 
| callback | 是     | 请求回调 |

> 返回类型为QuecGroupBean, 属性定义同上

**示例代码**

```kotlin
QuecGroupService.getGroupInfo("fdid") {
    if (it.isSuccess) {
        val data = it.data //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

**QuecGroupBean属性定义**

| 字段                   | 类型                           | 描述                 |
|----------------------|------------------------------|--------------------|
| name                 | String                       | 群组名称               |
| fid                  | String                       | 家庭id               |
| frid                 | String                       | 房间id               |
| gdid                 | String                       | 群组id               |
| roomName             | String                       | 房间名                |
| onlineStatus         | int                          | 设备状态：0-离线 1-在线     |
| productKey           | String                       | 产品pk               |
| deviceKey            | String                       | 设备dk               |
| isCommonUsed         | BOOL                         | 是否是常用设备：0-不常用 1-常用 |
| groupDeviceDeviceNum | int                          | 群组包含设备数量           |
| deviceList           | List&lt;QuecGroupDeviceBean> | 设备列表               |

**QuecGroupDeviceBean属性定义**

| 字段                  | 类型     | 描述              |
|---------------------|--------|-----------------|
| productKey          | String | 产品pk            |
| deviceKey           | String | 设备dk            |
| deviceName          | String | 设备名             |
| logoImage           | String | 设备logo图片        |
| frid                | String | 房间id            |
| roomName            | String | 房间名称            |
| onlineStatus        | int    | 设备云状态：0-离线 1-在线 |
| msg                 | String | 接口响应消息提示        |
| code                | int    | 接口响应code        |
| lowPowerProduct     | BOOL   | 是否为低功耗产品        |
| lowPowerStatus      | BOOL   | 低功耗开关状态         |
| lowPowerCache       | int    | 低功耗缓存时长，单位为秒    |
| lowPowerAlive       | int    | 低功耗心跳周期（分）      |
| lastOfflineTimeTs   | int    | 离线时间戳           |
| capabilitiesBitmask | int    | 设备通道能力mask      |



### 群组基础信息

**接口说明**

查询群组基础信息

```kotlin
fun getGroupDeviceInfo(
    gdid: String,
    callback: QuecCallback<QuecDeviceModel>
)
```

**参数说明**

| 参数       | 	是否必传 | 说明   |	
|----------|-------|------| 
| groupId  | 	是    | 群组id | 
| callback | 是     | 请求回调 |

> 返回类型为QuecDeviceModel, 属性定义在设备SDK

**示例代码**

```kotlin
QuecGroupService.getGroupDeviceInfo("gdid") {
    if (it.isSuccess) {
        val data = it.data //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 设备是否可加入群组

**接口说明**

批量判断设备是否可加入群组

```kotlin
fun checkDeviceAddGroup(
    fid: String,
    deviceList: List<QuecGroupCreateDeviceBean>,
    callback: QuecCallback<List<QuecGroupDeviceBean>?>
)
```

**参数说明**

| 参数         | 	是否必传 | 说明                           |	
|------------|-------|------------------------------| 
| fid        | 	是    | 家庭id                         | 
| deviceList | 	是    | QuecGroupCreateDeviceBean类对象 | 
| callback   | 是     | 请求回调                         |

> QuecGroupDeviceBean QuecGroupCreateDeviceBean属性定义同上

**示例代码**

```kotlin
QuecGroupService.checkDeviceAddGroup(
    QuecFamilyUtil.getFamilyId(), listOf(
    QuecGroupCreateDeviceBean().apply {
        productKey = "pk"
        deviceKey = "dk"
    }
)) {
    if (it.isSuccess) {
        val data = it.data //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 可添加设备列表

**接口说明**

获取可添加设备列表

```kotlin
fun getAddableList(
    fid: String,
    gdid: String,
    deviceList: List<QuecGroupCreateDeviceBean>,
    page: Int,
    pageSize: Int,
    callback: QuecCallback<QuecPageResponse<QuecGroupDeviceBean>>
)
```

**参数说明**

| 参数        | 	是否必传 | 说明                        |	
|-----------|-------|---------------------------| 
| addedList | 	是    | QuecGroupCreateDeviceBean | 
| fid       | 	是    | 家庭id                      | 
| gdid      | 	否    | 群组id                      | 
| pageSize  | 	否    | 分页大小，默认为 10 条             | 
| page      | 	否    | 分页，默认为第 1 页               | 
| callback  | 是     | 请求回调                      |

> QuecGroupCreateDeviceBean属性定义同上

**示例代码**

```kotlin
QuecGroupService.getAddableList(
    QuecFamilyUtil.getFamilyId(),
    "gdid",
    listOf(QuecGroupCreateDeviceBean().apply {
        productKey = "pk"
        deviceKey = "dk"
    }),
    1, 10
) {
    if (it.isSuccess) {
        val data = it.data //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 控制群组

**接口说明**

控制群组设备

```kotlin
fun controlGroupByHttp(
    data: List<QuecIotDataPointsModel.DataModel>,
    gdid: String,
    extraData: DpsHttpExtraDataBean?,
    callback: QuecCallback<Unit>
)
```

**参数说明**

| 参数        | 	是否必传 | 说明                 |	
|-----------|-------|--------------------| 
| dps       | 	是    | dps数据,具体格式封装同设备SDK | 
| groupId   | 	是    | 群组id               | 
| extraData | 	否    | 额外数据               | 
| callback  | 是     | 请求回调               |

**extraData额外数据NSDictionary 传入说明**

```kotlin
{
 type 类型 1：透传 2：属性 3：服务
 dataFormat 数据类型 1：Hex 2：Text（当type为透传时，需要指定 dataFormat）
 cacheTime 缓存时间，单位为秒，缓存时间范围 1-7776000 秒，启用缓存时必须设置缓存时间
 isCache  是否启用缓存 1：启用 2：不启用，默认不启用
 isCover 是否覆盖之前发送的相同的命令 1：覆盖 2：不覆盖，默认不覆盖，启用缓存时此参数有效
 }
```

> QuecIotDataPoint属性定义同设备SDK

**示例代码**

```kotlin
QuecGroupService.controlGroupByHttp(listOf(QuecIotDataPointsModel.DataModel().apply {
    id = 1
    dataType = QuecIotDataPointsModel.DataModel.QuecIotDataPointDataType.BOOL
    value = true
    code = "switch"
}), "gdid", null) {
    if (it.isSuccess) {
        //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 查询群组物模型属性值

**接口说明**

查询群组物模型属性值

```kotlin
fun groupAttributesById(
    groupId: String,
    codeList: String?,
    callback: QuecCallback<QuecProductTSLInfoModel>
)
```

**参数说明**

| 参数       | 	是否必传 | 说明               |	
|----------|-------|------------------| 
| groupId  | 	是    | 群组id             | 
| codeList | 	否    | 需要查询的属性值，用英文逗号拼接 | 
| callback | 是     | 请求回调             |

> QuecProductTSLInfoModel属性定义同设备SDK

**示例代码**

```kotlin
QuecGroupService.groupAttributesById("gid", "switch") {
    if (it.isSuccess) {
        val data = it.data //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 群组物模型属性,包含属性值

**接口说明**

查询群组物模型属性,包含属性值

```kotlin
fun groupAttributesWithTSL(
    groupId: String,
    productKey: String,
    codeList: String?,
    callback: QuecCallback<List<QuecProductTSLPropertyModel<Any>>>
)
```

**参数说明**

| 参数         | 	是否必传 | 说明                     |	
|------------|-------|------------------------| 
| bean       | 	是    | QuecGroupCreateBean类对象 | 
| productKey | 	是    | 产品pk                   | 
| codeList   | 	否    | 需要查询的属性值，用英文逗号拼接       | 
| callback   | 是     | 请求回调                   |

> QuecProductTSLPropertyModel属性定义同设备SDK

**示例代码**

```kotlin
QuecGroupService.groupAttributesWithTSL("groupId", "pk", null) {
    if (it.isSuccess) {
        val data = it.data //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 编辑群组基础信息

**接口说明**

用于群组基础信息修改

```kotlin
fun editGroupBasicInfo(
    groupId: String,
    groupDeviceName: String,
    fid: String?,
    frid: String?,
    callback: QuecCallback<Unit>
)
```

**参数说明**

| 参数              | 	是否必传 | 说明                 |	
|-----------------|-------|--------------------| 
| groupId         | 	是    | 群组id               | 
| groupDeviceName | 	是    | 群组名称               | 
| fid             | 	否    | 家庭id               | 
| frid            | 	否    | 房间id               | 
| isCommonUsed    | 	否    | 是否是常用设备：0-不常用 1-常用 | 
| callback        | 是     | 请求回调               |

**示例代码**

```kotlin
QuecGroupService.editGroupBasicInfo(
    "gid",
    "newName",
    QuecFamilyUtil.getFamilyId(),
    "frid"
) {
    if (it.isSuccess) {
        //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 编辑群组信息

**接口说明**

编辑群组信息

```kotlin
fun editGroupInfo(
    groupId: String,
    groupDeviceName: String,
    fid: String?,
    frid: String?,
    isCommonUsed: Boolean,
    deviceList: List<QuecGroupCreateDeviceBean>,
    callback: QuecCallback<QuecGroupCreateResultBean>
)
```

**参数说明**

| 参数              | 	是否必传 | 说明                 |	
|-----------------|-------|--------------------| 
| groupId         | 	是    | 群组id               | 
| groupDeviceName | 	是    | 群组名称               | 
| fid             | 	否    | 家庭id               | 
| frid            | 	否    | 房间id               | 
| deviceList      | 	是    | 设备列表               | 
| isCommonUsed    | 	否    | 是否是常用设备：0-不常用 1-常用 | 
| callback        | 是     | 请求回调               |

**示例代码**

```kotlin
QuecGroupService.editGroupInfo(
    "gid", "newName", QuecFamilyUtil.getFamilyId(), "frid", true,
    listOf(QuecGroupCreateDeviceBean().apply {
        productKey = "pk"
        deviceKey = "dk"
    })
) {
    if (it.isSuccess) {
        //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 批量移除/添加群组到常用

**接口说明**

批量移除/添加群组到常用

```kotlin
fun batchAddCommon(
    gdidList: MutableList<String>,
    fid: String,
    isCommonUsed: Boolean,
    callback: QuecCallback<QuecGroupBatchResultBean>
)
```

**参数说明**

| 参数           | 	是否必传 | 说明                 |	
|--------------|-------|--------------------| 
| groupIds     | 	是    | 群组id List          | 
| fid          | 	否    | 家庭id               |
| isCommonUsed | 	否    | 是否是常用设备：0-不常用 1-常用 | 
| callback     | 是     | 请求回调               |

**QuecGroupBatchResultBean属性定义**

| 字段   | 类型     | 描述    |
|------|--------|-------|
| code | String | code码 |
| msg  | String | 提示信息  |
| gdid | String | 群组id  |

**示例代码**

```kotlin
QuecGroupService.batchAddCommon(mutableListOf("gid"), QuecFamilyUtil.getFamilyId(), true) {
    if (it.isSuccess) {
        //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 移动群组到新房间

**接口说明**

批量移动群组到新房间, 需开启家庭模式

```kotlin
fun batchMoving(
    gdidList: List<String>,
    newFrid: String,
    callback: QuecCallback<QuecGroupBatchResultBean>
)
```

**参数说明**

| 参数       | 	是否必传 | 说明        |	
|----------|-------|-----------| 
| groupIds | 	是    | 群组id List | 
| newFrid  | 	否    | 新房间id     |
| callback | 是     | 请求回调      |

> QuecGroupBatchResultBean属性定义同上

**示例代码**

```kotlin
QuecGroupService.batchMoving(listOf("gid"), "newFrid") {
    if (it.isSuccess) {
        //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 批量删除群组

**接口说明**

批量删除群组

```kotlin
fun deleteGroupDevice(
    gdidList: List<String>,
    callback: QuecCallback<QuecGroupBatchResultBean>
)
```

**参数说明**

| 参数       | 	是否必传 | 说明        |	
|----------|-------|-----------| 
| groupIds | 	是    | 群组id List | 
| callback | 是     | 请求回调      |

> QuecGroupBatchResultBean属性定义同上

**示例代码**

```kotlin
QuecGroupService.deleteGroupDevice(listOf("gid")) {
    if (it.isSuccess) {
        //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 设置群组分享信息

**接口说明**

分享人设置群组分享信息

```kotlin
fun getShareCode(
    groupId: String,
    acceptingExpireAt: Long,
    isSharingAlwaysValid: Boolean,
    sharingExpireAt: Long,
    coverMark: Int,
    callback: QuecCallback<String>
)
```

**参数说明**

| 参数                   | 	是否必传 | 说明                                                                              |	
|----------------------|-------|---------------------------------------------------------------------------------| 
| groupId              | 	是    | 群组Id                                                                            | 
| acceptingExpireAt    | 	是    | 分享二维码种子失效时间 时间戳（毫秒），表示该分享在此时间戳时间内没有使用，会失效                                       | 
| isSharingAlwaysValid | 	是    | 设备是否永久有效                                                                        | 
| sharingExpireAt      | 	是    | 设备使用到期时间 时间戳（毫秒），表示该分享的群组，被分享人可以使用的时间<br/>如果不填，则为终生有效                           | 
| coverMark            | 	是    | 覆盖标志<br/>1 直接覆盖上条有效分享（默认）（覆盖原有的分享码）<br/>2 直接添加，允许多条并存<br/>3 只有分享时间延长了，才允许覆盖上条分享 | 
| callback             | 是     | 请求回调                                                                            |

**示例代码**

```kotlin
QuecGroupService.getShareCode(
    "gid",
    1745214106879,
    false,
    System.currentTimeMillis() + 90000,
    1
) {
    if (it.isSuccess) {
        val data = it.data //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 群组分享人列表

**接口说明**

获取群组分享人列表

```kotlin
fun getSharedLists(
    groupId: String,
    callback: QuecCallback<List<QuecGroupShareInfoBean>>
)
```

**参数说明**

| 参数       | 	是否必传 | 说明   |	
|----------|-------|------| 
| groupId  | 	是    | 群组Id | 
| callback | 是     | 请求回调 |

**QuecGroupShareInfoBean属性定义**

| 字段        | 类型                 | 描述   |
|-----------|--------------------|------|
| shareInfo | QuecGroupShareInfo | 分享信息 |
| userInfo  | QuecGroupUserInfo  | 用户信息 |

**QuecGroupShareInfo属性定义**

| 字段                | 类型     | 描述     |
|-------------------|--------|--------|
| acceptTime        | String | 分享时间   |
| acceptingExpireAt | String | 分享失效时间 |
| coverMark         | int    | 覆盖标志   |
| deleteTime        | String | 删除时间   |
| deviceName        | String | 设备名称   |
| dk                | String | 设备key  |
| ownerUid          | String | 所有者ID  |
| pk                | String | 产品key  |
| shareCode         | String | 分享码    |
| shareId           | String | 分享ID   |
| shareStatus       | int    | 分享状态   |
| shareTime         | String | 分享时间   |
| shareUid          | String | 分享用户ID |
| sharingExpireAt   | String | 分享失效时间 |
| updateTime        | String | 更新时间   |

**示例代码**

```kotlin
QuecGroupService.getSharedLists("gid") {
    if (it.isSuccess) {
        val data = it.data //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 分享人取消分享

**接口说明**

分享人取消分享

```kotlin
fun ownerUnShare(
    shareCode: String,
    callback: QuecCallback<Unit>
)
```

**参数说明**

| 参数        | 	是否必传 | 说明   |	
|-----------|-------|------| 
| shareCode | 	是    | 分享码  | 
| callback  | 是     | 请求回调 |

**示例代码**

```kotlin
QuecGroupService.ownerUnShare("shareCode") {
    if (it.isSuccess) {
        //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 被分享人取消分享

**接口说明**

被分享人取消分享

```kotlin
fun unShared(shareCode: String, callback: QuecCallback<Unit>)
```

**参数说明**

| 参数        | 	是否必传 | 说明   |	
|-----------|-------|------| 
| shareCode | 	是    | 分享码  | 
| callback  | 是     | 请求回调 |

**示例代码**

```kotlin
QuecGroupService.unShared("shareCode") {
    if (it.isSuccess) {
        //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 接受分享

**接口说明**

被分享人接受分享

```kotlin
fun acceptShare(
    shareCode: String,
    callback: QuecCallback<Unit>
)
```

**参数说明**

| 参数        | 	是否必传 | 说明   |	
|-----------|-------|------| 
| shareCode | 	是    | 分享码  | 
| callback  | 是     | 请求回调 |

**示例代码**

```kotlin
QuecGroupService.acceptShare("shareCode") {
    if (it.isSuccess) {
        //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 分享群组的信息

**接口说明**

查询分享群组的信息

```kotlin
fun getShareGroupInfo(shareCode: String, callback: QuecCallback<QuecGroupBasicInfoBean>)
```

**参数说明**

| 参数        | 	是否必传 | 说明   |	
|-----------|-------|------| 
| shareCode | 	是    | 分享码  | 
| callback  | 是     | 请求回调 |

**QuecGroupBasicInfoBean属性定义**

| 字段              | 类型     | 描述                |
|-----------------|--------|-------------------|
| gdid            | String | 群组id              |
| productkey      | String | 产品pk              |
| groupDeviceName | String | 群组名称              |
| onlinestatus    | int    | 群组在离线状态 0-离线 1-在线 |
| logoImage       | String | 群组logo            |
| fid             | String | 家庭id              |
| frid            | String | 房间id              |

**示例代码**

```kotlin
QuecGroupService.getShareGroupInfo("shareCode") {
    if (it.isSuccess) {
        val data = it.data //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

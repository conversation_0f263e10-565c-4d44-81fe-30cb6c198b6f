# __APP蓝牙配网__

## **前提条件：**
下载移联万物App并注册个人账号。

## **操作步骤：**

1、打开移联万物 APP 并登录个人账户，在 App 首页，点击右上角“⊕”按钮。在弹出的窗口中选择“近场发现”。

<a data-fancybox title="img" href="/zh/deviceDevelop/develop/app/Example-app-1.png">
<img src="/zh/deviceDevelop/develop/app/Example-app-1.png" style="width: 30%" /></a>

2、进入“搜索设备”界面并自动搜寻附近的蓝牙设备，搜索到蓝牙设备后会显示在当前界面中。点击已搜索到的蓝牙设备后的“⊕”，进行“网络配置”。

<a data-fancybox title="img" href="/zh/deviceDevelop/develop/app/Example-app-2.png">
<img src="/zh/deviceDevelop/develop/app/Example-app-2.png" style="width: 30%" /></a>

3、在“网络配置”界面输入当前环境下的 Wi-Fi 名称和密码（模组现仅支持 Wi-Fi 2.4 GHz）。

<a data-fancybox title="img" href="/zh/deviceDevelop/develop/app/Example-app-3.png">
<img src="/zh/deviceDevelop/develop/app/Example-app-3.png" style="width: 30%" /></a>

4、配网中，请等待配网完成。

<a data-fancybox title="img" href="/zh/deviceDevelop/develop/app/Example-app-4.png">
<img src="/zh/deviceDevelop/develop/app/Example-app-4.png" style="width: 30%" /></a>

5、配网完成后，成功添加的设备显示在 App 首页“设备列表”中，如下图所示。

<a data-fancybox title="img" href="/zh/deviceDevelop/develop/app/Example-app-5.png">
<img src="/zh/deviceDevelop/develop/app/Example-app-5.png" style="width: 30%" /></a>

此时开发者中心可以看到此设备已经是在线状态。

<a data-fancybox title="img" href="/zh/deviceDevelop/develop/app/Example-app-7.png">
<img src="/zh/deviceDevelop/develop/app/Example-app-7.png" /></a>


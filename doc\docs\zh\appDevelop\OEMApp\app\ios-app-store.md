# 发布iOS应用

## 第一步：在 [App Store Connect](https://appstoreconnect.apple.com/) 创建应用
如果您已经在 App Store Connect 创建过应用，可以跳过此步骤。

### 1. 创建 App
1）登录 App Store Connect，点击 我的 App。

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/appstore/store1.png">![image](/zh/appDevelop/oemapp/appstore/store1.png)</a>
2）点击左上角 + 号，选择 新建 App。

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/appstore/store2.png">![image](/zh/appDevelop/oemapp/appstore/store2.png)</a>
填写应用相应信息。

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/appstore/store3.png">![image](/zh/appDevelop/oemapp/appstore/store3.png)</a>

平台：选择 iOS。

名称：填写您的应用名称。

主要语言：根据实际情况，选择应用的默认语言。

套装 ID：选择之前制作证书时创建的应用 ID，一般与 iOS 包名完全一致（不带尾缀）。

SKU：可与套装 ID 一致，建议填写为应用包名。

3）填写完成后，点击 创建。

### 2. 填写 App 信息

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/appstore/store4.png">![image](/zh/appDevelop/oemapp/appstore/store4.png)</a>

副标题：选填。

类别：能够最贴切地描述此 App 的类别。

内容版权：如果 App 包含、显示或访问第三方内容，应根据上架国家或地区的法律要求，获得相应内容的所有必要版权或使用许可。

年龄分级：App 的年龄分级，用于在 App Store 中实施家长控制。点击 编辑 后，填写分级问卷，自动判定应用分级，所有选项均选择 无。填写后，点击完成。

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/appstore/store5.png">![image](/zh/appDevelop/oemapp/appstore/store5.png)</a>

若应用需要增加其他版本的语言，点击右上角 简体中文 处，勾选需要的语言。

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/appstore/store6.png">![image](/zh/appDevelop/oemapp/appstore/store6.png)</a>

新增语言后，每个语言都需要填写应用信息。

### 3. 设置价格与销售范围
价格：根据实际情况选择销售价格。

销售范围：根据实际情况选择需要上架的地区，一般选择上架全部区域。

App 分发方式：建议选择 公开—在 App Store 上对所有人可见（默认）、在 Apple 校园教务管理中提供批量购买优惠价格。

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/appstore/store7.png">![image](/zh/appDevelop/oemapp/appstore/store7.png)</a>

### 4. 配置应用隐私政策信息
隐私政策 为必填项。在 隐私政策 处点击 编辑，填写 隐私政策网址(URL)，可以填写官网地址。

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/appstore/store8.png">![image](/zh/appDevelop/oemapp/appstore/store8.png)</a>

数据收集类型

自 2020 年 12 月 08 日起，在 App Store 上架、更新应用时，需要披露：应用运行时，会收集和使用哪些由用户产生的数据。

按以下步骤填写 数据收集 项。

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/appstore/store9.png">![image](/zh/appDevelop/oemapp/appstore/store9.png)</a>

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/appstore/store10.png">![image](/zh/appDevelop/oemapp/appstore/store10.png)</a>

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/appstore/store11.png">![image](/zh/appDevelop/oemapp/appstore/store11.png)</a>

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/appstore/store12.png">![image](/zh/appDevelop/oemapp/appstore/store12.png)</a>

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/appstore/store13.png">![image](/zh/appDevelop/oemapp/appstore/store13.png)</a>

如果您的应用中未包含语音功能，不需要选择 音频数据。

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/appstore/store14.png">![image](/zh/appDevelop/oemapp/appstore/store14.png)</a>

设备 ID 并非指 IoT 设备，而是指 iOS 设备的唯一标识。

选择 存储 后，您可看到如下页面。

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/appstore/store15.png">![image](/zh/appDevelop/oemapp/appstore/store15.png)</a>

点击 设置电子邮件地址 等其他数据类型，设置 收集数据用途。

收集用途

设置 收集数据用途。

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/appstore/store16.png">![image](/zh/appDevelop/oemapp/appstore/store16.png)</a>

选择 是，从此应用中手机的电子邮件地址与用户身份关联。

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/appstore/store17.png">![image](/zh/appDevelop/oemapp/appstore/store17.png)</a>

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/appstore/store18.png">![image](/zh/appDevelop/oemapp/appstore/store18.png)</a>

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/appstore/store19.png">![image](/zh/appDevelop/oemapp/appstore/store19.png)</a>

选择 否，不会将电子邮件用于追踪目的。

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/appstore/store20.png">![image](/zh/appDevelop/oemapp/appstore/store20.png)</a>

其他 数据类型 操作方式与以上操作相似，按照以下表格填写。

如果您的应用中未包含语音功能，不需要选择 音频数据。

| 数据类型     | 用途           | 与用户身份关联 | 用于追踪目的 |
| ------------ | -------------- | -------------- | ------------ |
| 电子邮件地址 | 分析、应用功能 | 是             | 否           |
| 电话号码     | 分析、应用功能 | 是             | 否           |
| 精确位置     | 应用功能       | 是             | 否           |
| 粗略位置     | 应用功能       | 是             | 否           |
| 照片或视频   | 应用功能       | 是             | 否           |
| 音频数据     | 应用功能       | 否             | 否           |
| 客户支持     | 应用功能       | 是             | 否           |
| 其他用户内容 | 应用功能       | 是             | 否           |
| 用户 ID      | 分析、应用功能 | 是             | 否           |
| 产品交互     | 分析           | 是             | 否           |
| 崩溃数据     | 应用功能       | 是             | 否           |
| 性能数据     | 应用功能       | 否             | 否           |

填写完成后，您可看到如下页面。
如果您的应用中未包含语音功能，在 不会与您关联的数据 中不会显示 用户内容。

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/appstore/store21.png">![image](/zh/appDevelop/oemapp/appstore/store21.png)</a>

点击 发布 完成所有操作。

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/appstore/store22.png">![image](/zh/appDevelop/oemapp/appstore/store22.png)</a>

## 第二步：填写应用版本信息
### 1. 预览应用截图效果
上传准备好的应用功能预览图，选择您的一款产品进行配网绑定。建议不要使用球泡灯、灯带、单插等产品。这些产品的面板 UI 简单，拒审概率较高。获取不同的功能页面截图，并加以设计。

分别上传 iPhone 6.5 英寸显示屏 和 iPhone 5.5 英寸显示屏。其他尺寸暂无需上传。

必须用 iPhone 系列手机截图。

截图的通知栏不要出现其他应用图标。建议手机开启飞行模式后，进行截图。

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/appstore/store23.png">![image](/zh/appDevelop/oemapp/appstore/store23.png)</a>

预览图效果参考

背景图尺寸为 6.5英寸（1242 * 2688px）

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/appstore/store24.png">![image](/zh/appDevelop/oemapp/appstore/store24.png)</a>

背景图尺寸为 5.5英寸（1242 * 2208px）

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/appstore/store25.png">![image](/zh/appDevelop/oemapp/appstore/store25.png)</a>

### 2. 填写 App 版本信息和审核信息
版本信息

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/appstore/store26.png">![image](/zh/appDevelop/oemapp/appstore/store26.png)</a>

推广文本：简单介绍应用的新功能。在 App Store 中，宣传文本会显示在应用名称下方。

描述：对应用详细的描述与功能介绍。

关键词：用于应用搜索，设置合适的关键词可以增加应用被搜索到的几率，多个关键词之间用英文逗号 (,) 分隔。

技术支持网址：根据实际情况填写。如无技术支持网址，可以填写公司官网。

营销网址：选填，可不填。

版本：填写应用版本号，尽量填写跟移远后台创建的版本号一致。

版权：填写公司版权信息，例如：“©2023 上海移远通信技术股份有限公司”。

若有其他语言，点击右上角进行切换，并填写对应语言的相关信息。

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/appstore/store27.png">![image](/zh/appDevelop/oemapp/appstore/store27.png)</a>

如果应用支持多个语言，每个语言的宣传信息和图片都需要填写一遍。


审核信息

根据实际情况，填写相应的应用审核信息。

登录信息：勾选 需要登录，填写准备好的应用测试账号及登录密码。

要求：注册区域选择 美国，账号中需要绑定设备。此设备需要跟截图及视频拍摄的设备保持一致。建议添加虚拟设备。若添加的设备为真实设备，请在应用审核期间保持设备在线。

联系信息：根据实际填写联系人信息。

备注：填写准备好的应用操作视频链接。

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/appstore/store28.png">![image](/zh/appDevelop/oemapp/appstore/store28.png)</a>

### 3. 配置应用演示视频
视频拍摄要求：

必须要清楚地看到您的设备跟 苹果手机 摆放一起。
演示全流程的审核要点包括：从手机桌面点击您的应用进入开始，设备配网成功后用手机应用控制设备的开关等功能。
以下为演示视频参考示例。

<video controls src="https://quec-cicd-oss.oss-cn-shanghai.aliyuncs.com/resource/appstore.mp4"></video>

建议将视频上传到 YouTube，上传时选择 不公开 模式，表示只有通过网址链接才能访问该视频。

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/appstore/store34.png">![image](/zh/appDevelop/oemapp/appstore/store34.png)</a>

视频上传成功后页面会显示网址链接。此时还未完结，此链接无法正常观看，需要打开新的浏览器检验下。

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/appstore/store35.png">![image](/zh/appDevelop/oemapp/appstore/store35.png)</a>

必须通过验证后，进入可观看页面。此时，在网址栏中获取到的链接才是有效链接。将该链接填入开发者账号备注栏中。

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/appstore/store36.png">![image](/zh/appDevelop/oemapp/appstore/store36.png)</a>

```
注意：
请勿选择含有存储服务功能的 IPC 设备拍摄演示视频，可能会因为含有内购功能而导致审核被驳回。
```
## 第三步：上传应用安装包

### 通过 Transporter 上传
请停止使用 Application Loader 工具上传。因为该工具存在许多未知问题，会导致上传失败，无法解决。请使用苹果新发布的上传工具 Transporter。

使用 Mac 电脑，打开 Transporter 应用并登录。

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/appstore/store29.png">![image](/zh/appDevelop/oemapp/appstore/store29.png)</a>
点击 添加 APP，选择准备好的正式版本的应用安装包，文件后缀为 .ipa。

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/appstore/store30.png">![image](/zh/appDevelop/oemapp/appstore/store30.png)</a>
点击交付。如果长时间上传不成功，请多重试几次。

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/appstore/store31.png">![image](/zh/appDevelop/oemapp/appstore/store31.png)</a>
应用安装包就上传完成后，请耐心等待 15 分钟—1 小时，回到 App Store Connect 界面，在 构建版本 处选择刚才上传的安装包，点击 完成。

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/appstore/store32.png">![image](/zh/appDevelop/oemapp/appstore/store32.png)</a>

## 第四步：提交审核
全部信息填写完成后，点击页面右上角 提交以供审核。

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/appstore/store33.png">![image](/zh/appDevelop/oemapp/appstore/store33.png)</a>
应用审核一般为 1—3 个工作日。审核通过后会自动上架到应用市场，且联系人邮箱会收到审核通过的通知邮件。

如在上架应用时遇到任何问题，可以随时联系移远工作人员寻求帮助。

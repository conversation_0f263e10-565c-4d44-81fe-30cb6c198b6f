# Overview

## **Introduction**

> This tool provides a graphical configuration interface that enables users to easily configure product information and function definitions of Modbus slave devices. The generated configuration files can be directly imported into the TSL model on Developer Center. The tool can also download configuration files for the device, thus greatly reducing the development workload for Modbus devices.

## **Background**

> Numerous Modbus devices are available in the market, and many users have a demand for intelligent transformation of these devices. However, the cost of customized development for the corresponding DTUs is prohibitively high. Hence, there is an urgent need for a DTU that can be compatible with the available standard Modbus protocol devices to reduce the cost and time required for the entire device access process. With the Modbus DTU, developers can graphically configure Modbus meter information function points using the PC tool. This generates a standard TSL model file and a device file without any code development. After importing TSL model file to Developer Center and downloading the device file to DTU, developers can use the DTU to inter-convert Modbus device data and TSL model data. This enables them to collect and control data on Developer Center.  

## **Tool Downloading**

<a href="https://core.acceleronix.io/download?menuCode=DEVL_UTIL&resourceType=C" target="_blank">Download Center</a>



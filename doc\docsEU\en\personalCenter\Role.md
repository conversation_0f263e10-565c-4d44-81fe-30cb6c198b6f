# Permission Management

After becoming an enterprise user, you can use the sub-account feature. You need to create a role for the sub-account and assign accessible menu and data permissions.

## Role Management

After logging in to Developer Center, you can create a custom role  through "**Add a Role**" in "**Personal Center**"->"**Permission Management**"->"**Role Management**".

Once you have added the role,  click "**View Menu Permission**" to select the menu and operation permissions accessible to this role. 

The menu and operation permissions of sub-account will inherit from the role it is bound to.

## Sub-account Management

When creating a sub-account, you need to authorize data access permissions in addition to basic information. You can specify permissions for products, App applications and SaaS applications, or directly authorize access to all data under the account, which can be adjusted as needed.

Subsequently, administrators can reset sub-account passwords or reassign data access permissions.

Note:

- When logging in,  you can use the sub-account "sub-account username@{Enterprise ID}.aiot.quectel.com" , which can be obtained from the sub-account list after creation.

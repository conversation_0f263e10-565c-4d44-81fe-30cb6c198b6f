# 关联邮箱


**接口地址**:`/v2/enduser/enduserapi/relationEmail`


**请求方式**:`POST`


**请求数据类型**:`application/json`


**响应数据类型**:`*/*`


**接口描述**:<p>关联邮箱</p>


**请求参数**:


| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型 | schema |
| -------- | -------- | -------- | -------- | -------- | ------ |
| code     | 验证码   | query    | true     | string   |        |
| email    | 邮箱     | query    | true     | string   |        |


**响应状态**:


| 状态码 | 说明               | schema             |
| ------ | ------------------ | ------------------ |
| 200    | 关联邮箱成功       | 返回注册码响应数据 |
| 5026   | 请输入邮箱         |                    |
| 5039   | 邮箱已注册         |                    |
| 5040   | 邮箱格式不正确     |                    |
| 5046   | 邮箱验证码验证失败 |                    |
| 5064   | 请输入验证码       |                    |


**响应参数**:


| 参数名称 | 参数说明   | 类型           | schema         |
| -------- | ---------- | -------------- | -------------- |
| code     | 响应状态码 | integer(int32) | integer(int32) |
| data     | 响应数据   | object         |                |
| extMsg   | 扩展消息   | string         |                |
| msg      | 响应消息   | string         |                |


**响应示例**:
```javascript
{
	"code": 0,
	"data": {},
	"extMsg": "",
	"msg": ""
}
```

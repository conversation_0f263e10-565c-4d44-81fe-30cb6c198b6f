# 快速接入指引
SaaS系统与开发者中心进行对接集成时，需要创建一个SaaS应用对象，获取访问系统数据所需的AccessKey与AccessSecret。针对SaaS应用可通过API授权、App授权、产品授权进行可访问数据的限制。

## 创建SaaS应用

登录账号后在SaaS开发页面点击创建SaaS按钮。

第一步：输入SaaS应用名称与备注信息，点击确定即完成SaaS应用创建。

第二步：点击SaaS应用卡片进入应用详情页，在右侧信息栏可获取AccessKey与AccessSecret。

## 授权API服务

第三步：在API服务页面下，通过新增服务授权按钮添加服务包关联。（初次使用时需要先完成开通操作）

SaaS应用仅可调用已授权服务包所包含的API接口。

API分类与明细详见OpenAPI列表。

## 授权产品数据权限

第四步：在产品页面下，通过新增产品授权按钮添加产品关联。

SaaS应用仅可访问已授权产品的产品配置数据以及设备数据。

备注：一个产品同一时刻仅可授权到一个SaaS应用。

## 授权App数据权限

第五步：在App页面下，通过新增App授权按钮添加App关联。

SaaS应用仅可访问已授权App的用户数据。

## 创建消息订阅

### 队列管理

- 针对每个SaaS应用，用户可创建一条免费队列，通过免费队列推送的消息将不会被计入收费账单中。
- 当您需要有多个客户端重复接收数据时，可创建收费队列用于推送副本数据，通过收费队列推送的消息将会被统计到收费账单中，在使用前请购买服务订阅消息资源包。
- 当有关联的订阅被启用时，队列也将同步开启。
- 每个用户最多可创建50个队列，30天未使用的消息队列将会被停用，用以回收资源
- 在队列详情中，可以查看队列的消费速率以及堆积情况。

**备注：**

- 队列名称的约束：需要账号内唯一，最大长度64个字符，支持大小写英文字符串(区分大小写)、数字、下划线(_)、中划线(-)、不支持斜杠(/)、空格等其他字符。
- 队列的命名规则：a.{标识id}.{队列名称}

| 队列状态 | 描述                                                         |
| -------- | ------------------------------------------------------------ |
| 未启用   | 代表该队列未有相关订阅使用。                                 |
| 启用     | 代表队列有相关订阅存在，且订阅已启用。 当相关订阅都被删除时，该队列状态变更为未启用。 |
| 停用     | 代表该队列长时间无消费，已经被停用。 当相关订阅被重新开启时，该队列状态变更为启用。 |

## 订阅管理

- 订阅管理决定了SaaS应用会接收到哪些类型的消息，开发者中心支持创建三种数据级别的订阅。
- SaaS应用级订阅：可接收全部授权给该SaaS应用的产品、设备、物模型变更事件以及数据。
- 产品级订阅：仅可接收产品已授权给该SaaS应用，且已启用订阅的设备数据。
- 设备级订阅：仅可接收产品已授权给该SaaS应用，且已启用订阅的设备数据。

| 参数                    | 描述                                                         |
| --------------------------- | ------------------------------------------------------------ |
| 订阅名称                | 输入订阅名称，最大长度128字符， 支持中文、大小写英文字符串(区分大小写)、数字、下划线(_)、中划线(-)、不支持斜杠(/)、空格等其他字符。 |
| 选择产品                | 消息订阅针对具体产品进行订阅                                 |
| 数据级别                | 产品级别：针对上述选择的产品下所有设备数据进行订阅。设备级别：针对下选择的产品下具体设备数据进行订阅。当选择设备级别时，页面可模糊搜索和查询具体设备、 |
| 消息类型（物模型）      | 选择订阅的消息类型，可多选 设备上下线事件、设备和模组状态、设备命令响应数据、物模型属性信息、物模型事件上报-信息、物模型事件上报-告警、物模型事件上报-故障、物模型服务调用日志、设备定位下行信息、设备定位原始信息、设备定位信息、设备信息变更。 具体消息类型数据格式参见 [SaaS应用开发](/saasDevelop/AMQPSubscription/subscription/dataFormatDefinition.md) |
| 消息类型（透传/自定义） | 选择订阅的消息类型，可多选 设备上下线事件、设备和模组状态、设备上行数据、设备下行数据、设备命令响应数据、设备定位下行信息、设备定位原始信息、设备定位信息、设备信息变更。 具体消息类型数据格式参见 [SaaS应用开发](/saasDevelop/AMQPSubscription/subscription/dataFormatDefinition.md) |
| 消息队列                | 选择或创建一个消息队列用来绑定此订阅                         |

订阅创建完成后为未启动状态，需要点击“**启动**”才能使订阅规则生效。

**备注：**已启动的订阅规则不能进行修改和删除，如果要修改订阅规则，需要先点击“**停止**”，在订阅停止后方可进行操作。

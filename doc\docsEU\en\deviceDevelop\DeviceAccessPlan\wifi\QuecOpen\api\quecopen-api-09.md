# DTU-Modbus API
## **API Overview**

|                 Function                  | Description                                                                                                              |
| :---------------------------------------: | :----------------------------------------------------------------------------------------------------------------------- |
|      [Ql_iotMBInit()](#Ql_iotMBInit)      | Initializes Modbus components.                                                                                           |
| [Ql_iotMBCloudRecv()](#Ql_iotMBCloudRecv) | Converts the format of the TSL data issued by Developer Center to Modbus format and sends the data to Modbus sub-device. |
| [Ql_iotMBLocalRecv()](#Ql_iotMBLocalRecv) | Forwards Modbus data received by the serial port to Modbus components and processes the Modbus data.                     |
|    [Ql_iotMBDeinit()](#Ql_iotMBDeinit)    | De-initializes Modbus components.                                                                                        |

## **API Description**

<span id="Ql_iotMBInit">  </span>

## <span style="color:#A52A2A">__Ql_iotMBInit__</span>

This function initializes ModBus components. This function also configures the serial port list used to download configuration files (including device information, QuecThing configuration and serial port configuration; The file is generated by the tool provided by Acceleronix. Contact Acceleronix Technical Support for details), registers callback functions used to send data to the serial port, and initializes Modbus.

__Prototype__

```c
qbool Ql_iotMBInit(quint16_t uartList[],quint32_t uartNum,QIot_MBSend_f sendFunc,QIot_MBInitCb_f initCb);
```

__Parameter__
* __Input Parameter__
	* __quint16_t__     __`uartList`__: List of serial ports used to download configuration files. You should open the corresponding serial ports.
	* __quint32_t__     __`uartNum`__: Number of serial ports used to burn configuration files. You can set it to 0 if the feature of serial port downloading files is not required.
	* __QIot_MBSend_f__  __`sendFunc`__: Callback function for sending data to the serial port. See [QIot_MBSend_f](#QIot_MBSend_f) for details.
	* __QIot_MBInitCb_f__  __`initCb`__: Callback function of initializing Modbus. See [QIot_MBInitCb_f](#QIot_MBInitCb_f) for details.

__Return Value__
* __`True`__: Successful execution
* __`False`__: Failed execution

---

__Example__

```c
quint16_t portList[] = {0,1,2,3,4,5,6,7,8,9};
#define QIOT_MODBUS_PORT_MAXNUM   100
static pointer_t portFd[QIOT_MODBUS_PORT_MAXNUM] = {[0 ...(QIOT_MODBUS_PORT_MAXNUM-1)] = SOCKET_FD_INVALID};

quint32_t i;
for (i = 0; i < sizeof(portList)/sizeof(quint16_t); i++)
{
    portFd[i] = modbusUartInit(portList[i],9600,QIOT_MBUART_DATABITS_8,QIOT_MBUART_PARITY_NONE,QIOT_MBUART_STOPBITS_1);
}
/* Initialize modbus */
Ql_iotMBInit(portList,sizeof(portList)/sizeof(quint16_t),modbusUartSend,modbusInitCb);
```
---
<span id="QIot_MBSend_f">  </span>
### <span style="color:#A52A2A">__QIot_MBSend_f__</span>

This callback function is called when Modbus components need to send data to the serial port. The serial port sending ability should be implemented in this callback function.

__Prototype__

```c
typedef qbool (*QIot_MBSend_f)(quint16_t port, const quint8_t *buf, quint32_t bufLen);
```

__Parameter__
* __Input Parameter__
	* __quint16_t__     __`port`__: Serial port ID.
	* __const quint8_t *__  __`buf`__: Data to be sent.
	* __quint32_t__     __`bufLen`__: Length of data to be sent.

__Return Value__
* __`True`__: Successful execution
* __`False`__: Failed execution

---
<span id="QIot_MBInitCb_f">  </span>
### <span style="color:#A52A2A">__QIot_MBInitCb_f__</span>

After Modbus components are initialized, this callback function is called to receive the device information from the configuration files. This function handles the necessary operations for serial port communication, including sending and receiving data, as well as establishing a connection between the device and Developer Center.

__Prototype__

```c
typedef void (*QIot_MBInitCb_f)(char *pk, char *ps,QIot_MBUartCfg_t *uartInfo[], quint32_t uartNum);
```

__Parameter__
* __Input Parameter__
	* __char *__         __`pk`__: ProductKey generated when you create the product in Developer Center.
	* __char *__         __`ps`__: ProductSecret generated when you create the product in Developer Center.
	* __QIot_MBUartCfg_t *__  __`uartInfo`__: Serial port configuration in the configuration files. See [Ql_iotMBCloudRecv()](#Ql_iotMBCloudRecv) for details.
	* __quint32_t__       __`uartNum`__: Number of serial ports in the configuration files.

##### <span style="color:#A52A2A">__QIot_MBUartCfg_t__</span>

The structure of serial port configurations:

```c
typedef struct
{
    quint16_t port;
    quint32_t baudrate;
    QIot_MBDataBits_e dataBits;
    QIot_MBParity_e parity;
    QIot_MBStopBits_e stopBits;
}QIot_MBUartCfg_t;
```

__Parameter__

|       Type        | Parameter | Description                                                        |
| :---------------: | :-------: | :----------------------------------------------------------------- |
|     quint16_t     |   port    | Serial port ID                                                     |
|     quint32_t     | baudrate  | Baud rate                                                          |
| QIot_MBDataBits_e | dataBits  | Data bit. See [QIot_MBDataBits_e](#QIot_MBDataBits_e) for details. |
|  QIot_MBParity_e  |  parity   | Parity bit. See [QIot_MBParity_e ](#QIot_MBParity_e)for details.   |
| QIot_MBStopBits_e | stopBits  | Stop bit. See [QIot_MBStopBits_e ](#QIot_MBStopBits_e)for details. |

---
<span id="QIot_MBDataBits_e">  </span>

##### <span style="color:#A52A2A">__QIot_MBDataBits_e__</span>

The enumeration of data bits:

```c
typedef enum
{
    QIOT_MBUART_DATABITS_5  = 0,
    QIOT_MBUART_DATABITS_6  = 1,
    QIOT_MBUART_DATABITS_7  = 2,
    QIOT_MBUART_DATABITS_8  = 3,
}QIot_MBDataBits_e;
```

__Parameter__

|       Parameter        | Description |
| :--------------------: | :---------- |
| QIOT_MBUART_DATABITS_5 | 5 bit       |
| QIOT_MBUART_DATABITS_6 | 6 bit       |
| QIOT_MBUART_DATABITS_7 | 7 bit       |
| QIOT_MBUART_DATABITS_8 | 8 bit       |
---
<span id="QIot_MBParity_e">  </span>

##### <span style="color:#A52A2A">__QIot_MBParity_e__</span>

The enumeration of parity bits:

```c
typedef enum 
{
    QIOT_MBUART_PARITY_NONE  = 0,
    QIOT_MBUART_PARITY_EVEN  = 1,
    QIOT_MBUART_PARITY_ODD   = 2,
    QIOT_MBUART_PARITY_MARK  = 3,
    QIOT_MBUART_PARITY_SPACE = 4,
}QIot_MBParity_e;
```

__Parameter__

|        Parameter         | Description |
| :----------------------: | :---------- |
| QIOT_MBUART_PARITY_NONE  | None        |
| QIOT_MBUART_PARITY_EVEN  | Even parity |
|  QIOT_MBUART_PARITY_ODD  | Odd parity  |
| QIOT_MBUART_PARITY_MARK  | Always 1    |
| QIOT_MBUART_PARITY_SPACE | Always 0    |
---
<span id="QIot_MBStopBits_e">  </span>

##### <span style="color:#A52A2A">__QIot_MBStopBits_e__</span>

The enumeration of stops bits:

```c
typedef enum
{
    QIOT_MBUART_STOPBITS_1   = 0,
    QIOT_MBUART_STOPBITS_1_5 = 1,
    QIOT_MBUART_STOPBITS_2   = 2,
}QIot_MBStopBits_e;
```

__Parameter__

|        Parameter         | Description |
| :----------------------: | :---------- |
|  QIOT_MBUART_STOPBITS_1  | 1 bit       |
| QIOT_MBUART_STOPBITS_1_5 | 1.5 bit     |
|  QIOT_MBUART_STOPBITS_2  | 2 bit       |

---
<span id="Ql_iotMBCloudRecv">  </span>
### <span style="color:#A52A2A">__Ql_iotMBCloudRecv__</span>

This function converts the format of the TSL data issued by Developer Center to Modbus format and sends the data to Modbus sub-device.

__Prototype__

```c
qbool Ql_iotMBCloudRecv(const void *ttlvHead);
```

__Parameter__
* __Input Parameter__
	* __const void *__  __`ttlvHead`__: TTLV data table.

__Return Value__
* __`True`__: Successful execution
* __`False`__: Failed execution

---

__Example__

```c
void *ttlvHead = NULL;
qbool ret = Ql_iotMBCloudRecv(ttlvHead);
Ql_iotTtlvFree(&ttlvHead);
```
---



<span id="Ql_iotMBLocalRecv">  </span>
### <span style="color:#A52A2A">__Ql_iotMBLocalRecv__</span>

This function forwards Modbus data received by the serial port to Modbus components and processes the Modbus data.

__Prototype__

```c
qbool Ql_iotMBLocalRecv(quint16_t port,quint8_t *data,quint32_t len);
```

__Parameter__
* __Input Parameter__
	* __quint16_t__  __`port`__: Serial port ID.
	* __quint8_t *__  __`data`__: Modbus data received by the serial port.
	* __quint32_t__  __`len`__: Length of Modbus data received by the serial port.

__Return Value__
* __`True`__: Successful execution
* __`False`__: Failed execution

---

__Example__

```c
int uartId;
quint8_t buf[1024];
qint32_t bufLen = read(sockFd, buf, sizeof(buf));
Ql_iotMBLocalRecv(uartId, buf, bufLen);
```
---

<span id="Ql_iotMBDeinit">  </span>
### <span style="color:#A52A2A">__Ql_iotMBDeinit__</span>

This function de-initializes Modbus components.

__Prototype__

```c
qbool Ql_iotMBDeinit(void);
```

__Parameter__

None

__Return Value__
* __`True`__: Successful execution
* __`False`__: Failed execution

---

__Example__

```c
qbool ret = Ql_iotMBDeinit();
```
---

# 设备控制

## 功能概述

设备控制主要提供设备控制, 设备通道连接与断开

支持以下通道连接

1. 云端通道(ws)
2. wifi局域网通道
3. ble通道

当设备上云的话, ws通道会默认建立

当以默认方式连接设备时, 连接的优先级为:  云端 > wifi > ble

> QuecDeviceModel属性定义参考[设备管理](设备管理.md)

## 设备控制

### 初始化设备

根据设备 ID 去初始化设备控制类。

**接口说明**

>您需要调用获取设备列表接口后, 初始化设备才能成功. 如果在业务层需要使用QuecDeviceClient设备对象, 同样需要调用初始化接口.

```objc
/// Returns the QuecDevice instance. If the current user does not have this device, a value of nil is returned.
/// @param deviceId deviceModel.deviceId
+ (nullable instancetype)deviceWithId:(NSString *)deviceId;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- |  
| deviceId |	是| QuecDeviceModel中属性, 自定义格式为:pk@dk. 例: @"p00001@AABBCCDD0001" | 

**示例代码**

```objc
QuecDeviceClient *device = [QuecDeviceClient deviceWithId:deviceModel.deviceId];
device.delegate = self;
/// Default auto mode
[device connect];
```

### 设备代理监听

>实现 QuecDeviceClientDelegate 代理协议后，您可以在设备状态更变的回调中进行处理刷新设备在离线状态、页面UI显示等.

**示例代码**

```objc
/// Device information updates, such as the name and online status.
/// @param device The device instance.
- (void)deviceInfoUpdate:(QuecDeviceClient *)device;

/// Device online status updates
/// @param device device instance.
/// @param onlineState  device channel online state （0: all offline, 1: websocket online, 2 : wifi online, 3: wifi + websocket online, 4: ble online, 5: websocket + ble online, 6: ble + wifi online , 7: wifi + ble + ws online)
- (void)device:(QuecDeviceClient *)device onlineUpdate:(NSUInteger)onlineState;

/// Device connecting state updates
/// @param device device instance.
/// @param connectingState  device channel online state （0: all offline, 1: websocket online, 2 : wifi online, 3: wifi + websocket online, 4: ble online, 5: websocket + ble online, 6: ble + wifi online , 7: wifi + ble + ws online)
- (void)device:(QuecDeviceClient *)device connectingStateUpdate:(NSUInteger)connectingState;

/// Indicates whether device is removed.
/// @param device device instance.
- (void)deviceRemoved:(QuecDeviceClient *)device;

/// The DP updates.
/// @param device device instance.
/// @param dps  command dictionary.
- (void)device:(QuecDeviceClient *)device dpsUpdate:(QuecIotDataPointsModel *)dps;
```

**QuecIotDataPointsModel属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| decodeType       | QuecIotDataDecodeType             | 默认使用QuecIotDataDecodeTypeDecodeDPSCode    |
| type  | QuecIotChannelType            | 通道类型 |
| action | QuecIotDataPointAction | 动作类型    |
| pk | NSString | 产品pk    |
| dk | NSString |  设备dk   |
| dps | NSArray<QuecIotDataPoint*> * | dps数据   |
| originData | id | 原始通道数据    |
| deviceId | NSString | 设备id    |
| transparentData | NSData | 透传数据    |
| resetData | NSDictionary | 设备重置数据    |

**QuecIotDataDecodeType枚举定义**

```objc
typedef NS_ENUM(NSUInteger, QuecIotDataDecodeType) {
    QuecIotDataDecodeTypeDecodeDPSCode,
    QuecIotDataDecodeTypeDecodeDPSId,
    QuecIotDataDecodeTypeDecodeNONE,
};
```

### 设备通道连接与断开

**接口说明**

用于设备通道连接或断开, 需要先获取到当前QuecDeviceClient对象. 

```objc
- (void)connect;
- (void)connectWithMode:(QuecIotChannelMode)mode;
- (void)disconnect;
- (void)disconnectWithType:(QuecIotChannelType)type;
```

**QuecIotChannelMode枚举定义**

```objc
typedef NS_ENUM(NSUInteger, QuecIotChannelMode)
{
    QuecIotChannelModeAuto = 0 ,
    QuecIotChannelModeWS,
    QuecIotChannelModeWifi,
    QuecIotChannelModeBle,
};
```

**QuecIotChannelType枚举定义**

```objc
typedef NS_ENUM(NSUInteger, QuecIotChannelType)
{
    QuecIotChannelTypeUnknown = 0,
    QuecIotChannelTypeWS = 1,
    QuecIotChannelTypeWifi,
    QuecIotChannelTypeBle,
};
```

### 设备删除及通道状态重置

**接口说明**

>用于设备删除并且需要清空通道状态, 需要先获取到当前QuecDeviceClient对象. 以下移除接口包括云端关系解绑和设备通道状态清空, 删除设备建议使用以下方法. QuecDeviceService中解绑接口仅解除云端绑定关系

#### 删除设备

**接口说明**

```objc
- (void)remove:(nullable QuecVoidBlock)success failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- |  
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**示例代码**

```objc
QuecDeviceClient *device = [QuecDeviceClient deviceWithId:@"device id"];
[device remove:^{
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```
#### 删除纯蓝牙设备

**接口说明**

```objc
- (void)removeBtWithIsInit:(BOOL)isInit
                     random:(nullable NSString *)random
          resetCredentials:(nullable NSString *)resetCredentials
                    success:(nullable QuecVoidBlock)success
                    failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- |  
| isInit |	否|是否重置设备, 默认False	| 
| random |	否| 设备返回随机标识, isInit为True时必填	| 
| resetCredentials |否| 设备返回重置凭证, isInit为True时必填	| 
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**示例代码**

```objc
QuecDeviceClient *device = [QuecDeviceClient deviceWithId:@"device id"];
[device removeBtWithIsInit:NO random:@"" resetCredentials:@"" success:^{
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

#### 批量删除设备

**接口说明**

```objc
+ (void)batchRemoveWithFid:(nullable NSString *)fid
                     isInit:(BOOL)isInit
                 deviceList:(NSArray<QuecDeviceModel *> *)deviceList
                    success:(nullable QuecVoidBlock)success
                   failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- |  
| fid |	否| 家居模式下传入, 当前家庭id	| 
| isInit |	否|是否重置设备, 默认False	| 
| deviceList |是| 待删除设备QuecDeviceModel数组	| 
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	|

**示例代码**

```objc
QuecDeviceModel *deviceModel1 = QuecDeviceModel.new;
QuecDeviceModel *deviceModel2 = QuecDeviceModel.new;
[QuecDeviceClient batchRemoveWithFid:@"" isInit:NO deviceList:@[deviceModel1, deviceModel2] success:^{
    /// Next Action
} failure:^(NSError *error) {
    NSLog(@"check error: %@", error);
}];
```

### 设备数据下发

**接口说明**

>通过组装dps数据, 通过以下接口下发给设备

```objc
- (void)writeDps:(NSArray<QuecIotDataPoint*> *)dps
         success:(nullable QuecVoidBlock)success
         failure:(nullable QuecErrorBlock)failure;

- (void)writeDps:(NSArray<QuecIotDataPoint*> *)dps
            mode:(QuecIotChannelMode)mode
         success:(nullable QuecVoidBlock)success
         failure:(nullable QuecErrorBlock)failure;

- (void)writeDps:(NSArray<QuecIotDataPoint*> *)dps
       extraData:(nullable QuecIotChannelExtraData *)extraData
         success:(nullable QuecVoidBlock)success
         failure:(nullable QuecErrorBlock)failure;

- (void)writeDps:(NSArray<QuecIotDataPoint*> *)dps
       extraData:(nullable QuecIotChannelExtraData *)extraData
            mode:(QuecIotChannelMode)mode
         success:(nullable QuecVoidBlock)success
         failure:(nullable QuecErrorBlock)failure;
```

**参数说明**

**QuecIotDataPoint属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| Id       | int             | 功能id    |
| dataType  | QuecIotDataPointDataType            | 数据类型 |
| code | NSString | 标识符    |
| value | id | 根据数据类型对应值    |
| stringValue | NSString | float&double类型会将高精度值写入该字段    |
| min | NSString | 最小值 (发生接受不需要处理)    |
| max | NSString | 最大值 (发生接受不需要处理)    |

**QuecIotChannelExtraData属性定义**

| 字段        | 类型                 | 描述                                                |
|-----------|--------------------|---------------------------------------------------|
| cacheTime       | long             | 指令缓存时间，ws通道有效                                     |
| msgId  | long long            | 消息Id，ws通道有效                                       |
| isCover | BOOL | 是否覆盖                                              |
| isCache | BOOL | 是否缓存 default NO  (根据cacheTime 判断)                 |
| sendTTLV | BOOL | 使用TTLV 发生 default: true  (仅支持属性读/写, 带有cacheTime ) |
| productKey | NSString | 设备PK (用于面板给子设备发送消息)                               |
| deviceKey | NSString | 设备DK (用于面板给子设备发送消息)                               |
| transparentData | NSData | 透传数据                                              |

**示例代码**

```objc
QuecDeviceClient *device = [QuecDeviceClient deviceWithId:@"device id"];
QuecIotDataPoint *dp = QuecIotDataPoint.new;
dp.value = @(3);
dp.dataType = QuecIotDataPointDataTypeINT;
dp.Id = 2; /// tsl id
[device writeDps:@[dp] mode:QuecIotChannelModeAuto success:^{
    /// Next Action
} failure:^(NSError * _Nonnull error) {
    NSLog(@"check error: %@", error);
}];
```

### 设备数据读取

**接口说明**

>通过组装dps数据, 查询设备信息

```objc
- (void)readDps:(NSArray<QuecIotDataPoint*> *)dps
        success:(nullable QuecVoidBlock)success
        failure:(nullable QuecErrorBlock)failure;

- (void)readDps:(NSArray<QuecIotDataPoint*> *)dps
           mode:(QuecIotChannelMode)mode
        success:(nullable QuecVoidBlock)success
        failure:(nullable QuecErrorBlock)failure;

- (void)readDps:(NSArray<QuecIotDataPoint*> *)dps
      extraData:(nullable QuecIotChannelExtraData *)extraData
        success:(nullable QuecVoidBlock)success
        failure:(nullable QuecErrorBlock)failure;

- (void)readDps:(NSArray<QuecIotDataPoint*> *)dps
      extraData:(nullable QuecIotChannelExtraData *)extraData
           mode:(QuecIotChannelMode)mode
        success:(nullable QuecVoidBlock)success
        failure:(nullable QuecErrorBlock)failure;
```

**参数说明**

> QuecIotDataPoint QuecIotChannelExtraData QuecIotChannelMode 定义同上

查询后通过QuecDeviceClient协议方法dps更新UI

```objc
- (void)device:(QuecDeviceClient *)device dpsUpdate:(QuecIotDataPointsModel *)dps;
```

**示例代码**

```objc
QuecDeviceClient *device = [QuecDeviceClient deviceWithId:@"device id"];
QuecIotDataPoint *dp = QuecIotDataPoint.new;
dp.dataType = QuecIotDataPointDataTypeINT;
dp.Id = 2; /// tsl id
[device readDps:@[dp] mode:QuecIotChannelModeAuto success:^{
    /// Next Action
} failure:^(NSError * _Nonnull error) {
    NSLog(@"check error: %@", error);
}];
```


### 发送透传数据

**接口说明**

发送透传数据, 注意, 该透传数据只能通过蓝牙通道发送

```objc
- (void)writeTransparent:(NSData *)transparentData
                 success:(nullable QuecVoidBlock)success
                 failure:(nullable QuecErrorBlock)failure;
```

**参数说明**
|参数|	是否必传|说明|	
| --- | --- | --- |  
| transparentData |	是| 二进制数据 | 
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

```objc
QuecDeviceClient *device = [QuecDeviceClient deviceWithId:@"device id"];
NSData *sendData = [[NSData alloc] initWithBase64EncodedString:@"1234567890" options:0];
[device writeTransparent:sendData success:^{
    /// Next Action
} failure:^(NSError * _Nonnull error) {
    NSLog(@"check error: %@", error);
}];
```

## 设备批量控制

### 通过云端批量控制

**接口说明**

设备批量控制, 通过云端控制

```objc
- (void)sendDataToDevicesByHttpWithData:(NSString *)data
                              deviceList:(NSArray<QuecBatchControlDeviceModel *> *)deviceList
                                    type:(NSInteger)type
                              extraData:(nullable QuecBatchControlExtraModel *)extraData
                                 success:(void(^)(QuecBatchControlModel *model))success
                                 failure:(QuecErrorBlock)failure;
```

**参数说明**

|参数|	是否必传|说明|	
| --- | --- | --- |  
| data | 是 | 遵循tsl格式的json string |
| deviceList | 是 | 设备列表 |
| type | 是 | 类型 1：透传 2：属性 3：服务 |
| extraData | 否 | dataFormat 数据类型 1：Hex 2：Text（当type为透传时，需要指定 dataFormat）,dataFormat在extraData中传递 |
| success |	否|接口请求成功回调	| 
| failure |	否|接口请求失败回调	| 

**QuecBatchControlDeviceModel属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| productKey  | NSString  | 产品pk |
| deviceKey  | NSString  | 设备dk |
| gatewayDeviceKey  | NSString  | 网关设备dk |
| gatewayProductKey  | NSString  | 网关设备pk |

**QuecBatchControlExtraModel属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| cacheTime  | NSInteger  | 缓存时间，单位为秒，缓存时间范围 1-7776000 秒，启用缓存时必须设置缓存时间 （非必填） |
| dataFormat  | NSInteger  | 数据类型 1：Hex 2：Text （当 type 为透传时，需要指定 dataFormat） （非必填） |
| isCache  | NSInteger  | 是否启用缓存 1：启用 2：不启用，默认不启用 （非必填） |
| isCover  | NSInteger  | 是否覆盖之前发送的相同的命令 1：覆盖 2：不覆盖，默认不覆盖，启用缓存时此参数有效 （非必填） |
| qos  | NSInteger  | QoS等级设置，参数值范围 0、1，默认为1 （非必填） |

**QuecBatchControlModel属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| successList  | NSArray<QuecBatchControlResultModel *> *  | 成功列表 |
| failureList  | NSArray<QuecBatchControlResultModel *> *  | 失败列表 |

**QuecBatchControlResultModel属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| data  | QuecBatchControlDeviceModel | 控制设备信息 |
| ticket  | NSString  | ticket (仅成功列表有此属性) |
| msg  | NSString  | 错误提示（仅失败列表有此属性） |

**QuecBatchControlDeviceModel属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| productKey  | NSString | 产品pk |
| deviceKey  | NSString  | 设备dk |
| gatewayDeviceKey  | NSString  | 网关dk |
| gatewayProductKey  | NSString  | 网关pk |

**示例代码**

```objc
QuecBatchControlExtraModel *extraModel = QuecBatchControlExtraModel.new;
extraModel.dataFormat = 1;
[QuecDeviceControlService.sharedInstance sendDataToDevicesByHttpWithData:@"[{\"read_write_float\":\"4.4\"}]"
                                                           deviceList:deviceList
                                                                 type:2
                                                            extraData:extraModel
                                                              success:^(QuecBatchControlModel * _Nonnull model) {
        /// Next Action
    } failure:^(NSError *error) {
        NSLog(@"check error: %@", error);
    }];
```

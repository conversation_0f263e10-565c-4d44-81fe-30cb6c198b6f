# 账号详情

本文介绍了如何对用户的信息进行管理

## 功能概述

在该模块中，您可以调用对象 QuecUserService 获取当前用户的所有信息及相关的登录注册方法。QuecUserModel数据模型如下表所示：

| 参数            | 类型     | 说明     |
|---------------|--------|--------|
| nikeName      | String | 昵称     |
| phone         | String | 手机号    |
| registerTime  | String | 注册时间   |
| sex           | String | 性别     |
| timezone      | String | 时区     |
| uid           | String | 用户ID   |
| wchartId      | String | 微信ID   |
| wchartName    | String | 微信昵称   |
| address       | String | 地址     |
| email         | String | 邮箱     |
| headimg       | String | 头像     |
| lang          | String | 语言     |
| lastLoginTime | String | 上次登录时间 |
| nationality   | String | 国籍     |

```kotlin
QuecUserService
```

### 设置登录状态失效回调

**接口说明**

当登录状态失效时, 该回调会触发

```kotlin
fun setTokenInvalidCallBack(callBack: QuecCallback<Unit>)
```

**参数说明**

| 参数     | 是否必传 | 说明     |
| -------- | -------- | -------- |
| callback | 是       | 设置回调 |

**示例代码**

```kotlin
QuecUserService.setTokenInvalidCallBack {
    // 登录失效处理
}
```

### 修改手机号

**接口说明**

用于修改用户登录手机号

```kotlin
fun updatePhone(
    newPhone: String,
    newInternationalCode: String,
    newPhoneCode: String,
    oldPhone: String,
    oldInternationalCode: String,
    oldPhoneCode: String,
    callback: QuecCallback<Unit>
)
```

**参数说明**

| 参数                   | 	是否必传 | 说明           |
|----------------------|-------|--------------|
| newPhone             | 	是    | 新手机号码        | 
| newInternationalCode | 	是    | 新手机号码国际代码    | 
| newPhoneCode         | 	是    | 新手机号码接收到的验证码 | 
| oldPhone             | 	是    | 原手机号码        | 
| oldInternationalCode | 	是    | 原手机号码国际代码    | 
| oldPhoneCode         | 	是    | 原手机号码接收到的验证码 | 
| callback             | 是     | 请求回调         |

**示例代码**

```kotlin
QuecUserService.updatePhone("13000000001", "86", "code1", "13000000000", "86", "code2") {
    if (it.isSuccess) {
        //更新成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 修改用户头像

**接口说明**

传入图片地址修改用户头像

```kotlin
fun updateUserIcon(imagePath: String, callback: QuecCallback<Unit>)
```

**参数说明**

| 参数        | 	是否必传 | 说明   |
|-----------|-------|------|
| imagePath | 	是    | 图片地址 |
| callback  | 是     | 请求回调 |

**示例代码**

```kotlin
QuecUserService.updateUserIcon("https://xxx.com/xxx.jpg") {
    if (it.isSuccess) {
        //更新成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 修改用户昵称

**接口说明**

用于修改用户昵称

```kotlin
fun updateUserNickName(nikeName: String, callback: QuecCallback<Unit>)
```

**参数说明**

| 参数       | 	是否必传 | 说明   |
|----------|-------|------|
| nikeName | 	是    | 昵称   |  
| callback | 是     | 请求回调 |

**示例代码**

```kotlin
QuecUserService.updateUserNickName("nickName") {
    if (it.isSuccess) {
        //更新成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 修改用户性别

**接口说明**

用于修改用户性别

```kotlin
fun updateUserSex(sex: Int, callback: QuecCallback<Unit>)
```

**参数说明**

| 参数       | 	是否必传 | 说明              |
|----------|-------|-----------------|
| sex      | 	是    | 性别 0：男 1：女 2：保密 |  
| callback | 是     | 请求回调            |

**示例代码**

```kotlin
QuecUserService.updateUserSex(1) {
    if (it.isSuccess) {
        //更新成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 修改密码

**接口说明**

用于用户修改登录密码

```kotlin
fun updatePassword(newPassword: String, oldPassword: String, callback: QuecCallback<Unit>)
```

**参数说明**

| 参数          | 	是否必传 | 说明   |
|-------------|-------|------|
| newPassword | 	是    | 新密码  | 
| oldPassword | 	是    | 原密码  |  
| callback    | 是     | 请求回调 |

**示例代码**

```kotlin
QuecUserService.updatePassword("newPwd", "oldPwd") {
    if (it.isSuccess) {
        //更新成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 删除用户

**接口说明**

用于当前用户删除

```kotlin
fun deleteUser(type: Int?, callback: QuecCallback<Unit>)
```

**参数说明**

| 参数       | 	是否必传 | 说明                               |
|----------|-------|----------------------------------|
| type     | 	否    | 删除类型：1- 立即删除 2- 7天后删除，默认为 7 天后删除 |
| callback | 是     | 请求回调                             |

**示例代码**

```kotlin
QuecUserService.deleteUser(1) {
    if (it.isSuccess) {
        //删除成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 用户信息读取

**接口说明**

用于获取用户信息

```kotlin
fun getUserInfo(callback: QuecCallback<QuecUserModel>)
```

**参数说明**

| 参数       | 	是否必传 | 说明   |
|----------|-------|------|
| callback | 是     | 请求回调 |

**示例代码**

```kotlin
QuecUserService.getUserInfo {
    if (it.isSuccess) {
        val value = it.data //获取成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 退出登录

**接口说明**

用户退出登录

```kotlin
fun logout(callback: QuecCallback<Unit>)
```

**参数说明**

| 参数       | 	是否必传 | 说明   |
|----------|-------|------|
| callback | 是     | 请求回调 |

**示例代码**

```kotlin
QuecUserService.logout {
    if (it.isSuccess) {
        //登出成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 是否登录

**接口说明**

检查用户是否登录

```kotlin
fun checkUserLoginState(callback: QuecCallback<Boolean>)
```

**参数说明**

| 参数       | 	是否必传 | 说明   |
|----------|-------|------|
| callback | 是     | 请求回调 |

**示例代码**

```kotlin
QuecUserService.checkUserLoginState {
    if (it.isSuccess) {
        val value = it.data //查询成功, true: 已登录, false: 未登录
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

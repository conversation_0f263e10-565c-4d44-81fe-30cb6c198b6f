/**
 * 子模块同步脚本
 * 将子模块中的文档文件复制到主项目的content目录中，以便CMS可以访问
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 项目根目录
const projectRoot = path.resolve(__dirname, '..');

// 源目录（子模块）和目标目录（主项目）的映射
const syncMappings = [
  {
    source: 'doc/docs/zh/introduction',
    target: 'content/introduction',
    description: '产品介绍文档'
  },
  {
    source: 'doc/docs/zh/quickStart',
    target: 'content/quickstart',
    description: '快速开始文档'
  },
  {
    source: 'doc/docs/zh/appDevelop/AndroidGuide',
    target: 'content/android-guide',
    description: 'Android开发指南'
  },
  {
    source: 'doc/docs/zh/appDevelop/iOSGuide',
    target: 'content/ios-guide',
    description: 'iOS开发指南'
  }
];

function ensureDir(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
    console.log(`创建目录: ${dirPath}`);
  }
}

/**
 * 从文件名生成标题
 */
function generateTitleFromFilename(filename) {
  // 移除.md扩展名
  let title = filename.replace('.md', '');

  // 处理特殊文件名
  const titleMap = {
    'page-01': '平台概述',
    'page-02': '产品优势',
    'page-03': '名词解释',
    'page-05': '支持的地域',
    'CreatProduct': '创建产品',
    'DeviceConnect': '设备连接',
    'DeviceDebug': '设备调试',
    'Overview': '概述',
    'register': '注册账号',
    '快速集成': 'SDK快速集成',
    '用户账号': '用户账号管理',
    '设备管理': '设备管理',
    '家庭管理': '家庭管理',
    '消息管理': '消息管理',
    '场景': '场景管理',
    '自动化': '自动化',
    '群组': '设备群组',
    '设备控制': '设备控制',
    '设备添加': '设备添加'
  };

  return titleMap[title] || title;
}

/**
 * 从内容中提取第一个标题
 */
function extractTitleFromContent(content) {
  const lines = content.split('\n');
  for (const line of lines) {
    const trimmed = line.trim();
    if (trimmed.startsWith('# ')) {
      return trimmed.substring(2).trim();
    }
  }
  return null;
}

/**
 * 检查内容是否已有frontmatter
 */
function hasFrontmatter(content) {
  return content.trim().startsWith('---');
}

/**
 * 添加frontmatter到内容
 */
function addFrontmatter(content, filename) {
  if (hasFrontmatter(content)) {
    return content; // 已有frontmatter，直接返回
  }

  // 从内容中提取标题，如果没有则从文件名生成
  let title = extractTitleFromContent(content) || generateTitleFromFilename(filename);

  // 生成描述
  let description = `${title}相关文档`;

  // 构建frontmatter
  const frontmatter = `---
title: "${title}"
description: "${description}"
---

`;

  return frontmatter + content;
}

/**
 * 复制文件并添加frontmatter
 */
function copyFile(source, target) {
  try {
    const content = fs.readFileSync(source, 'utf8');
    const filename = path.basename(source);
    const processedContent = addFrontmatter(content, filename);
    fs.writeFileSync(target, processedContent, 'utf8');
    return true;
  } catch (error) {
    console.error(`复制文件失败: ${source} -> ${target}`, error.message);
    return false;
  }
}

/**
 * 同步单个映射
 */
function syncMapping(mapping) {
  const sourcePath = path.resolve(projectRoot, mapping.source);
  const targetPath = path.resolve(projectRoot, mapping.target);

  console.log(`\n同步 ${mapping.description}`);
  console.log(`源目录: ${sourcePath}`);
  console.log(`目标目录: ${targetPath}`);
  if (!fs.existsSync(sourcePath)) {
    console.log(`源目录不存在，跳过: ${sourcePath}`);
    return false;
  }

  ensureDir(targetPath);

  const files = fs.readdirSync(sourcePath).filter(file => file.endsWith('.md'));
  
  if (files.length === 0) {
    console.log(` 源目录中没有找到.md文件: ${sourcePath}`);
    return false;
  }

  let successCount = 0;
  let totalCount = files.length;

  // 复制每个文件
  files.forEach(file => {
    const sourceFile = path.join(sourcePath, file);
    const targetFile = path.join(targetPath, file);
    
    if (copyFile(sourceFile, targetFile)) {
      // console.log(` ${file}`);
      successCount++;
    } else {
      console.log(`   ❌ ${file}`);
    }
  });

  // console.log(`完成: ${successCount}/${totalCount} 个文件`);
  return successCount > 0;
}

/**
 * 主同步函数
 */
function syncAll() {
  console.log('开始同步子模块文档...\n');

  let totalSuccess = 0;
  let totalMappings = syncMappings.length;

  syncMappings.forEach(mapping => {
    if (syncMapping(mapping)) {
      totalSuccess++;
    }
  });

  console.log(`\n同步完成: ${totalSuccess}/${totalMappings} 个映射成功`);
  
  if (totalSuccess === totalMappings) {
    console.log('所有文档同步成功！');
    return true;
  } else {
    console.log('部分文档同步失败，请检查日志');
    return false;
  }
}

/**
 * 监听文件变化并自动同步
 */
function watchAndSync() {
  console.log('开始监听子模块文件变化...');
  
  syncMappings.forEach(mapping => {
    const sourcePath = path.resolve(projectRoot, mapping.source);
    
    if (fs.existsSync(sourcePath)) {
      fs.watch(sourcePath, { recursive: true }, (eventType, filename) => {
        if (filename && filename.endsWith('.md')) {
          console.log(`文件变化: ${filename}`);
          syncMapping(mapping);
        }
      });
      console.log(`   监听: ${sourcePath}`);
    }
  });
}

/**
 * 清理目标目录
 */
function clean() {
  console.log('清理目标目录...');
  
  syncMappings.forEach(mapping => {
    const targetPath = path.resolve(projectRoot, mapping.target);
    if (fs.existsSync(targetPath)) {
      fs.rmSync(targetPath, { recursive: true, force: true });
      console.log(`删除: ${targetPath}`);
    }
  });
  
  console.log('清理完成');
}

// 命令行参数处理
const args = process.argv.slice(2);
const command = args[0] || 'sync';

switch (command) {
  case 'sync':
    syncAll();
    break;
  case 'watch':
    syncAll();
    watchAndSync();
    break;
  case 'clean':
    clean();
    break;
  default:
    console.log(`
使用方法:
  node scripts/sync-submodule.js [command]

示例:
  npm run sync-docs   
  npm run sync-docs:watch
  npm run sync-docs:clean
`);
}

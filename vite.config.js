import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

// https://vite.dev/config/
export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  // 确保子模块内容被包含在开发服务器中
  server: {
    fs: {
      // 允许访问项目根目录之外的文件（包括子模块）
      allow: ['..', '.']
    }
  },
  // 确保子模块内容在构建时被正确处理
  assetsInclude: ['**/*.md'],
  // 优化依赖，包含子模块路径
  optimizeDeps: {
    exclude: ['doc/**']
  }
})

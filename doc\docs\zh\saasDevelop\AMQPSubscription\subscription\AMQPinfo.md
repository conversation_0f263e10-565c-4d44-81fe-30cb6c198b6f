# AMQP消息订阅概述

消息订阅是开发者中心基于AMQP标准协议实现的数据订阅功能。AMQP（Advanced Message Queuing Protocol）即高级消息队列协议。用户在创建AccessKey、配置和启动消息订阅规则后，平台会按照配置信息将已订阅的消息类型转发至指定消息队列中，然后用户使用客户端连接队列即可获取订阅的消息数据。

AMQP消息订阅数据流转如下图：

<a data-fancybox title="img" href="/zh/saasDevelop/image2022-3-17_17-17-12.png">![img](/zh/saasDevelop/image2022-3-17_17-17-12.png)</a>

AMQP服务端订阅优势：

● 支持多消费组。同一个账号，可以在开发环境下使用消息队列A订阅产品A，同时在正式环境下消息队列B订阅产品B。 如果多个不同消费队列同时订阅产品B，则不同消费队列可同时收到来自设备B的相同信息。<br />
● 方便排查问题。支持查看客户端状态、查看堆积和消费速率。<br />
● 线性扩展。在消费者能力足够，即客户端机器足够的情况下，可轻松线性扩展推送能力。<br />
● 消息堆积队列。设备实时消息直接推送，推送失败时进入会堆积队列，不会丢失设备消息。即使消费者的客户端宕机，或因消费能力不足堆积了消息，当消费端恢复后，未接收的设备消息也可以继续正常消费。

使用AMQP服务端订阅，需先在开发者中心控制台创建消息队列和配置订阅规则，请参见[SaaS管理](/saasDevelop/SaaSManagement/SaaSManage)，然后开发AMQP客户端，接入开发者中心订阅消息，请参见[AMQP客户端接入说明](/saasDevelop/AMQPSubscription/subscription/AMQPtoC)。


## **AMQP订阅使用限制**

| 限制项           | 描述                                                                                                                                                                                                                                                                                                                                                                   |
| :--------------- | :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 认证超时         | 建立连接之后，需要立刻发送认证请求。如果15秒内没有认证成功，服务器将主动关闭连接。                                                                                                                                                                                                                                                                                     |
| 数据超时         | 服务端与开发者中心建立连接时，需传入心跳时间（AMQP协议参数idle-timeout），取值范围为30~60秒。 如果超过心跳时间，连接上无任何帧通信，平台将关闭连接。 建立连接后，用户服务端需在心跳时间内发送PING包来维持连接。若没有在心跳时间内发送PING包，平台将断开连接。注：使用IoT平台提供的SDK，建立连接后，无需发送PING包维持连接。SDK存在保活心跳，只需保证主进程不退出即可。 |
| 失败推送重试策略 | 由于消费客户端离线、消息消费慢等原因，消息不能实时消费，而进入堆积队列。消费客户端重新上线并恢复稳定消费能力后，平台重试推送堆积消息。如果客户端对重试推送的消息消费失败，可能导致堆积队列阻塞。按大约一分钟间隔，平台向客户端再次重试推送。                                                                                                                           |
| 消息保存条数     | 每个消费队列最多缓存500MB数据                                                                                                                                                                                                                                                                                                                                          |
| 消息保存时长     | 6小时                                                                                                                                                                                                                                                                                                                                                                  |
| 消息队列个数限制 | 一个账号最多创建50个消息队列，超过30天未使用则自动停用                                                                                                                                                                                                                                                                                                                 |
| 消息订阅个数限制 | 一个账号最多创建100个消息订阅                                                                                                                                                                                                                                                                                                                                          |


**<i style="color: #d20030">免责声明：</i>**<br />
移远开发者中心将根据需要，不定期发布或迭代更新版本。移远开发者中心的发布或更新将导致用户AMQP客户端断开连接。移远将尽一切合理方式提前通知，但为保证用户的连续使用，**请用户确保使用具备断线重连机制的AMQP客户端接入移远开发者中心。** 移远开发者中心无法保证或承诺IoT平台服务的连续性。因用户未使用具备断线重连机制的AMQP客户端而产生的任何形式（直接、间接或其它）的损失或损害，不论是合同、侵权或其它，移远开发者中心均不承担任何责任和义务。

# 手机号码

本文介绍了如何使用手机号码注册账号, 登录等功能

## 功能概述

```kotlin
QuecUserService
```

### 查询手机号是否已注册

**接口说明**

用于校验手机号是否已注册

```kotlin
fun queryPhoneIsRegister(
    phone: String, internationalCode: String?, callback: QuecCallback<Boolean>
)
```

**参数说明**

| 参数                | 	是否必传 | 说明         |	
|-------------------|-------|------------| 
| phone             | 	是    | 手机号码       | 
| internationalCode | 	否    | 国家码, 例如 86 | 
| callback          | 是     | 请求回调       |

**示例代码**

```kotlin
QuecUserService.queryPhoneIsRegister("13000000000", "86") {
    if (it.isSuccess) {
        val value = it.data //true表示已注册，false表示未注册
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 手机号密码登录

**接口说明**

用于用户手机号+密码登录

```kotlin
fun loginByPhone(
    phone: String, password: String, internationalCode: String?, callback: QuecCallback<Unit>
)
```

**参数说明**

| 参数                | 	是否必传 | 说明         |
|-------------------|-------|------------|
| phone             | 	是    | 手机号码       |
| password          | 是     | 密码         |
| internationalCode | 否     | 国际代码，默认为国内 |
| callback          | 是     | 请求回调       |

**示例代码**

```kotlin
QuecUserService.loginByPhone("13000000000", "password", "86") {
    if (it.isSuccess) {
        //登录成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 手机号短信验证码登录

**接口说明**

用于手机号短信验证码登录，需先获取短信验证码

```kotlin
fun loginWithMobile(
    mobile: String, code: String, internationalCode: String?, callback: QuecCallback<Unit>
)
```

**参数说明**

| 参数                | 	是否必传 | 说明          |
|-------------------|-------|-------------|
| mobile            | 	是    | 手机号	        | 
| code              | 	是    | 验证码	        | 
| internationalCode | 	否    | 国际代码，默认为国内	 | 
| callback          | 是     | 请求回调        |

**示例代码**

```kotlin
QuecUserService.loginWithMobile("13000000000", "code", "86") {
    if (it.isSuccess) {
        //登录成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 手机号密码注册

**接口说明**

用于手机号密码注册，需先获取手机验证码

```kotlin
fun registerByPhone(
    phone: String,
    code: String,
    password: String,
    internationalCode: String,
    callback: QuecCallback<Unit>
)
```

**参数说明**

| 参数                | 	是否必传 | 说明         |
|-------------------|-------|------------|
| phone             | 	是    | 手机号        |
| code              | 	是    | 验证码        |
| password          | 	是    | 密码         |
| internationalCode | 	否    | 国际代码，默认为国内 |
| callback          | 是     | 请求回调       |

**示例代码**

```kotlin
QuecUserService.registerByPhone("13000000000", "code", "password", "86") {
    if (it.isSuccess) {
        //注册成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 发送手机验证码

**接口说明**

用于密码重置、登录、注册、注销、增值服务接受人 发送手机验证码

```kotlin
fun sendVerifyCodeByPhone(
    phone: String, internationalCode: String?, type: Int, callback: QuecCallback<Unit>
)
```

**参数说明**

| 参数                | 	是否必传 | 说明                                                   |
|-------------------|-------|------------------------------------------------------|
| phone             | 	是    | 手机号                                                  |
| internationalCode | 是     | 国际代码                                                 |
| type              | 是     | 验证码类型 1: 注册验证码, 2: 密码重置验证码, 3: 登录验证码 4: 注销 5:增值服务接受人 |
| callback          | 是     | 请求回调                                                 |

**示例代码**

```kotlin
QuecUserService.sendVerifyCodeByPhone("13000000000", "86", 1) {
    if (it.isSuccess) {
        //发送成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 验证国际手机号格式

**接口说明**

用于验证国际手机号格式

```kotlin
fun validateInternationalPhone(
    phone: String, internationalCode: String, callback: QuecCallback<Unit>
)
```

**参数说明**

| 参数                | 	是否必传 | 说明   |
|-------------------|-------|------|
| phone             | 	是    | 手机号  | 
| internationalCode | 是     | 国际代码 | 
| callback          | 是     | 请求回调 |

**示例代码**

```kotlin
QuecUserService.validateInternationalPhone("13000000000", "86") {
    if (it.isSuccess) {
        //验证成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 验证短信验证码

**接口说明**

用于验证短信验证码，需先获取短信验证码

```kotlin
fun validateSmsCode(
    phone: String,
    smsCode: String,
    internationalCode: String?,
    type: Int?,
    callback: QuecCallback<Unit>
)
```

**参数说明**

| 参数                | 	是否必传 | 说明                         |
|-------------------|-------|----------------------------|
| phone             | 	是    | 手机号                        | 
| smsCode           | 	是    | 验证码                        | 
| internationalCode | 	否    | 国际代码                       | 
| type              | 否     | 验证码验证后是否失效，1：失效 2：不失效，默认 1 | 
| callback          | 是     | 请求回调                       |

**示例代码**

```kotlin
QuecUserService.validateSmsCode("13000000000", "code", "86", 1) {
    if (it.isSuccess) {
        //验证成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 手机号重置密码

**接口说明**

用户通过手机号+验证码重置密码

```kotlin
fun resetPasswordByPhone(
    phone: String,
    code: String,
    internationalCode: String?,
    password: String?,
    callback: QuecCallback<Unit>
)
```

**参数说明**

| 参数                | 	是否必传 | 说明                         |
|-------------------|-------|----------------------------|
| phone             | 	否    | 手机号码	                      |
| code              | 	是    | 验证码	                       |
| internationalCode | 	否    | 国际代码，配合手机号码使用，默认为国内	       |
| password          | 	否    | 用户重置的密码，如果不输入默认为 12345678	 |
| callback          | 是     | 请求回调                       |

**示例代码**

```kotlin
QuecUserService.resetPasswordByPhone("13000000000", "code", "86", "newPwd") {
    if (it.isSuccess) {
        //重置成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

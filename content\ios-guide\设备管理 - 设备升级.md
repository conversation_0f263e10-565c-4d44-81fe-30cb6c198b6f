---
title: "设备升级SDK: QuecOTAUpgradeKit"
description: "设备升级SDK: QuecOTAUpgradeKit相关文档"
---

# 设备升级SDK: QuecOTAUpgradeKit

## 功能概述

通过此SDK, 可以实现设备升级功能(通过云升级,或者通过蓝牙升级)

```objc
#import <QuecOTAUpgradeKit/QuecOTAUpgradeKit.h>
/// 初始化
[QuecOTAManager sharedInstance]
```
> **注意**
>
> 为保证Http通道升级设备状态同步, 获取到设备列表后轮询调用以下Api检查一次设备升级状态

```objc
 /// deviceModelList: 设备列表页
for (QuecDeviceModel *device in deviceModelList) {
    [QuecOTAManager.sharedInstance checkDeviceHttpOtaWithDeviceModel:device];
}
```

## 设备升级

设备升级的流程为:

1. 查询设备升级计划
2. 设置事件监听器
3. 开始设备升级
4. 通过事件回调获取升级进度和升级结果
5. 展示升级结果, 移除事件监听器

SDK会根据设备的在线状态来确定是通过云OTA还是蓝牙OTA:

1. 设备云在线时, 通过云OTA
2. 设备云不在线时, 通过蓝牙OTA

### 查询单个设备的升级计划

**接口说明**

查询单个设备的升级计划, 当设备有升级计划时, 返回升级计划信息, 否则返回null

```objc
- (void)checkVersionWithProductKey:(NSString *)productKey
                          deviceKey:(NSString *)deviceKey
                         planBlock:(void (^)(QuecOtaPlanInfoModel * planInfo))planBlock;
```

**参数说明**

| 参数       | 是否必传 | 说明           |
|----------|------|--------------|
| productKey       | 是    | 设备productKey |
| deviceKey       | 是    | 设备deviceKey  |
| planBlock |	否|接口回调	| 

**示例代码**

```objc
[QuecOTAManager.sharedInstance checkVersionWithProductKey:@"pk"
                                                    deviceKey:@"dk"
                                                    planBlock:^(QuecOtaPlanInfoModel * _Nonnull planInfo) {
    /// Next Action
}];
```

**QuecOtaPlanInfoModel属性定义**

| 字段                 | 类型              | 描述           |
|--------------------|-----------------|--------------|
| productKey         | NSString          | 产品productKey |
| deviceKey          | NSString          | 设备deviceKey  |
| productIcon        | NSString          | 产品图片         |
| deviceName        | NSString          | 设备名称         |
| planId             | long long          | 升级计划ID       |
| planName           | NSString          | 升级计划名称       |
| planStartTime         | long long | 升级计划开始时间，时间戳，精确到毫秒       |
| planEndTime | long long          | 升级计划结束时间，时间戳，精确到毫秒       |
| userConfirmStatus | NSInteger          | 用户确认升级状态，0-未确认，1-马上升级       |
| userConfirmStatusDesc | int          | 用户确认升级状态描述      |
| deviceStatus         | NSInteger          | 设备升级状态，0-未升级，1-升级中，2-升级成功，3-升级失败         |
| deviceStatusDesc | int          | 用户确认升级状态描述      |
| upgradeFinishTips | int          | 升级结果确认状态，0-已确认，1-未确认；无此确认需求的可以忽略      |
| upgradeProgress | float          | 设备升级进度，取值范围：[0,1]；0-未升级，1-升级成功      |
| upgradeVersionInfo | NSString          | 用户确认升级文案      |
| versionInfo | NSString          | 待升级（正在升级）组件升级所需版本包的备注；早期版本中无升级文案，使用此备注作为升级文案      |
| comVerList | NSArray<QuecOtaPlanComVerInfo *> *          | 升级组件列表      |

**QuecOtaPlanComVerInfo属性定义**

| 字段          | 类型            | 描述                  |
|-------------|---------------|---------------------|
| sort        | int           | 组件排序                |
| comNo       | NSString        | 组件编号                |
| comType     | int           | 组件类型, 0: 模组 ,1: MCU |
| dataType     | int           | 升级方式，0：整包升级，1：差分升级 |
| cver        | NSString        | 当前版本号               |
| tver        | NSString        | 目标版本号               |
| versionInfo | NSString        | 组件升级所需版本包的备注              |
| versionMatch | BOOL        | 组件是否符合升级，true：是，false，否；恒true，仅返回可升级组件列表              |
| battery | NSString        | 升级要求的最低电量              |
| signal | int        | 升级要求的最弱信号              |
| space | long long        | 升级要求的磁盘空间，单位：B（升级所需要最大的固件的文件大小）              |
| downloadStatus | int        | 组件下载固件状态：0-未下载，1-下载中，2-下载成功，3-下载失败              |
| downloadProgress | NSString        | 下载进度              |
| upgradeStatus | NSString        | 组件升级状态：0-未升级，1-升级中，2-升级成功，3-升级失败              |
| fileList    | NSArray<QuecOtaPlanFileInfo *> *| 组件升级所需固件信息              |

**QuecOtaPlanFileInfo属性定义**

| 字段         | 类型     | 描述          |
|------------|--------|-------------|
| updIndex   | int    | 升级文件序号      |
| fileName   | NSString | 升级文件名       |
| filePath   | NSString | 文件下载地址      |
| fileSize   | long long   | 升级文件大小      |
| fileSha256 | NSString | 升级文件sha256值 |
| fileMd5 | NSString | md5校验码 |
| fileCrc | NSString | crc校验码 |

### 批量查询设备的升级计划

**接口说明**

```objc
- (void)checkListVersionWithDeviceList:(NSArray *)deviceList
                         planListBlock:(void (^)(NSArray<QuecOtaPlanInfoModel *> * planInfos))planListBlock;
```

**参数说明**

| 参数       | 是否必传 | 说明  |
|----------|------|-----|
| deviceList     | 是    | 设备列表|
| planListBlock | 是    | 请求回调 |

**示例代码**

```objc
[QuecOTAManager.sharedInstance checkListVersionWithDeviceList:@[@{@"pk" : @"your pk", @"dk" : @"your dk"}] planListBlock:^(NSArray<QuecOtaPlanInfoModel *> * _Nonnull planInfos) {
    /// Next Action
}];
```

### 查询账号下所有设备的升级计划

**接口说明**

```objc
- (void)checkAllVersionWithPage:(NSInteger)page
                        pageSize:(NSInteger)pageSize
                  planListBlock:(void (^)(NSArray<QuecOtaPlanInfoModel *> * planInfos))planListBlock;
```

**参数说明**

| 参数       | 是否必传 | 说明   |
|----------|------|------|
| page     | 是    | 分页 |
| pageSize     | 是    | 分页大小 |
| planListBlock | 是    | 请求回调 |

**示例代码**

```objc
[QuecOTAManager.sharedInstance checkAllVersionWithPage:1
                                                  pageSize:10
                                             planListBlock:^(NSArray<QuecOtaPlanInfoModel *> * _Nonnull planInfos) {
    /// Next Action
}];
```

### 获取设备版本信息

**接口说明**

```objc
- (void)queryCurrentVersionWithProductKey:(NSString *)productKey
                                deviceKey:(NSString *)deviceKey
                                   result:(void (^)(NSArray<QuecBleVersionInfo *> * versionInfos))result;
```

**参数说明**

| 参数       | 是否必传 | 说明           |
|----------|------|--------------|
| productKey       | 是    | 设备productKey |
| deviceKey       | 是    | 设备deviceKey  |
| result | 是    | 请求回调         |

**示例代码**

```objc
[QuecOTAManager.sharedInstance queryCurrentVersionWithProductKey:@"your pk"
                                                           deviceKey:@"your dk"
                                                              result:^(NSArray<QuecBleVersionInfo *> * _Nonnull versionInfos) {
    /// Next Action
}];
```

**QuecBleVersionInfo属性定义**

| 字段         | 类型     | 描述          |
|------------|--------|-------------|
| componentName   | NSString    | 组件名称      |
| version   | NSString | 组件版本       |
| componentType   | int |  组件类型 0：模组，1: MCU       |

### 开始设备升级

**接口说明**

指定设备开始ota升级, 在调用此方法时, 会自动确认升级方式

```objc
- (void)startOtaWithPlanInfoModel:(QuecOtaPlanInfoModel *)planInfoModel;
```

**参数说明**

| 参数   | 是否必传 | 说明   |
|------|------|------|
| planInfoModel | 是    | 升级信息 |

**示例代码**

```objc
//查询升级计划获取到的信息
QuecOtaPlanInfoModel *planInfo = QuecOtaPlanInfoModel.new;
[QuecOTAManager.sharedInstance startOtaWithPlanInfoModel:planInfo];
```

### 停止设备升级

**接口说明**

> 注意
>
> 只有蓝牙通道ota升级时, 可以停止, 如果是云ota时, 升级流程无法停止, 设备会继续升级, 直到结束

指定设备停止ota升级

```objc
- (void)stopOtaWithPlanInfoModel:(QuecOtaPlanInfoModel *)planInfoModel;
```

**参数说明**

| 参数   | 是否必传 | 说明   |
|------|------|------|
| planInfoModel | 是    | 升级信息 |

**示例代码**

```objc
//查询升级计划获取到的信息
QuecOtaPlanInfoModel *planInfo = QuecOtaPlanInfoModel.new;
[QuecOTAManager.sharedInstance stopOtaWithPlanInfoModel:planInfo];
```

### 添加升级状态监听器

**接口说明**

添加升级状态监听器, 通过该监听器回调, 能获取到设备的升级状态和进度

```objc

typedef void (^QuecOtaStateBlock)(QuecOTAStateModel *stateModel);

/// add listener
- (void)addStateListener:(id)listener state:(QuecOtaStateBlock)state;
```

**参数说明**

| 参数       | 是否必传 | 说明  |
|----------|------|-----|
| listener | 是    | 监听器 |
| state | 是    | OTA升级信息 |

**示例代码**

```objc
/// add listener
[QuecOTAManager.sharedInstance addStateListener:self state:^(QuecOTAStateModel * _Nonnull stateModel) {
    /// Next Action
}];
```

**QuecOTAStateModel属性定义**

| 字段          | 类型     | 描述                                         |
|-------------|--------|--------------------------------------------|
| planId      | NSString   | 升级计划ID                                     |
| state       | QuecOTAState   | 升级状态: QuecOTAState                         |
| pk          | String | 设备productKey                               |
| dk          | String | 设备deviceKey                                |
| progress    | double | 升级进度 (0.0 ~ 100.0)                         |
| channelMode | QuecOTAChannelMode   | 升级通道类型: QuecOTAChannelMode                 |
| userConfirmStatus    | int   | 用户确认升级状态，0-未确认，1-马上升级 |

**QuecOTAState枚举定义**

```objc
typedef NS_ENUM(int, QuecOTAState) {
    QuecOTAUpgradeEmpty = 0, // 无升级计划
    QuecOTAUpgrading,        // 升级中
    QuecOTAUpgradeSuccess,   // 升级成功
    QuecOTAUpgradeFailure,   // 升级失败
    QuecOTAUpgradeExpired,   // 升级过期
};
```

**QuecOTAChannelMode枚举定义**

```objc
typedef NS_ENUM(int, QuecOTAChannelMode) {
    QuecOTAChannelUnknown = 0, // 无可用通道
    QuecOTAChannelCloud,        // 云升级
    QuecOTAChannelBle,        // 蓝牙升级
};
```

### 移除升级状态监听器

**接口说明**

移除升级状态监听器, 请在完成ota升级后及时移除监听器, 避免内存泄漏

```objc
/// remove listener
- (void)removeStateListener:(id)listener;
```

**参数说明**

| 参数       | 是否必传 | 说明  |
|----------|------|-----|
| listener | 是    | 监听器 |

**示例代码**

```objc
/// remove listener
[QuecOTAManager.sharedInstance removeStateListener:self];
```

### 移除类中所有的缓存

**接口说明**

移除类中所有的缓存, 在不再使用ota资源时调用

```objc
- (void)releaseAllOta;
```

**示例代码**

```objc
[QuecOTAManager.sharedInstance releaseAllOta];
```

### 获取指定设备的OTA升级进度和状态

**接口说明**

获取指定设备的OTA升级进度和状态, 为空时表示此设备无状态

```objc
- (QuecOTAStateModel *)getOtaStateWithProductKey:(NSString *)productKey deviceKey:(NSString *)deviceKey;
```

**参数说明**

| 参数 | 是否必传 | 说明           |
|----|------|--------------|
| productKey | 是    | 设备productKey |
| deviceKey | 是    | 设备deviceKey  |

返回值为升级状态和进度对象

**示例代码**

```objc
QuecOTAStateModel *stateModel = [QuecOTAManager.sharedInstance getOtaStateWithProductKey:@"your pk" deviceKey:@"your dk"];
```

### 获取设备的升级方式

**接口说明**

在开始设备升级之前调用, 可获取当前开始升级时, 将选择的升级方式

```objc
- (QuecOTAChannelMode)getDeviceUpgradeChannelWithProductKey:(NSString *)productKey deviceKey:(NSString *)deviceKey;
```

**参数说明**

| 参数 | 是否必传 | 说明           |
|----|------|--------------|
| productKey | 是    | 设备productKey |
| deviceKey | 是    | 设备deviceKey  |

返回值为升级方式枚举

**示例代码**

```objc
QuecOTAChannelMode mode = [QuecOTAManager.sharedInstance getDeviceUpgradeChannelWithProductKey:@"your pk" deviceKey:@"your dk"];
```

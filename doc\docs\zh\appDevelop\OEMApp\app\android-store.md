# 发布Android应用（国内版）

#### 点击[腾讯开放平台](https://open.tencent.com) > “管理中心”，点击右上角“登录”按钮进入腾讯应用开放平台

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/androidstore/store1.png">![image](/zh/appDevelop/oemapp/androidstore/store1.png)</a>

#### 点击“创建应用”按钮创建一个Android应用

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/androidstore/store2.png">![image](/zh/appDevelop/oemapp/androidstore/store2.png)</a>

#### 核对APP ID 和 APP KEY，点击第一步，完善基础信息，填写资料

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/androidstore/store3.png">![image](/zh/appDevelop/oemapp/androidstore/store3.png)</a>
<a data-fancybox title="img" href="/zh/appDevelop/oemapp/androidstore/store4.png">![image](/zh/appDevelop/oemapp/androidstore/store4.png)</a>

#### 填写应用的“基本信息”

* 应用名称：APP名称
* 应用类型：软件
* 应用子分类：请根据应用类型勾选
* 应用提供方：公司名称，需与开发者资质中的公司名称一致
* 应用研发方：请您填写与APP内隐私协议主体一致的研发方名称
* 应用简介：需填写60至500字的应用简介
* 一句话简介：需填写5至15字的一句话简介。
* 年龄分级：根据实际情况选择，建议选择“18周岁+”
  
<a data-fancybox title="img" href="/zh/appDevelop/oemapp/androidstore/store5.png">![image](/zh/appDevelop/oemapp/androidstore/store5.png)</a>

#### 上传“图标素材”

* 应用图标：按照腾讯要求上传APP图标
* 应用截图：可用自家APP直接截图上传，也可按照尺寸上传自己制作好的APP市场截图
* 介绍视频：可不填写
  
<a data-fancybox title="img" href="/zh/appDevelop/oemapp/androidstore/store6.png">![image](/zh/appDevelop/oemapp/androidstore/store6.png)</a>

#### 勾选上传“适配信息”

* 支持屏幕大小：选择上架应用支持的屏幕大小，建议选择“全部”
* 支持语言：选择应用支持的语言，建议选择“中文”
* 支持IPv6：选择应用支持的网络，建议选择“不支持”
* 设备信息：选择应用支持的设备类型，建议选择“Phone”
  
<a data-fancybox title="img" href="/zh/appDevelop/oemapp/androidstore/store7.png">![image](/zh/appDevelop/oemapp/androidstore/store7.png)</a>

#### 上传“版权证明”

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/androidstore/store8.png">![image](/zh/appDevelop/oemapp/androidstore/store8.png)</a>

**版权证明：**

1. 上传软著证书【注】腾讯开放平台必须上传软著证书才可通过市场审核,如果软著做了变更，请第一时间在应用宝平台重新上传，避免因为旧软著失效被判定资质造假 导致的应用下架或账号被封等问题。

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/androidstore/store9.png">![image](/zh/appDevelop/oemapp/androidstore/store9.png)</a>

2. 上传安全评估后台通过截图

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/androidstore/store10.png">![image](/zh/appDevelop/oemapp/androidstore/store10.png)</a>

3. ICP备案证明请以截图方式上传至应用版权证明处

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/androidstore/store11.png">![image](/zh/appDevelop/oemapp/androidstore/store11.png)</a>

4. 上传《人力资源服务许可证》：若您有后期有在APP内运营招聘相关内容的计划，需要申请此证或者有此证的资质授权，否则会导致APP被应用宝限制外显。

#### 上传“隐私限制”

1. “隐私权限”：隐私权限截图展示（需包含首页弹窗+常驻入口页面），图片大小2M以内，支持jpg、png格式，可安装正式包后,截图下对应页面上传

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/androidstore/store12.png">![image](/zh/appDevelop/oemapp/androidstore/store12.png)</a>

隐私权限截图示范图：

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/androidstore/store13.png">![image](/zh/appDevelop/oemapp/androidstore/store13.png)</a>

2. “隐私政策”：打开隐私政策网址，复制黏贴隐私政策打开隐私政策网址；打开链接，复制黏贴隐私政策内容

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/androidstore/store14.png">![image](/zh/appDevelop/oemapp/androidstore/store14.png)</a>

注：1）请勿直接提交隐私政策链接 2）腾讯开放平台（应用宝）若已因隐私原因被拒，必须要升级版本号。若版本不变，应用宝不会重新做隐私检测，依旧会因之前的原因被拒。

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/androidstore/store15.png">![image](/zh/appDevelop/oemapp/androidstore/store15.png)</a>

#### 上传安全评估报告

1. 上传《互联网信息服务安全评估》及安全评估后台通过截图，页面较多，可多张图拼到一张图上然后上传
2. 上传安全承诺书，承诺书填写好后再盖章拍照上传，安全承诺书页面较多，可多张图拼到一张图上然后上传

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/androidstore/store16.png">![image](/zh/appDevelop/oemapp/androidstore/store16.png)</a>

#### 完成以上信息填写后，点击右上角“保存”

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/androidstore/store17.png">![image](/zh/appDevelop/oemapp/androidstore/store17.png)</a>

#### 申请上架

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/androidstore/store18.png">![image](/zh/appDevelop/oemapp/androidstore/store18.png)</a>

1. 版本特性说明：可以参考Android版本更新日志，编辑版本特性说明；注意：特性说明是给普通用户看的，文案要通俗易懂一些
2. 正式包：上传安装包，APP后台分别下载32位包，64位包进行上传
3. 是否登录：应用内涉及账号登录能力，建议选择“是”，并提供可以正常登录APP的账号和密码；注意：不要提交管理员账号
4. 是否涉及支付：根据应用类型选择对应的选项，建议选择“含支付”
5. 发布类型：可选择审核通过后立即发布或者是定时发布。
6. 勾选“允许平台为我的应用提供最终协议”

#### 提交审核1~3天后可再次进入“管理中心”进行查看

若应用状态为“已上线”则表示该APP已在应用宝成功上线

<a data-fancybox title="img" href="/zh/appDevelop/oemapp/androidstore/store19.png">![image](/zh/appDevelop/oemapp/androidstore/store19.png)</a>

提示:如果审核未通过，请参照审核未通过提示进行修改，或联系腾讯开放平台客服进行沟通。

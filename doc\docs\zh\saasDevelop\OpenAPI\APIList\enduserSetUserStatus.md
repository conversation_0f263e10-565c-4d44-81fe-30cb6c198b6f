# 设置用户状态


**接口地址**:`/v2/enduser/enterpriseapi/setUserStatus`


**请求方式**:`GET`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`

**接口描述**:<p>设置用户状态</p>


**请求参数**:


| 参数名称 | 参数说明                 | 请求类型 | 是否必须 | 数据类型       | schema |
| -------- | ------------------------ | -------- | -------- | -------------- | ------ |
| status   | 用户状态：0 禁用  1 正常 | query    | true     | integer(int32) |        |
| uid      | 用户ID                   | query    | true     | string         |        |


**响应状态**:


| 状态码 | 说明             | schema             |
| ------ | ---------------- | ------------------ |
| 200    | 设置用户状态成功 | 返回注册码响应数据 |
| 5036   | 秘钥验证失败     |                    |
| 5055   | 请输入秘钥       |                    |


**响应参数**:


| 参数名称 | 参数说明   | 类型           | schema         |
| -------- | ---------- | -------------- | -------------- |
| code     | 响应状态码 | integer(int32) | integer(int32) |
| data     | 响应数据   | object         |                |
| extMsg   | 扩展消息   | string         |                |
| msg      | 响应消息   | string         |                |


**响应示例**:
```javascript
{
	"code": 0,
	"data": {},
	"extMsg": "",
	"msg": ""
}
```

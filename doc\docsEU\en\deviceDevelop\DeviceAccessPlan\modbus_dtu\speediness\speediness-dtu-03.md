# Operations on Device

## __1. Burn the Firmware__

<span style="color:#999AAA">Note: Because the burning program of each DTU-Modbus module is different, please contact Acceleronix Technical Support for the specific burning methods.</span>

## __2. Burn Configuration File to DTU Device__

<a data-fancybox title="img" href="/en/deviceDevelop/develop/DTU_Modbus/Example-27.png">![img](/en/deviceDevelop/develop/DTU_Modbus/Example-27.png)</a>

1) Click "__Download__"  > "__Serial Port Download__"  on the tool bar. <br>
2) Click "__Import a File__",  and select the generated *xxxxxx_xxxxxxxxxx_local.bin* in the pop-up window.   <br>
3) From the pop-up window, fill in the baud rate, port number or other configurations, and click "__Open the serial port__". <span style="color:#999AAA">Note: You can find the corresponding burning serial port by referring to the PIN number on the development board or the schematic diagram.</span><br>
4) Click "**Start Download**" and restart the hardware device. The download progress will be displayed in the text box. When the download is successful, a pop-up window saying "Download Success" will appear.<br>


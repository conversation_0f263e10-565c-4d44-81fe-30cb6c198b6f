# 修改家庭成员名称


**接口地址**:`/v2/family/enduserapi/setFamilyMemberName`


**请求方式**:`PUT`


**请求数据类型**:`application/json`


**响应数据类型**:`*/*`


**接口描述**:<p>修改家庭成员名称</p>


**请求参数**:


| 参数名称   | 参数说明       | 请求类型 | 是否必须 | 数据类型 | schema |
| ---------- | -------------- | -------- | -------- | -------- | ------ |
| fid        | 家庭ID         | query    | true     | string   |        |
| memberName | 成员名称       | query    | true     | string   |        |
| memberUid  | 家庭成员用户ID | query    | true     | string   |        |


**响应状态**:


| 状态码 | 说明                          | schema             |
| ------ | ----------------------------- | ------------------ |
| 200    | 修改家庭成员名称成功          | 返回注册码响应数据 |
| 5628   | 未查询到用户信息              |                    |
| 5636   | 请输入家庭ID                  |                    |
| 5638   | 普通成员没有权限              |                    |
| 5648   | 请输入成员名称                |                    |
| 5656   | 请输入家庭成员id              |                    |
| 6049   | 权限不足,当前用户是普通成员   |                    |
| 6053   | 权限不足,被修改者不在该家庭下 |                    |


**响应参数**:


| 参数名称 | 参数说明   | 类型           | schema         |
| -------- | ---------- | -------------- | -------------- |
| code     | 响应状态码 | integer(int32) | integer(int32) |
| data     | 响应数据   | object         |                |
| extMsg   | 扩展消息   | string         |                |
| msg      | 响应消息   | string         |                |


**响应示例**:
```javascript
{
	"code": 0,
	"data": {},
	"extMsg": "",
	"msg": ""
}
```

---
title: "设备数据统计"
description: "设备数据统计相关文档"
---

# 设备数据统计

## 功能概述

设备数据统计管理, 主要获取设备相关的统计信息

```kotlin
QuecDeviceStatisticsService
```

## 数据统计

### 设备历史轨迹

**接口说明**

获取设备历史轨迹

```kotlin
fun getLocationHistory(
    productKey: String,
    deviceKey: String,
    startTimestamp: Long,
    endTimestamp: Long,
    gatewayDk: String?,
    gatewayPk: String?,
    locateTypes: String?,
    callback: QuecCallback<List<QuecLocationHistoryModel>>
)
```

**参数说明**

| 参数             | 	是否必传 | 说明                                                                                                                            |	
|----------------|-------|-------------------------------------------------------------------------------------------------------------------------------|  
| productKey     | 是     | 产品key                                                                                                                         |
| deviceKey      | 是     | 设备key                                                                                                                         |
| startTimestamp | 是     | 开始时间（毫秒时间戳）                                                                                                                   |
| endTimestamp   | 是     | 结束时间（毫秒时间戳）                                                                                                                   |
| gatewayDk      | 否     | 网关设备的 Device Key                                                                                                              |
| gatewayPk      | 否     | 网关设备的 Product Key                                                                                                             |
| locateTypes    | 否     | 定位类型（默认查询所有类型的定位），查询多种定位时使用英文逗号分隔 GNSS-全球导航卫星系统 GPS-美国导航定位系统 GL-俄罗斯格洛纳导航定位系统 GA-欧盟伽利略卫星导航系统 BD/PQ-中国导航定位系统 LBS-基于通信运营商的基站定位系统 |
| callback       | 是     | 请求回调                                                                                                                          |

**QuecLocationHistoryModel属性定义**

| 字段           | 类型     | 描述                        |
|--------------|--------|---------------------------|
| bdLat        | Double | 设备纬度。BD09                 |
| bdLng        | Double | 设备经度。BD09                 |
| deviceKey    | String | 设备dk                      |
| gcjLat       | Double | 设备纬度。GCJ02                |
| gcjLng       | Double | 设备经度。GCJ02                |
| hdop         | Double | 水平精度因子。0.5-99.99          |
| locateId     | String | 轨迹ID                      |
| locateType   | int    | 定位方式（0：GNSS 1：LBS 2：手动标点） |
| locationTime | String | 定位时间                      |
| productKey   | String | 设备pk                      |
| satellites   | int    | 当前卫星数                     |
| tsLocateTime | int    | 最新定位时间（时间戳）               |
| wgsLat       | Double | 设备纬度。WGS84                |
| wgsLng       | Double | 设备经度。WGS84                |

**示例代码**

```kotlin
QuecDeviceStatisticsService.getLocationHistory(
    "pk",
    "dk",
    System.currentTimeMillis() - 90000,
    System.currentTimeMillis(),
    null,
    null,
    "GNSS"
) {
    if (it.isSuccess) {
        val data = it.data //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 设备属性图表列表

**接口说明**

获取设备属性图表列表

```kotlin
fun getPropertyChartList(
    productKey: String,
    deviceKey: String,
    startTimestamp: Long,
    endTimestamp: Long,
    attributeCode: String,
    gatewayDk: String?,
    gatewayPk: String?,
    countType: Int,
    timeGranularity: Int,
    callback: QuecCallback<List<QuecPropertyChartListModel>>
)
```

**参数说明**

| 参数              | 	是否必传 | 说明                                |	
|-----------------|-------|-----------------------------------|  
| productKey      | 是     | 产品key                             |
| deviceKey       | 是     | 设备key                             |
| startTimestamp  | 是     | 开始时间（毫秒时间戳）                       |
| endTimestamp    | 是     | 结束时间（毫秒时间戳）                       |
| attributeCode   | 否     | 物模型属性标识符，查询多个属性时使用英文逗号分隔          |
| gatewayDk       | 否     | 网关设备的 Device Key                  |
| gatewayPk       | 否     | 网关设备的 Product Key                 |
| countType       | 否     | 聚合类型（默认3）：1-最大值 2-最小值 3-平均值 4-差值  |
| timeGranularity | 否     | 统计时间粒度（默认2）：1-月 2-日 3-小时 4-分钟 5-秒 |
| callback        | 是     | 请求回调                              |

**QuecPropertyChartListModel属性定义**

| 字段           | 类型              | 描述      |
|--------------|-----------------|---------|
| propertyCode | String          | 物模型code |
| xaxisData    | List&lt;String> | 记录时间    |
| xaxisDataTs  | List&lt;Double> | 时间戳     |
| yaxisData    | List&lt;Any>    | 属性内容    |

**示例代码**

```kotlin
QuecDeviceStatisticsService.getPropertyChartList(
    "pk",
    "dk",
    System.currentTimeMillis() - 90000,
    System.currentTimeMillis(),
    "temperature",
    null,
    null,
    3,
    1
) {
    if (it.isSuccess) {
        val data = it.data //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 设备属性环比统计数据

**接口说明**

获取设备属性环比统计数据

```kotlin
fun getPropertyStatisticsPath(
    productKey: String,
    deviceKey: String,
    currentTimestamp: Long,
    attributeCode: String,
    gatewayDk: String?,
    gatewayPk: String?,
    countType: Int,
    timeGranularities: String?,
    callback: QuecCallback<List<QuecPropertyStatisticsModel>>
)
```

**参数说明**

| 参数                | 	是否必传 | 说明                                          |	
|-------------------|-------|---------------------------------------------|  
| productKey        | 是     | 产品key                                       |
| deviceKey         | 是     | 设备key                                       |
| currentTimestamp  | 是     | 当前时间（毫秒时间戳）                                 |
| attributeCode     | 否     | 物模型属性标识符，查询多个属性时使用英文逗号分隔                    |
| gatewayDk         | 否     | 网关设备的 Device Key                            |
| gatewayPk         | 否     | 网关设备的 Product Key                           |
| countType         | 否     | 聚合类型（默认3）：1-最大值 2-最小值 3-平均值 4-差值            |
| timeGranularities | 否     | 统计时间粒度，查询多个粒度时使用英文逗号分隔（默认1）：1-日 2-周 3-月 4-年 |
| callback          | 是     | 请求回调                                        |

**QuecPropertyStatisticsModel属性定义**

| 字段              | 类型               | 描述                     |
|-----------------|------------------|------------------------|
| statValue       | ListList&lt;Any> | 当期环比值                  |
| timeGranularity | int              | 时间单位 【1-日，2-周，3-月，4-年】 |

**示例代码**

```kotlin
QuecDeviceStatisticsService.getPropertyStatisticsPath(
    "pk",
    "dk",
    System.currentTimeMillis(),
    "temperature",
    null,
    null,
    3,
    "1,2,3"
) {
    if (it.isSuccess) {
        val data = it.data //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

### 设备属性数据列表

**接口说明**

获取设备属性数据列表

```kotlin
fun getPropertyDataList(
    productKey: String,
    deviceKey: String,
    startTimestamp: Long,
    endTimestamp: Long,
    attributeCode: String,
    gatewayDk: String?,
    gatewayPk: String?,
    pageNumber: Int,
    pageSize: Int,
    callback: QuecCallback<QuecPageResponse<QuecPropertyDataListModel>>
)
```

**参数说明**

| 参数             | 	是否必传 | 说明                       |	
|----------------|-------|--------------------------|  
| productKey     | 是     | 产品key                    |
| deviceKey      | 是     | 设备key                    |
| startTimestamp | 是     | 开始时间（毫秒时间戳）              |
| endTimestamp   | 是     | 结束时间（毫秒时间戳）              |
| attributeCode  | 否     | 物模型属性标识符，查询多个属性时使用英文逗号分隔 |
| gatewayDk      | 否     | 网关设备的 Device Key         |
| gatewayPk      | 否     | 网关设备的 Product Key        |
| pageNumber     | 否     | 当前页，默认为第 1 页             |
| pageSize       | 否     | 页大小，默认为 10 条             |
| callback       | 是     | 请求回调                     |

**示例代码**

```kotlin
QuecDeviceStatisticsService.getPropertyDataList(
    "pk",
    "dk",
    System.currentTimeMillis() - 90000,
    System.currentTimeMillis(),
    "temperature",
    null,
    null,
    1,
    10
) {
    if (it.isSuccess) {
        val data = it.data //请求成功
    } else {
        val code = it.code //请求失败, 错误码
        val msg = it.msg //请求失败, 错误信息
    }
}
```

# 移除家庭成员


**接口地址**:`/v2/family/enduserapi/deleteFamilyMember`


**请求方式**:`DELETE`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:<p>移除家庭成员</p>


**请求参数**:


| 参数名称  | 参数说明       | 请求类型 | 是否必须 | 数据类型 | schema |
| --------- | -------------- | -------- | -------- | -------- | ------ |
| fid       | 家庭ID         | query    | true     | string   |        |
| memberUid | 家庭成员用户ID | query    | true     | string   |        |


**响应状态**:


| 状态码 | 说明                          | schema             |
| ------ | ----------------------------- | ------------------ |
| 200    | 移除家庭成员成功              | 返回注册码响应数据 |
| 5635   | 未开启家居模式                |                    |
| 5636   | 请输入家庭ID                  |                    |
| 5638   | 普通成员没有权限              |                    |
| 5656   | 请输入家庭成员id              |                    |
| 5658   | 成员权限不合法                |                    |
| 5671   | 用户没有该家庭权限            |                    |
| 5679   | 禁止将自己移除家庭            |                    |
| 6054   | 权限不足,被移除者不在该家庭下 |                    |


**响应参数**:


| 参数名称 | 参数说明   | 类型           | schema         |
| -------- | ---------- | -------------- | -------------- |
| code     | 响应状态码 | integer(int32) | integer(int32) |
| data     | 响应数据   | object         |                |
| extMsg   | 扩展消息   | string         |                |
| msg      | 响应消息   | string         |                |


**响应示例**:
```javascript
{
	"code": 0,
	"data": {},
	"extMsg": "",
	"msg": ""
}
```

# 场景SDK:QuecSceneSdk

## 功能概述

SDK支持用户按照一定的规则, 使一个或者多个设备执行多个任务

```kotlin
QuecSceneService
```

## SDK集成方式

> **注意**
>
> 场景SDK运行依赖核心库quec-iot-sdk, 请先按照快速集成文档集成核心库

引入依赖

```groovy
implementation 'com.quectel.app.sdk:quec-scene-sdk:2.14.0'
```

## 场景管理

### 获取场景列表

**接口说明**

获取当前场景的场景列表

```kotlin
fun getSceneList(pageNumber: Int, pageSize: Int, callback: QuecCallback<QuecPageResponse<QuecSceneModel>>)
```

**参数说明**

| 参数         | 是否必传 | 说明               |
|------------|------|------------------|
| pageNumber | 是    | 分页号              |
| pageSize   | 是    | 每一页的大小           |
| callback   | 是    | 请求回调             |

**示例代码**

```kotlin
QuecSceneService.getSceneList(1, 10) {
    if (it.isSuccess) {
        //获取场景列表成功
        val data = it.data  //场景列表数据
    } else {
        //获取场景列表失败
        val code = it.code  //错误码
        val msg = it.msg    //错误信息
    }
}
```

**QuecSceneModel属性定义**

| 字段        | 类型                 | 描述      |
|-----------|--------------------|---------|
| fid       | String             | 家庭id    |
| isCommon  | boolean            | 是否是常用场景 |
| sceneInfo | QuecSceneInfoModel | 场景详情    |

**QuecSceneInfoModel属性定义**

| 字段            | 类型                         | 描述       |
|---------------|----------------------------|----------|
| executeResult | int                        | 上次场景执行结果 |
| icon          | String                     | 场景图标     |
| id            | int                        | 场景执行结果   |
| metaDataList  | List&lt;QuecMetaDataModel> | 场景设备动作列表 |
| name          | String                     | 场景名称     |
| sceneId       | String                     | 场景Id     |

**QuecMetaDataModel属性定义**

| 字段         | 类型                            | 描述                 |
|------------|-------------------------------|--------------------|
| actionList | List&lt;QuecSceneActionModel> | 设备动作列表             |
| deviceKey  | String                        | 设备dk               |
| productKey | String                        | 设备pk               |
| deviceName | String                        | 设备名称               |
| logoImage  | String                        | 设备icon             |
| deviceType | int                           | 设备类型 1 默认普通设备 2 群组 |

**QuecSceneActionModel属性定义**

| 字段       | 类型     | 描述          |
|----------|--------|-------------|
| id       | int    | 物模型功能ID     |
| code     | String | 物模型标志符      |
| dataType | String | 物模型数据类型     |
| actionId | int    | 执行动作Id      |
| name     | String | 物模型功能名称     |
| subName  | String | 物模型值subName |
| type     | String | 物模型功能类型     |
| subType  | String | 物模型subType  |
| subType  | String | 物模型subType  |

### 添加场景

**接口说明**

添加一个新的场景

```kotlin
fun addScene(sceneModel: QuecSceneModel, callback: QuecCallback<Unit>)
```

**参数说明**

| 参数         | 是否必传 | 说明   |
|------------|------|------|
| sceneModel | 是    | 场景属性 |
| callback   | 是    | 请求回调 |

**示例代码**

```kotlin
val model = QuecSceneModel()
val sceneInfoModel = QuecSceneInfoModel()
val metaDataModel = QuecMetaDataModel()
val actionModel = QuecSceneActionModel()

model.isIsCommon = true
sceneInfoModel.name = "设备开灯"
sceneInfoModel.icon = "https://xxx/xxx.png"

actionModel.code = "switch"
actionModel.dataType = "BOOL"
actionModel.id = 1
actionModel.name = "开关"
actionModel.subName = "开灯"
actionModel.subType = "RW"
actionModel.type = "PROPERTY"
actionModel.value = true

metaDataModel.productKey = "pk"
metaDataModel.deviceKey = "dk"
metaDataModel.deviceType = 1

model.sceneInfo = sceneInfoModel
sceneInfoModel.metaDataList = listOf(metaDataModel)
metaDataModel.actionList = listOf(actionModel)

QuecSceneService.addScene(model) {
    if (it.isSuccess) {
        //添加场景成功
    } else {
        //添加场景失败
        val code = it.code  //错误码
        val msg = it.msg    //错误信息
    }
}
```

### 修改场景

**接口说明**

编写场景中的信息

```kotlin
fun editScene(sceneModel: QuecSceneModel, callback: QuecCallback<Unit>)
```

**参数说明**

| 参数         | 是否必传 | 说明   |
|------------|------|------|
| sceneModel | 是    | 场景属性 |
| callback   | 是    | 请求回调 |

**示例代码**

```kotlin
val model = QuecSceneModel() //从场景列表获取到的对象
model.sceneInfo.name = "新名称"
QuecSceneService.editScene(model) {
    if (it.isSuccess) {
        //修改场景成功
    } else {
        //修改场景失败
        val code = it.code  //错误码
        val msg = it.msg    //错误信息
    }
}
```

### 删除场景

**接口说明**

删除指定的场景

```kotlin
fun deleteScene(sceneId: String, callback: QuecCallback<Unit>)
```

**参数说明**

| 参数       | 是否必传 | 说明               |
|----------|------|------------------|
| sceneId  | 是    | 场景id             |
| callback | 是    | 请求回调             |

**示例代码**

```kotlin
QuecSceneService.deleteScene("sceneId") {
    if (it.isSuccess) {
        //删除场景成功
    } else {
        //删除场景失败
        val code = it.code  //错误码
        val msg = it.msg    //错误信息
    }
}
```

### 获取场景详情

**接口说明**

获取指定场景的场景详情

```kotlin
fun getSceneInfo(sceneId: String, callback: QuecCallback<QuecSceneModel>)
```

**参数说明**

| 参数       | 是否必传 | 说明               |
|----------|------|------------------|
| sceneId  | 是    | 场景id             |
| callback | 是    | 请求回调             |

**示例代码**

```kotlin
QuecSceneService.getSceneInfo("sceneId") {
    if (it.isSuccess) {
        //获取场景详情成功
    } else {
        //获取场景详情失败
        val code = it.code  //错误码
        val msg = it.msg    //错误信息
    }
}
```

## 场景执行

### 执行场景

**接口说明**

执行指定的场景

```kotlin
fun executeScene(sceneId: String, callback: QuecCallback<QuecActionExecuteResultModel>)
```

**参数说明**

| 参数       | 是否必传 | 说明               |
|----------|------|------------------|
| sceneId  | 是    | 场景id             |
| callback | 是    | 请求回调             |

**示例代码**

```kotlin
QuecSceneService.executeScene("sceneId") {
    if (it.isSuccess) {
        //执行场景成功
    } else {
        //执行场景失败
        val code = it.code  //错误码
        val msg = it.msg    //错误信息
    }
}
```

**QuecActionExecuteResultModel属性定义**

| 字段             | 类型                           | 描述     |
|----------------|------------------------------|--------|
| executeResult  | boolean                      | 执行结果   |
| failActionList | List&lt;QuecFailActionModel> | 失败结果列表 |
| failCount      | int                          | 失败结果数  |
| successCount   | int                          | 成功结果数  |

**QuecFailActionModelModel属性定义**

| 字段         | 类型     | 描述     |
|------------|--------|--------|
| productKey | String | 设备pk   |
| deviceKey  | String | 设备dk   |
| deviceName | String | 设备名    |
| imageLogo  | String | 设备logo |
| reason     | String | 原因     |

### 测试场景

**接口说明**

测试场景配置的执行情况, 在未保存场景状态到云端的情况下, 也可执行

```kotlin
fun executeTestScene(sceneInfoModel: QuecSceneModel, callback: QuecCallback<QuecActionExecuteResultModel>)
```

**参数说明**

| 参数         | 是否必传 | 说明   |
|------------|------|------|
| sceneModel | 是    | 场景属性 |
| callback   | 是    | 请求回调 |

**示例代码**

```kotlin
val model = QuecSceneModel() //从场景列表获取到的对象
QuecSceneService.executeTestScene(model) {
    if (it.isSuccess) {
        //执行场景测试成功
    } else {
        //执行场景测试失败
        val code = it.code  //错误码
        val msg = it.msg    //错误信息
    }
}
```

## 常用场景

### 获取常用场景列表

**接口说明**

获取常用场景列表

```kotlin
fun getCommonSceneList(pageNumber: Int, pageSize: Int, callback: QuecCallback<QuecPageResponse<QuecSceneModel>>)
```

**参数说明**

| 参数         | 是否必传 | 说明               |
|------------|------|------------------|
| pageNumber | 是    | 分页号              |
| pageSize   | 是    | 每一页的大小           |
| callback   | 是    | 请求回调             |

**示例代码**

```kotlin
QuecSceneService.getCommonSceneList(1, 10) {
    if (it.isSuccess) {
        //获取常用场景列表成功
    } else {
        //获取常用场景列表失败
        val code = it.code  //错误码
        val msg = it.msg    //错误信息
    }
}
```

### 批量添加常用场景

**接口说明**

批量添加常用场景

```kotlin
fun batchAddCommonScene(sceneIdList: List<String>, callback: QuecCallback<QuecAddCommonSceneIdModel>)
```

**参数说明**

| 参数          | 是否必传 | 说明               |
|-------------|------|------------------|
| sceneIdList | 是    | 场景id列表，必传        |
| callback    | 是    | 请求回调             |

**示例代码**

```kotlin
QuecSceneService.batchAddCommonScene(listOf("sceneId1", "sceneId2")) {
    if (it.isSuccess) {
        //添加常用场景成功
    } else {
        //添加常用场景失败
        val code = it.code  //错误码
        val msg = it.msg    //错误信息
    }
}
```

### 批量删除常用场景

**接口说明**

批量删除常用场景

```kotlin
fun batchDeleteCommonScene(sceneIdList: List<String>, callback: QuecCallback<QuecAddCommonSceneIdModel>)
```

**参数说明**

| 参数          | 是否必传 | 说明               |
|-------------|------|------------------|
| sceneIdList | 是    | 场景id列表，必传        |
| callback    | 是    | 请求回调             |

**示例代码**

```kotlin
QuecSceneService.batchDeleteCommonScene(listOf("sceneId1", "sceneId2")) {
    if (it.isSuccess) {
        //删除常用场景成功
    } else {
        //删除常用场景失败
        val code = it.code  //错误码
        val msg = it.msg    //错误信息
    }
}
```

## 场景日志

### 获取场景日志列表

**接口说明**

获取场景日志列表

```kotlin
fun getSceneLogList(lastExecutionId: Long, limit: Int, callback: QuecCallback<List<QuecSceneLogItemModel>>)
```

**参数说明**

| 参数              | 是否必传 | 说明               |
|-----------------|------|------------------|
| lastExecutionId | 否    | 最后一条执行日志的id      |
| limit           | 是    | 查询的数据数量          |
| callback        | 是    | 请求回调             |

**示例代码**

```kotlin
QuecSceneService.getSceneLogList(80, 10) {
    if (it.isSuccess) {
        //查询日志列表成功
        val data = it.data //列表数据
    } else {
        //查询日志列表失败
        val code = it.code  //错误码
        val msg = it.msg    //错误信息
    }
}
```

**QuecSceneLogItemModel属性定义**

| 字段              | 类型                                  | 描述     |
|-----------------|-------------------------------------|--------|
| sceneId         | String                              | 场景ID   |
| sceneName       | String                              | 场景名称   |
| sceneIcon       | String                              | 场景icon |
| executionId     | long                                | 执行id   |
| executionTime   | long                                | 执行时间   |
| executionResult | int                                 | 执行结果   |
| executionList   | List&lt;QuecSceneLogExecutionModel> | 执行结果列表 |

**QuecSceneLogExecutionModel属性定义**

| 字段               | 类型                               | 描述     |
|------------------|----------------------------------|--------|
| productKey       | String                           | 设备pk   |
| deviceKey        | String                           | 设备dk   |
| deviceName       | String                           | 设备名称   |
| logoImage        | String                           | 设备logo |
| executionResult  | String                           | 执行结果   |
| actionResultList | List&lt;QuecSceneLogActionModel> | 场景ID   |

**QuecSceneLogActionModel属性定义**

| 字段         | 类型                   | 描述   |
|------------|----------------------|------|
| result     | boolean              | 执行结果 |
| reason     | String               | 原因   |
| createTime | String               | 时间戳  |
| action     | QuecSceneActionModel | 物模型  |

### 获取场景日志详情

**接口说明**

获取场景日志详情

```kotlin
fun getSceneLogDetailInfo(executionId: Long, callback: QuecCallback<QuecSceneLogItemModel>)
```

**参数说明**

| 参数          | 是否必传 | 说明     |
|-------------|------|--------|
| executionId | 是    | 场景日志id |
| callback    | 是    | 请求回调   |

**示例代码**

```kotlin
QuecSceneService.getSceneLogDetailInfo(1000) {
    if (it.isSuccess) {
        //查询日志详情成功
        val data = it.data //详情数据
    } else {
        //查询日志详情失败
        val code = it.code  //错误码
        val msg = it.msg    //错误信息
    }
}
```

### 清除场景日志

**接口说明**

清除场景日志

```kotlin
fun clearSceneLog(callback: QuecCallback<Unit>)
```

**参数说明**

| 参数       | 是否必传 | 说明   |
|----------|------|------|
| callback | 是    | 请求回调 |

**示例代码**

```kotlin
QuecSceneService.clearSceneLog() {
    if (it.isSuccess) {
        //清除日志成功
    } else {
        //清除日志失败
        val code = it.code  //错误码
        val msg = it.msg    //错误信息
    }
}
```

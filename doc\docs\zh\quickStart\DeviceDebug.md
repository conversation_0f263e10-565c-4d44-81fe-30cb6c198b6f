# 设备通信调试

本文档以RGB灯产品为例，详细阐述如何利用 AT 命令进行数据业务交互。

## 一、数据上行

设备成功接入开发者中心后，MCU 可通过 **AT+QIOTMODELTD** 命令发送物模型数据到云平台。在执行 **AT+QIOTMODELTD** 命令并等待设备返回 **>** 提示符之后，再向设备发送按照规范组成的物模型数据。JSON 格式的物模型数据格式为：**{"\<ID1>":\<value1>,"\<ID2>":\<value2>,…,"\<IDn>",\<valuen>}**。 **\<ID>**为物模型 ID，**\<value>**为物模型值。

以发送如下物模型为例： 

⚫物模型 ID：1；类型：BOOL；功能名称：开关；内容：True。

⚫ 物模型 ID：2；类型：INT；功能名称：颜色R；内容：100。

⚫ 物模型 ID：3；类型：INT；功能名称：颜色G；内容：150。

⚫ 物模型 ID：4；类型：INT；功能名称：颜色B；内容：200。

⚫ 物模型 ID：5；类型：INT；功能名称：亮度；内容：100。

⚫ 物模型 ID：6；类型：INT；功能名称：延时关灯时间；内容：60。

```Plain
[TX]AT+QIOTMODELTD=1,19 

[RX]> 
[TX]{"1":true,"2":100,"3":150,"4":200,"5":100,"6":60} 
[RX]OK 
[RX]+QIOTEVT: 4,10210
```

**备注** 

若设备上报物模型失败，可通过以下方式进行排查： 

1. 通过 **AT+QIOTSTATE?** 命令查询平台连接状态，若响应值等于 8，则平台连接正常。 
2. 详细事件码查询链接：**开发者中心 > 文档中心 > 设备开发 >回调事件码**。

## 二、数据下行

打开的 设备运维 -> 设备调试 ，模拟远程下发控制命令场景，在设备调试中选择需要下发的物模型数据，最后单击 发送指令 。

<a data-fancybox title="img" href="/zh/quickStart/devicedebug01.png" >![img](/zh/quickStart/devicedebug01.png)</a>

以发送如下物模型为例： 

⚫ 物模型 ID：1；类型：Bool；功能名称：开关；内容：false。

在 非缓存模式 中，当终端设备接收到平台下发的数据，直接向 MCU 打印数据，例如，平台下发服务后。串口工具显示的结果如下所示。

```Plain
[RX]+QIOTEVT: 5,10200,11
[RX]{"1":false}
```

交互数据解析(事件详情请参考：[设备与平台交互事件码](https://iot-cloud-docs.quectelcn.com/deviceDevelop/DeviceAccessPlan/cellular/AT/InteractionEvent.html))

- +QIOTEVT: 5,10200,11 ：终端设备接收到物模型数据后，向 MCU 下发的事件通知。
  - 5 ：事件类型，表示着接收到下行数据； 
  - 10210 ：事件Code，代表收到物模型下发数据； 
  - 11 ：接收到下发数据的长度；
- {"1":false} ：接收到平台下发的物模型服务Json格式数据。

